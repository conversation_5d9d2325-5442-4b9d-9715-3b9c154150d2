#!/usr/bin/env python3
"""
P6和值预测器训练脚本

支持命令行参数，支持单模型和全模型训练
提供详细的训练日志和性能报告

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import sys
import os
import argparse
import logging
import json
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.predictors.sum_predictor import SumPredictor

def setup_logging(log_level: str = "INFO", log_file: str = None):
    """设置日志配置"""
    log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # 配置根日志器
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format=log_format,
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    # 如果指定了日志文件，添加文件处理器
    if log_file:
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(logging.Formatter(log_format))
        logging.getLogger().addHandler(file_handler)

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="P6和值预测器训练脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 训练所有模型
  python train_sum_predictor.py
  
  # 只训练XGBoost模型
  python train_sum_predictor.py --model xgb
  
  # 使用自定义配置文件
  python train_sum_predictor.py --config config/custom_sum_config.yaml
  
  # 保存模型到指定路径
  python train_sum_predictor.py --save-path models/sum_predictor_20250114
  
  # 启用详细日志
  python train_sum_predictor.py --log-level DEBUG --log-file logs/training.log
        """
    )
    
    parser.add_argument(
        "--model", "-m",
        type=str,
        choices=["xgb", "lgb", "lstm", "distribution", "constraint", "ensemble", "all"],
        default="all",
        help="要训练的模型类型 (默认: all)"
    )
    
    parser.add_argument(
        "--config", "-c",
        type=str,
        default="config/sum_predictor_config.yaml",
        help="配置文件路径 (默认: config/sum_predictor_config.yaml)"
    )
    
    parser.add_argument(
        "--db-path", "-d",
        type=str,
        default="data/lottery.db",
        help="数据库文件路径 (默认: data/lottery.db)"
    )
    
    parser.add_argument(
        "--save-path", "-s",
        type=str,
        default=None,
        help="模型保存路径 (默认: models/sum_predictor/YYYYMMDD_HHMMSS)"
    )
    
    parser.add_argument(
        "--cross-validation", "-cv",
        action="store_true",
        help="执行交叉验证"
    )
    
    parser.add_argument(
        "--cv-folds",
        type=int,
        default=5,
        help="交叉验证折数 (默认: 5)"
    )
    
    parser.add_argument(
        "--log-level",
        type=str,
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="日志级别 (默认: INFO)"
    )
    
    parser.add_argument(
        "--log-file",
        type=str,
        default=None,
        help="日志文件路径 (可选)"
    )
    
    parser.add_argument(
        "--no-save",
        action="store_true",
        help="不保存训练后的模型"
    )
    
    parser.add_argument(
        "--evaluate",
        action="store_true",
        help="训练后立即评估模型性能"
    )
    
    return parser.parse_args()

def train_model(predictor: SumPredictor, model_name: str, args) -> dict:
    """训练指定模型"""
    logger = logging.getLogger("train_model")
    
    try:
        logger.info(f"开始训练模型: {model_name}")
        start_time = datetime.now()
        
        # 训练模型
        if model_name == "all":
            performance = predictor.train()
        else:
            performance = predictor.train(model_name)
        
        end_time = datetime.now()
        training_time = (end_time - start_time).total_seconds()
        
        logger.info(f"模型 {model_name} 训练完成，耗时: {training_time:.2f}秒")
        
        # 添加训练时间到性能指标
        if isinstance(performance, dict):
            for model_perf in performance.values():
                if isinstance(model_perf, dict):
                    model_perf['training_time'] = training_time
        
        return performance
        
    except Exception as e:
        logger.error(f"训练模型 {model_name} 失败: {e}")
        raise

def perform_cross_validation(predictor: SumPredictor, args) -> dict:
    """执行交叉验证"""
    logger = logging.getLogger("cross_validation")
    
    try:
        logger.info(f"开始 {args.cv_folds} 折交叉验证")
        
        # 加载数据
        X, y = predictor.models['xgb'].load_data()  # 使用XGB模型的数据加载方法
        
        # 执行交叉验证
        cv_results = predictor.cross_validate(X, y, args.cv_folds)
        
        logger.info("交叉验证完成")
        return cv_results
        
    except Exception as e:
        logger.error(f"交叉验证失败: {e}")
        raise

def save_training_results(performance: dict, cv_results: dict, args):
    """保存训练结果"""
    logger = logging.getLogger("save_results")
    
    try:
        # 确定保存路径
        if args.save_path:
            results_dir = Path(args.save_path)
        else:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            results_dir = Path(f"models/sum_predictor/{timestamp}")
        
        results_dir.mkdir(parents=True, exist_ok=True)
        
        # 保存训练性能
        performance_file = results_dir / "training_performance.json"
        with open(performance_file, 'w', encoding='utf-8') as f:
            json.dump(performance, f, indent=2, ensure_ascii=False, default=str)
        
        # 保存交叉验证结果
        if cv_results:
            cv_file = results_dir / "cross_validation_results.json"
            with open(cv_file, 'w', encoding='utf-8') as f:
                json.dump(cv_results, f, indent=2, ensure_ascii=False, default=str)
        
        # 保存训练配置
        config_file = results_dir / "training_config.json"
        config_data = {
            'model': args.model,
            'config_path': args.config,
            'db_path': args.db_path,
            'cross_validation': args.cross_validation,
            'cv_folds': args.cv_folds,
            'training_time': datetime.now().isoformat()
        }
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"训练结果已保存到: {results_dir}")
        return str(results_dir)
        
    except Exception as e:
        logger.error(f"保存训练结果失败: {e}")
        raise

def print_performance_summary(performance: dict, cv_results: dict = None):
    """打印性能摘要"""
    print("\n" + "="*60)
    print("训练性能摘要")
    print("="*60)
    
    if isinstance(performance, dict):
        for model_name, perf in performance.items():
            if isinstance(perf, dict) and 'error' not in perf:
                print(f"\n{model_name.upper()} 模型:")
                print(f"  准确率: {perf.get('accuracy', 0):.4f}")
                print(f"  MAE: {perf.get('mae', 0):.4f}")
                print(f"  RMSE: {perf.get('rmse', 0):.4f}")
                print(f"  R²分数: {perf.get('r2_score', 0):.4f}")
                if 'training_time' in perf:
                    print(f"  训练时间: {perf['training_time']:.2f}秒")
            elif 'error' in perf:
                print(f"\n{model_name.upper()} 模型: 训练失败 - {perf['error']}")
    
    if cv_results:
        print("\n" + "-"*40)
        print("交叉验证结果")
        print("-"*40)
        
        for model_name, cv_perf in cv_results.items():
            if isinstance(cv_perf, dict) and 'error' not in cv_perf:
                print(f"\n{model_name.upper()} 模型:")
                print(f"  平均准确率: {cv_perf.get('avg_accuracy', 0):.4f} ± {cv_perf.get('std_accuracy', 0):.4f}")
                print(f"  平均MAE: {cv_perf.get('avg_mae', 0):.4f} ± {cv_perf.get('std_mae', 0):.4f}")

def main():
    """主函数"""
    args = parse_arguments()
    
    # 设置日志
    setup_logging(args.log_level, args.log_file)
    logger = logging.getLogger("main")
    
    try:
        logger.info("开始P6和值预测器训练")
        logger.info(f"参数: {vars(args)}")
        
        # 检查数据库文件
        if not Path(args.db_path).exists():
            logger.error(f"数据库文件不存在: {args.db_path}")
            return 1
        
        # 初始化预测器
        logger.info("初始化和值预测器")
        predictor = SumPredictor(args.db_path, args.config)
        
        # 训练模型
        performance = train_model(predictor, args.model, args)
        
        # 交叉验证
        cv_results = {}
        if args.cross_validation:
            cv_results = perform_cross_validation(predictor, args)
        
        # 保存模型
        if not args.no_save:
            save_path = save_training_results(performance, cv_results, args)
            
            # 保存训练后的模型
            logger.info("保存训练后的模型")
            if args.model == "all":
                predictor.save_model(filepath=save_path)
            else:
                predictor.save_model(args.model, save_path)
        
        # 打印性能摘要
        print_performance_summary(performance, cv_results)
        
        logger.info("训练完成")
        return 0
        
    except Exception as e:
        logger.error(f"训练过程发生错误: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
