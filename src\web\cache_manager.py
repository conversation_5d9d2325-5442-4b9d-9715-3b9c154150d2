# P10-Web界面系统 缓存管理器
# 提供API响应缓存、数据库查询缓存和性能优化

import time
import json
import hashlib
from typing import Any, Dict, Optional, Callable, Union
from datetime import datetime, timedelta
import asyncio
from functools import wraps
import logging

logger = logging.getLogger(__name__)

class CacheItem:
    """缓存项"""
    def __init__(self, data: Any, ttl: int):
        self.data = data
        self.created_at = time.time()
        self.ttl = ttl
        self.access_count = 0
        self.last_access = time.time()
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        return time.time() - self.created_at > self.ttl
    
    def access(self) -> Any:
        """访问数据，更新统计信息"""
        self.access_count += 1
        self.last_access = time.time()
        return self.data

class MemoryCache:
    """内存缓存管理器"""
    
    def __init__(self, max_size: int = 1000, default_ttl: int = 300):
        self.cache: Dict[str, CacheItem] = {}
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.hits = 0
        self.misses = 0
        self.evictions = 0
    
    def _generate_key(self, key: Union[str, tuple, dict]) -> str:
        """生成缓存键"""
        if isinstance(key, str):
            return key
        elif isinstance(key, (tuple, list)):
            return hashlib.md5(str(key).encode()).hexdigest()
        elif isinstance(key, dict):
            sorted_items = sorted(key.items())
            return hashlib.md5(str(sorted_items).encode()).hexdigest()
        else:
            return hashlib.md5(str(key).encode()).hexdigest()
    
    def get(self, key: Union[str, tuple, dict]) -> Optional[Any]:
        """获取缓存数据"""
        cache_key = self._generate_key(key)
        
        if cache_key in self.cache:
            item = self.cache[cache_key]
            
            if item.is_expired():
                del self.cache[cache_key]
                self.misses += 1
                return None
            
            self.hits += 1
            return item.access()
        
        self.misses += 1
        return None
    
    def set(self, key: Union[str, tuple, dict], data: Any, ttl: Optional[int] = None) -> None:
        """设置缓存数据"""
        cache_key = self._generate_key(key)
        ttl = ttl or self.default_ttl
        
        # 如果缓存已满，执行LRU清理
        if len(self.cache) >= self.max_size and cache_key not in self.cache:
            self._evict_lru()
        
        self.cache[cache_key] = CacheItem(data, ttl)
    
    def delete(self, key: Union[str, tuple, dict]) -> bool:
        """删除缓存项"""
        cache_key = self._generate_key(key)
        if cache_key in self.cache:
            del self.cache[cache_key]
            return True
        return False
    
    def clear(self) -> None:
        """清空缓存"""
        self.cache.clear()
        self.hits = 0
        self.misses = 0
        self.evictions = 0
    
    def _evict_lru(self) -> None:
        """LRU清理策略"""
        if not self.cache:
            return
        
        # 找到最久未访问的项
        oldest_key = min(
            self.cache.keys(),
            key=lambda k: self.cache[k].last_access
        )
        
        del self.cache[oldest_key]
        self.evictions += 1
    
    def cleanup_expired(self) -> int:
        """清理过期项"""
        expired_keys = [
            key for key, item in self.cache.items()
            if item.is_expired()
        ]
        
        for key in expired_keys:
            del self.cache[key]
        
        return len(expired_keys)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        total_requests = self.hits + self.misses
        hit_rate = (self.hits / total_requests * 100) if total_requests > 0 else 0
        
        return {
            "size": len(self.cache),
            "max_size": self.max_size,
            "hits": self.hits,
            "misses": self.misses,
            "hit_rate": round(hit_rate, 2),
            "evictions": self.evictions,
            "memory_usage": sum(
                len(str(item.data)) for item in self.cache.values()
            )
        }

# 全局缓存实例
cache = MemoryCache(max_size=2000, default_ttl=300)  # 5分钟默认TTL

def cache_response(ttl: int = 300, key_func: Optional[Callable] = None):
    """API响应缓存装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 生成缓存键
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                # 默认使用函数名和参数生成键
                cache_key = f"{func.__name__}:{hash(str(args) + str(sorted(kwargs.items())))}"
            
            # 尝试从缓存获取
            cached_result = cache.get(cache_key)
            if cached_result is not None:
                logger.debug(f"缓存命中: {cache_key}")
                return cached_result
            
            # 执行函数并缓存结果
            result = await func(*args, **kwargs)
            cache.set(cache_key, result, ttl)
            logger.debug(f"缓存设置: {cache_key}")
            
            return result
        
        return wrapper
    return decorator

def cache_database_query(ttl: int = 600):
    """数据库查询缓存装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 生成查询缓存键
            cache_key = f"db:{func.__name__}:{hash(str(args) + str(sorted(kwargs.items())))}"
            
            # 尝试从缓存获取
            cached_result = cache.get(cache_key)
            if cached_result is not None:
                logger.debug(f"数据库缓存命中: {cache_key}")
                return cached_result
            
            # 执行查询并缓存结果
            result = func(*args, **kwargs)
            cache.set(cache_key, result, ttl)
            logger.debug(f"数据库缓存设置: {cache_key}")
            
            return result
        
        return wrapper
    return decorator

class CacheManager:
    """缓存管理器"""
    
    def __init__(self):
        self.cache = cache
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return self.cache.get_stats()
    
    def clear_cache(self, pattern: Optional[str] = None) -> int:
        """清理缓存"""
        if pattern is None:
            count = len(self.cache.cache)
            self.cache.clear()
            return count
        
        # 按模式清理
        keys_to_delete = [
            key for key in self.cache.cache.keys()
            if pattern in key
        ]
        
        for key in keys_to_delete:
            del self.cache.cache[key]
        
        return len(keys_to_delete)
    
    def cleanup_expired(self) -> int:
        """清理过期缓存"""
        return self.cache.cleanup_expired()
    
    def preload_cache(self, data_loaders: Dict[str, Callable]) -> None:
        """预加载缓存"""
        for key, loader in data_loaders.items():
            try:
                data = loader()
                self.cache.set(key, data)
                logger.info(f"预加载缓存成功: {key}")
            except Exception as e:
                logger.error(f"预加载缓存失败 {key}: {e}")
    
    async def warm_up_cache(self) -> None:
        """缓存预热"""
        try:
            # 预加载常用数据
            preload_tasks = {
                "system_status": self._load_system_status,
                "prediction_stats": self._load_prediction_stats,
                "performance_metrics": self._load_performance_metrics
            }
            
            for key, loader in preload_tasks.items():
                try:
                    data = await loader()
                    self.cache.set(f"warmup:{key}", data, ttl=600)
                    logger.info(f"缓存预热成功: {key}")
                except Exception as e:
                    logger.warning(f"缓存预热失败 {key}: {e}")
                    
        except Exception as e:
            logger.error(f"缓存预热过程失败: {e}")
    
    async def _load_system_status(self) -> Dict[str, Any]:
        """加载系统状态数据"""
        # 这里应该调用实际的系统状态获取函数
        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "components": ["api", "database", "websocket"]
        }
    
    async def _load_prediction_stats(self) -> Dict[str, Any]:
        """加载预测统计数据"""
        # 这里应该调用实际的预测统计获取函数
        return {
            "total_predictions": 1000,
            "accuracy_rate": 0.85,
            "last_update": datetime.now().isoformat()
        }
    
    async def _load_performance_metrics(self) -> Dict[str, Any]:
        """加载性能指标数据"""
        # 这里应该调用实际的性能指标获取函数
        return {
            "response_time": 150.5,
            "throughput": 100,
            "error_rate": 0.05,
            "timestamp": datetime.now().isoformat()
        }

# 全局缓存管理器实例
cache_manager = CacheManager()

# 定期清理任务
async def periodic_cleanup():
    """定期清理过期缓存"""
    while True:
        try:
            expired_count = cache_manager.cleanup_expired()
            if expired_count > 0:
                logger.info(f"清理了 {expired_count} 个过期缓存项")
            
            # 每5分钟清理一次
            await asyncio.sleep(300)
            
        except Exception as e:
            logger.error(f"定期清理任务失败: {e}")
            await asyncio.sleep(60)  # 出错时等待1分钟后重试
