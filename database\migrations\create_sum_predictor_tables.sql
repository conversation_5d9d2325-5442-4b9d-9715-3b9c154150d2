-- P6和值预测器数据库表创建脚本
-- 创建日期: 2025-01-14
-- 描述: 为P6和值预测器创建4个专用数据库表

-- 和值预测结果表（标准表结构+专属字段）
CREATE TABLE IF NOT EXISTS sum_predictions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    issue TEXT NOT NULL,
    model_type TEXT NOT NULL,           -- xgb/lgb/lstm/distribution/constraint/ensemble
    predicted_digit REAL NOT NULL,     -- 统一字段名：预测和值（0-27）
    confidence REAL NOT NULL,          -- 预测置信度
    probabilities TEXT,                 -- JSON格式：和值概率分布（专属）

    -- 和值专属字段
    prediction_range_min INTEGER,      -- 预测范围最小值
    prediction_range_max INTEGER,      -- 预测范围最大值
    distribution_entropy REAL,         -- 分布熵值（专属特征）
    constraint_score REAL,             -- 约束一致性分数（专属特征）

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 索引优化
    UNIQUE(issue, model_type)
);

-- 和值模型性能表（标准表结构+专属指标）
CREATE TABLE IF NOT EXISTS sum_model_performance (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    model_type TEXT NOT NULL,
    evaluation_period TEXT NOT NULL,
    accuracy REAL NOT NULL,             -- 标准字段：主要准确率指标

    -- 回归专属性能指标
    mae REAL NOT NULL,                  -- 平均绝对误差（专属）
    rmse REAL NOT NULL,                 -- 均方根误差（专属）
    accuracy_1 REAL NOT NULL,           -- ±1准确率（专属）
    accuracy_2 REAL NOT NULL,           -- ±2准确率（专属）
    r2_score REAL NOT NULL,             -- R²分数（专属）
    distribution_accuracy REAL,         -- 分布预测准确率（专属）

    -- 标准性能字段
    avg_confidence REAL,                -- 平均置信度
    training_time REAL,                 -- 训练时间
    prediction_time REAL,               -- 预测时间
    model_size INTEGER,                 -- 模型大小

    evaluated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 和值分布统计表（专属特征表）
CREATE TABLE IF NOT EXISTS sum_distribution_stats (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    sum_value INTEGER NOT NULL,         -- 和值（0-27）
    frequency INTEGER NOT NULL,         -- 出现频次
    probability REAL NOT NULL,          -- 概率
    avg_span REAL,                      -- 该和值的平均跨度
    common_patterns TEXT,               -- 常见组合模式(JSON)
    seasonal_frequency TEXT,            -- 季节性频次(JSON)
    correlation_with_positions TEXT,    -- 与位置预测的相关性(JSON)
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 索引和约束
    UNIQUE(sum_value),
    CHECK(sum_value >= 0 AND sum_value <= 27),
    CHECK(probability >= 0 AND probability <= 1)
);

-- 和值约束规则表（专属特征表）
CREATE TABLE IF NOT EXISTS sum_constraint_rules (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    rule_name TEXT NOT NULL,
    rule_description TEXT NOT NULL,
    rule_type TEXT NOT NULL,            -- 规则类型：position/sum/span/pattern

    -- 约束条件
    min_sum INTEGER,                    -- 最小和值
    max_sum INTEGER,                    -- 最大和值
    span_constraint TEXT,               -- 跨度约束条件(JSON)
    pattern_constraint TEXT,            -- 模式约束条件(JSON)
    position_constraint TEXT,           -- 位置约束条件(JSON)

    -- 规则配置
    weight REAL DEFAULT 1.0,            -- 规则权重
    priority INTEGER DEFAULT 1,         -- 规则优先级
    is_active BOOLEAN DEFAULT TRUE,     -- 是否启用

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 约束检查
    CHECK(weight >= 0 AND weight <= 1),
    CHECK(priority >= 1),
    CHECK(min_sum >= 0 AND max_sum <= 27),
    CHECK(min_sum <= max_sum)
);

-- 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_sum_predictions_issue ON sum_predictions(issue);
CREATE INDEX IF NOT EXISTS idx_sum_predictions_model_type ON sum_predictions(model_type);
CREATE INDEX IF NOT EXISTS idx_sum_predictions_created_at ON sum_predictions(created_at);

CREATE INDEX IF NOT EXISTS idx_sum_performance_model_type ON sum_model_performance(model_type);
CREATE INDEX IF NOT EXISTS idx_sum_performance_period ON sum_model_performance(evaluation_period);

CREATE INDEX IF NOT EXISTS idx_sum_distribution_value ON sum_distribution_stats(sum_value);
CREATE INDEX IF NOT EXISTS idx_sum_distribution_probability ON sum_distribution_stats(probability DESC);

CREATE INDEX IF NOT EXISTS idx_sum_rules_type ON sum_constraint_rules(rule_type);
CREATE INDEX IF NOT EXISTS idx_sum_rules_active ON sum_constraint_rules(is_active);
CREATE INDEX IF NOT EXISTS idx_sum_rules_priority ON sum_constraint_rules(priority DESC);
