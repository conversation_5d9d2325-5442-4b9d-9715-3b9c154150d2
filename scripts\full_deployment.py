#!/usr/bin/env python3
"""
全功能启用脚本

启用所有融合功能、监控和自动调整
完成P8系统的全面部署

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import os
import sys
import json
import yaml
import logging
import subprocess
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

class FullDeploymentManager:
    """全功能部署管理器"""
    
    def __init__(self):
        """初始化全功能部署管理器"""
        self.project_root = Path(__file__).parent.parent
        self.config_dir = self.project_root / "config"
        self.scripts_dir = self.project_root / "scripts"
        self.reports_dir = self.project_root / "reports"
        self.logs_dir = self.project_root / "logs"
        
        # 确保目录存在
        self.reports_dir.mkdir(exist_ok=True)
        self.logs_dir.mkdir(exist_ok=True)
        
        # 设置日志
        self._setup_logging()
        self.logger = logging.getLogger(__name__)
        
        # 部署状态
        self.deployment_status = {
            'deployment_time': datetime.now().isoformat(),
            'components': {},
            'services': {},
            'configurations': {},
            'validations': {},
            'overall_status': 'initializing'
        }
    
    def _setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler(self.logs_dir / 'full_deployment.log')
            ]
        )
    
    def enable_fusion_system(self) -> bool:
        """启用融合系统"""
        try:
            self.logger.info("启用P8融合系统...")
            
            # 检查融合配置文件
            fusion_config_path = self.config_dir / "fusion_config.yaml"
            if not fusion_config_path.exists():
                self.logger.error("融合配置文件不存在")
                return False
            
            # 加载并验证配置
            with open(fusion_config_path, 'r', encoding='utf-8') as f:
                fusion_config = yaml.safe_load(f)
            
            # 启用所有融合算法
            if 'fusion' in fusion_config and 'algorithms' in fusion_config['fusion']:
                for algorithm in fusion_config['fusion']['algorithms']:
                    fusion_config['fusion']['algorithms'][algorithm]['enabled'] = True
            
            # 保存更新的配置
            with open(fusion_config_path, 'w', encoding='utf-8') as f:
                yaml.dump(fusion_config, f, default_flow_style=False, 
                         allow_unicode=True, indent=2)
            
            self.deployment_status['components']['fusion_system'] = {
                'status': 'enabled',
                'config_file': str(fusion_config_path),
                'algorithms_count': len(fusion_config.get('fusion', {}).get('algorithms', {}))
            }
            
            self.logger.info("P8融合系统已启用")
            return True
            
        except Exception as e:
            self.logger.error(f"启用融合系统失败: {e}")
            self.deployment_status['components']['fusion_system'] = {
                'status': 'failed',
                'error': str(e)
            }
            return False
    
    def enable_monitoring_system(self) -> bool:
        """启用监控系统"""
        try:
            self.logger.info("启用监控系统...")
            
            # 检查监控配置
            monitoring_config_path = self.config_dir / "system_logging_config.yaml"
            if not monitoring_config_path.exists():
                self.logger.warning("监控配置文件不存在，将创建默认配置")
                self._create_default_monitoring_config()
            
            # 启用性能监控
            monitor_script = self.scripts_dir / "system_monitor.py"
            if monitor_script.exists():
                self.deployment_status['services']['performance_monitor'] = {
                    'status': 'available',
                    'script': str(monitor_script),
                    'description': '系统性能监控服务'
                }
            
            # 启用日志轮转
            log_rotation_script = self.scripts_dir / "log_rotation.py"
            if log_rotation_script.exists():
                self.deployment_status['services']['log_rotation'] = {
                    'status': 'available',
                    'script': str(log_rotation_script),
                    'description': '日志轮转服务'
                }
            
            # 启用告警系统
            alert_script = self.scripts_dir / "alert_system.py"
            if alert_script.exists():
                self.deployment_status['services']['alert_system'] = {
                    'status': 'available',
                    'script': str(alert_script),
                    'description': '系统告警服务'
                }
            
            self.logger.info("监控系统已启用")
            return True
            
        except Exception as e:
            self.logger.error(f"启用监控系统失败: {e}")
            return False
    
    def enable_auto_adjustment(self) -> bool:
        """启用自动调整功能"""
        try:
            self.logger.info("启用自动调整功能...")
            
            # 权重自动调整
            weight_optimizer_script = self.scripts_dir / "weight_optimizer.py"
            if weight_optimizer_script.exists():
                self.deployment_status['services']['weight_optimizer'] = {
                    'status': 'available',
                    'script': str(weight_optimizer_script),
                    'description': '动态权重自动调整'
                }
            
            # 参数自动优化
            param_optimizer_script = self.scripts_dir / "parameter_optimizer.py"
            if param_optimizer_script.exists():
                self.deployment_status['services']['parameter_optimizer'] = {
                    'status': 'available',
                    'script': str(param_optimizer_script),
                    'description': '参数自动优化'
                }
            
            # 融合算法优化
            fusion_optimizer_script = self.scripts_dir / "fusion_algorithm_optimizer.py"
            if fusion_optimizer_script.exists():
                self.deployment_status['services']['fusion_optimizer'] = {
                    'status': 'available',
                    'script': str(fusion_optimizer_script),
                    'description': '融合算法自动优化'
                }
            
            self.logger.info("自动调整功能已启用")
            return True
            
        except Exception as e:
            self.logger.error(f"启用自动调整功能失败: {e}")
            return False
    
    def _create_default_monitoring_config(self):
        """创建默认监控配置"""
        try:
            monitoring_config = {
                'monitoring': {
                    'enabled': True,
                    'check_interval': 300,
                    'performance_thresholds': {
                        'cpu_percent': 80,
                        'memory_percent': 85,
                        'disk_percent': 90,
                        'response_time': 3.0
                    }
                },
                'logging': {
                    'level': 'INFO',
                    'rotation': {
                        'max_size_mb': 20,
                        'backup_count': 10
                    }
                },
                'alerts': {
                    'enabled': True,
                    'cooldown_minutes': 15,
                    'max_alerts_per_hour': 50
                }
            }
            
            config_path = self.config_dir / "monitoring_config.yaml"
            with open(config_path, 'w', encoding='utf-8') as f:
                yaml.dump(monitoring_config, f, default_flow_style=False, 
                         allow_unicode=True, indent=2)
            
            self.logger.info(f"创建默认监控配置: {config_path}")
            
        except Exception as e:
            self.logger.error(f"创建默认监控配置失败: {e}")
    
    def validate_system_components(self) -> bool:
        """验证系统组件"""
        try:
            self.logger.info("验证系统组件...")
            
            validation_results = {}
            
            # 验证核心文件
            core_files = [
                'src/fusion/fusion_predictor.py',
                'src/fusion/probability_fusion_engine.py',
                'src/fusion/constraint_optimizer.py',
                'src/fusion/intelligent_ranker.py',
                'src/fusion/dynamic_weight_adjuster.py',
                'p8_fusion_cli.py'
            ]
            
            for file_path in core_files:
                full_path = self.project_root / file_path
                validation_results[file_path] = {
                    'exists': full_path.exists(),
                    'size': full_path.stat().st_size if full_path.exists() else 0
                }
            
            # 验证配置文件
            config_files = [
                'config/fusion_config.yaml',
                'config/system_logging_config.yaml'
            ]
            
            for config_file in config_files:
                full_path = self.project_root / config_file
                validation_results[config_file] = {
                    'exists': full_path.exists(),
                    'valid': self._validate_config_file(full_path) if full_path.exists() else False
                }
            
            # 验证数据库
            db_path = self.project_root / "data" / "lottery.db"
            validation_results['database'] = {
                'exists': db_path.exists(),
                'size_mb': db_path.stat().st_size / (1024**2) if db_path.exists() else 0
            }
            
            self.deployment_status['validations'] = validation_results
            
            # 检查验证结果
            all_valid = True
            for component, result in validation_results.items():
                if not result.get('exists', False):
                    self.logger.warning(f"组件缺失: {component}")
                    all_valid = False
            
            self.logger.info(f"系统组件验证完成，结果: {'通过' if all_valid else '部分失败'}")
            return all_valid
            
        except Exception as e:
            self.logger.error(f"验证系统组件失败: {e}")
            return False
    
    def _validate_config_file(self, config_path: Path) -> bool:
        """验证配置文件"""
        try:
            if config_path.suffix == '.yaml':
                with open(config_path, 'r', encoding='utf-8') as f:
                    yaml.safe_load(f)
            elif config_path.suffix == '.json':
                with open(config_path, 'r', encoding='utf-8') as f:
                    json.load(f)
            return True
        except Exception:
            return False
    
    def create_deployment_summary(self) -> Dict[str, Any]:
        """创建部署摘要"""
        summary = {
            'deployment_time': self.deployment_status['deployment_time'],
            'total_components': len(self.deployment_status.get('components', {})),
            'total_services': len(self.deployment_status.get('services', {})),
            'enabled_components': [],
            'available_services': [],
            'system_status': 'unknown'
        }
        
        try:
            # 统计启用的组件
            for component, status in self.deployment_status.get('components', {}).items():
                if status.get('status') == 'enabled':
                    summary['enabled_components'].append(component)
            
            # 统计可用的服务
            for service, status in self.deployment_status.get('services', {}).items():
                if status.get('status') == 'available':
                    summary['available_services'].append(service)
            
            # 确定系统状态
            validations = self.deployment_status.get('validations', {})
            validation_count = len(validations)
            valid_count = sum(1 for v in validations.values() if v.get('exists', False))
            
            if valid_count == validation_count and validation_count > 0:
                summary['system_status'] = 'fully_operational'
            elif valid_count >= validation_count * 0.8:
                summary['system_status'] = 'mostly_operational'
            elif valid_count >= validation_count * 0.5:
                summary['system_status'] = 'partially_operational'
            else:
                summary['system_status'] = 'limited_operational'
            
        except Exception as e:
            self.logger.error(f"创建部署摘要失败: {e}")
        
        return summary
    
    def run_full_deployment(self) -> Dict[str, Any]:
        """运行全功能部署"""
        self.logger.info("开始全功能部署")
        
        deployment_success = True
        
        # 1. 启用融合系统
        if not self.enable_fusion_system():
            deployment_success = False
        
        # 2. 启用监控系统
        if not self.enable_monitoring_system():
            deployment_success = False
        
        # 3. 启用自动调整功能
        if not self.enable_auto_adjustment():
            deployment_success = False
        
        # 4. 验证系统组件
        if not self.validate_system_components():
            deployment_success = False
        
        # 5. 更新总体状态
        if deployment_success:
            self.deployment_status['overall_status'] = 'deployed'
        else:
            self.deployment_status['overall_status'] = 'partial_deployment'
        
        # 6. 创建部署摘要
        deployment_summary = self.create_deployment_summary()
        self.deployment_status['summary'] = deployment_summary
        
        # 7. 保存部署报告
        self._save_deployment_report()
        
        return self.deployment_status
    
    def _save_deployment_report(self):
        """保存部署报告"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = self.reports_dir / f"full_deployment_{timestamp}.json"
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.deployment_status, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"部署报告已保存到: {filename}")
            
        except Exception as e:
            self.logger.error(f"保存部署报告失败: {e}")


def main():
    """主函数"""
    print("🚀 开始P8系统全功能部署...")
    print("=" * 50)
    
    deployment_manager = FullDeploymentManager()
    results = deployment_manager.run_full_deployment()
    
    # 输出部署结果
    print("\n📊 全功能部署结果:")
    
    overall_status = results.get('overall_status', 'unknown')
    print(f"总体状态: {overall_status}")
    
    summary = results.get('summary', {})
    if summary:
        print(f"系统状态: {summary.get('system_status', 'unknown')}")
        print(f"启用组件: {len(summary.get('enabled_components', []))}")
        print(f"可用服务: {len(summary.get('available_services', []))}")
        
        if summary.get('enabled_components'):
            print("\n✅ 已启用组件:")
            for component in summary['enabled_components']:
                print(f"  • {component}")
        
        if summary.get('available_services'):
            print("\n🔧 可用服务:")
            for service in summary['available_services']:
                print(f"  • {service}")
    
    if overall_status == 'deployed':
        print("\n🎉 P8系统全功能部署成功！")
        print("所有融合功能、监控和自动调整已启用。")
    elif overall_status == 'partial_deployment':
        print("\n⚠️ P8系统部分功能部署成功")
        print("部分组件可能需要手动配置。")
    else:
        print("\n❌ P8系统部署失败")
        print("请检查错误日志并修复问题。")
    
    return overall_status in ['deployed', 'partial_deployment']


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
