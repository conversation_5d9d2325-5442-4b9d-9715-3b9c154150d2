#!/usr/bin/env python3
"""
性能数据收集脚本

收集执行时间、内存使用、准确率等数据
为系统优化提供数据支持

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import os
import sys
import time
import json
import psutil
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

class PerformanceCollector:
    """性能数据收集器"""
    
    def __init__(self):
        """初始化性能收集器"""
        self.project_root = Path(__file__).parent.parent
        self.reports_dir = self.project_root / "reports"
        self.logs_dir = self.project_root / "logs"
        
        # 确保目录存在
        self.reports_dir.mkdir(exist_ok=True)
        self.logs_dir.mkdir(exist_ok=True)
        
        # 设置日志
        self._setup_logging()
        self.logger = logging.getLogger(__name__)
        
        # 性能数据存储
        self.performance_data = {
            'collection_start': datetime.now().isoformat(),
            'system_info': self._get_system_info(),
            'measurements': []
        }
    
    def _setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler(self.logs_dir / 'performance_collector.log')
            ]
        )
    
    def _get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        try:
            return {
                'cpu_count': psutil.cpu_count(),
                'cpu_count_logical': psutil.cpu_count(logical=True),
                'memory_total_gb': psutil.virtual_memory().total / (1024**3),
                'disk_total_gb': psutil.disk_usage('/').total / (1024**3),
                'python_version': sys.version,
                'platform': sys.platform
            }
        except Exception as e:
            self.logger.error(f"获取系统信息失败: {e}")
            return {}
    
    def collect_system_metrics(self) -> Dict[str, Any]:
        """收集系统性能指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_times = psutil.cpu_times()
            
            # 内存使用情况
            memory = psutil.virtual_memory()
            
            # 磁盘使用情况
            disk = psutil.disk_usage('/')
            disk_io = psutil.disk_io_counters()
            
            # 网络IO
            net_io = psutil.net_io_counters()
            
            # 进程信息
            process = psutil.Process()
            process_memory = process.memory_info()
            
            metrics = {
                'timestamp': datetime.now().isoformat(),
                'cpu': {
                    'percent': cpu_percent,
                    'user_time': cpu_times.user,
                    'system_time': cpu_times.system,
                    'idle_time': cpu_times.idle
                },
                'memory': {
                    'total_gb': memory.total / (1024**3),
                    'available_gb': memory.available / (1024**3),
                    'used_gb': memory.used / (1024**3),
                    'percent': memory.percent,
                    'process_rss_mb': process_memory.rss / (1024**2),
                    'process_vms_mb': process_memory.vms / (1024**2)
                },
                'disk': {
                    'total_gb': disk.total / (1024**3),
                    'used_gb': disk.used / (1024**3),
                    'free_gb': disk.free / (1024**3),
                    'percent': disk.percent
                },
                'network': {
                    'bytes_sent': net_io.bytes_sent,
                    'bytes_recv': net_io.bytes_recv,
                    'packets_sent': net_io.packets_sent,
                    'packets_recv': net_io.packets_recv
                } if net_io else {},
                'process': {
                    'pid': process.pid,
                    'cpu_percent': process.cpu_percent(),
                    'num_threads': process.num_threads(),
                    'create_time': process.create_time()
                }
            }
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"收集系统指标失败: {e}")
            return {}
    
    def measure_database_performance(self) -> Dict[str, Any]:
        """测量数据库性能"""
        db_metrics = {
            'test_name': '数据库性能测试',
            'start_time': datetime.now().isoformat(),
            'success': False,
            'metrics': {}
        }
        
        try:
            import sqlite3
            db_path = self.project_root / "data" / "lottery.db"
            
            # 测试数据库连接时间
            start_time = time.time()
            with sqlite3.connect(db_path) as conn:
                connect_time = time.time() - start_time
                
                cursor = conn.cursor()
                
                # 测试查询性能
                queries = [
                    ("SELECT COUNT(*) FROM lottery_data", "count_all"),
                    ("SELECT * FROM lottery_data ORDER BY issue DESC LIMIT 100", "recent_100"),
                    ("SELECT issue, hundreds, tens, units FROM lottery_data WHERE issue > '2024000'", "recent_data")
                ]
                
                query_times = {}
                for query, name in queries:
                    query_start = time.time()
                    cursor.execute(query)
                    results = cursor.fetchall()
                    query_time = time.time() - query_start
                    
                    query_times[name] = {
                        'time_seconds': query_time,
                        'result_count': len(results)
                    }
                
                db_metrics['metrics'] = {
                    'connection_time': connect_time,
                    'queries': query_times,
                    'database_size_mb': db_path.stat().st_size / (1024**2) if db_path.exists() else 0
                }
                
                db_metrics['success'] = True
                
        except Exception as e:
            db_metrics['error'] = str(e)
            self.logger.error(f"数据库性能测试失败: {e}")
        
        db_metrics['end_time'] = datetime.now().isoformat()
        return db_metrics
    
    def measure_file_io_performance(self) -> Dict[str, Any]:
        """测量文件IO性能"""
        io_metrics = {
            'test_name': '文件IO性能测试',
            'start_time': datetime.now().isoformat(),
            'success': False,
            'metrics': {}
        }
        
        try:
            test_file = self.reports_dir / "io_test.tmp"
            test_data = "测试数据" * 10000  # 约80KB数据
            
            # 测试写入性能
            write_start = time.time()
            with open(test_file, 'w', encoding='utf-8') as f:
                f.write(test_data)
            write_time = time.time() - write_start
            
            # 测试读取性能
            read_start = time.time()
            with open(test_file, 'r', encoding='utf-8') as f:
                read_data = f.read()
            read_time = time.time() - read_start
            
            # 获取文件大小
            file_size_kb = test_file.stat().st_size / 1024
            
            io_metrics['metrics'] = {
                'write_time_seconds': write_time,
                'read_time_seconds': read_time,
                'file_size_kb': file_size_kb,
                'write_speed_kb_per_sec': file_size_kb / write_time if write_time > 0 else 0,
                'read_speed_kb_per_sec': file_size_kb / read_time if read_time > 0 else 0
            }
            
            # 清理测试文件
            test_file.unlink()
            
            io_metrics['success'] = True
            
        except Exception as e:
            io_metrics['error'] = str(e)
            self.logger.error(f"文件IO性能测试失败: {e}")
        
        io_metrics['end_time'] = datetime.now().isoformat()
        return io_metrics
    
    def collect_log_statistics(self) -> Dict[str, Any]:
        """收集日志统计信息"""
        log_stats = {
            'test_name': '日志统计',
            'start_time': datetime.now().isoformat(),
            'success': False,
            'statistics': {}
        }
        
        try:
            log_files = list(self.logs_dir.glob("*.log"))
            
            total_size = 0
            file_stats = []
            
            for log_file in log_files:
                file_size = log_file.stat().st_size
                total_size += file_size
                
                # 统计日志行数
                try:
                    with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                        line_count = sum(1 for _ in f)
                except:
                    line_count = 0
                
                file_stats.append({
                    'name': log_file.name,
                    'size_mb': file_size / (1024**2),
                    'line_count': line_count,
                    'modified_time': datetime.fromtimestamp(log_file.stat().st_mtime).isoformat()
                })
            
            log_stats['statistics'] = {
                'total_files': len(log_files),
                'total_size_mb': total_size / (1024**2),
                'files': file_stats
            }
            
            log_stats['success'] = True
            
        except Exception as e:
            log_stats['error'] = str(e)
            self.logger.error(f"日志统计失败: {e}")
        
        log_stats['end_time'] = datetime.now().isoformat()
        return log_stats
    
    def run_performance_collection(self, duration_minutes: int = 5) -> Dict[str, Any]:
        """运行性能数据收集"""
        self.logger.info(f"开始性能数据收集，持续时间: {duration_minutes} 分钟")
        
        collection_result = {
            'collection_info': {
                'duration_minutes': duration_minutes,
                'start_time': datetime.now().isoformat(),
                'measurements_count': 0
            },
            'system_baseline': self.collect_system_metrics(),
            'performance_tests': [],
            'continuous_monitoring': []
        }
        
        # 执行性能测试
        self.logger.info("执行数据库性能测试...")
        db_perf = self.measure_database_performance()
        collection_result['performance_tests'].append(db_perf)
        
        self.logger.info("执行文件IO性能测试...")
        io_perf = self.measure_file_io_performance()
        collection_result['performance_tests'].append(io_perf)
        
        self.logger.info("收集日志统计信息...")
        log_stats = self.collect_log_statistics()
        collection_result['performance_tests'].append(log_stats)
        
        # 连续监控
        self.logger.info(f"开始连续监控 {duration_minutes} 分钟...")
        end_time = datetime.now() + timedelta(minutes=duration_minutes)
        measurement_count = 0
        
        while datetime.now() < end_time:
            metrics = self.collect_system_metrics()
            if metrics:
                collection_result['continuous_monitoring'].append(metrics)
                measurement_count += 1
                
                if measurement_count % 10 == 0:
                    self.logger.info(f"已收集 {measurement_count} 个测量点")
            
            time.sleep(30)  # 每30秒收集一次
        
        collection_result['collection_info']['measurements_count'] = measurement_count
        collection_result['collection_info']['end_time'] = datetime.now().isoformat()
        
        # 计算统计信息
        if collection_result['continuous_monitoring']:
            self._calculate_statistics(collection_result)
        
        # 保存结果
        self._save_performance_data(collection_result)
        
        return collection_result
    
    def _calculate_statistics(self, collection_result: Dict[str, Any]):
        """计算性能统计信息"""
        try:
            measurements = collection_result['continuous_monitoring']
            
            if not measurements:
                return
            
            # 提取CPU和内存数据
            cpu_values = [m['cpu']['percent'] for m in measurements if 'cpu' in m]
            memory_values = [m['memory']['percent'] for m in measurements if 'memory' in m]
            
            statistics = {
                'cpu': {
                    'avg': sum(cpu_values) / len(cpu_values) if cpu_values else 0,
                    'max': max(cpu_values) if cpu_values else 0,
                    'min': min(cpu_values) if cpu_values else 0
                },
                'memory': {
                    'avg': sum(memory_values) / len(memory_values) if memory_values else 0,
                    'max': max(memory_values) if memory_values else 0,
                    'min': min(memory_values) if memory_values else 0
                }
            }
            
            collection_result['statistics'] = statistics
            
        except Exception as e:
            self.logger.error(f"计算统计信息失败: {e}")
    
    def _save_performance_data(self, data: Dict[str, Any]):
        """保存性能数据"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = self.reports_dir / f"performance_data_{timestamp}.json"
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"性能数据已保存到: {filename}")
            
        except Exception as e:
            self.logger.error(f"保存性能数据失败: {e}")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="性能数据收集工具")
    parser.add_argument('--duration', type=int, default=5, 
                       help='监控持续时间(分钟)')
    
    args = parser.parse_args()
    
    print(f"🚀 开始性能数据收集 (持续 {args.duration} 分钟)...")
    print("=" * 50)
    
    collector = PerformanceCollector()
    results = collector.run_performance_collection(args.duration)
    
    # 输出结果摘要
    print("\n📊 性能数据收集完成:")
    print(f"测量点数量: {results['collection_info']['measurements_count']}")
    print(f"性能测试数量: {len(results['performance_tests'])}")
    
    if 'statistics' in results:
        stats = results['statistics']
        print(f"平均CPU使用率: {stats['cpu']['avg']:.1f}%")
        print(f"平均内存使用率: {stats['memory']['avg']:.1f}%")
    
    print("\n✅ 性能数据收集任务完成！")
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
