import sqlite3
import os

# 分析 fucai3d.db
db_path = 'data/fucai3d.db'
print(f"分析 {db_path}")
print("=" * 50)

if os.path.exists(db_path):
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 获取表列表
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = [t[0] for t in cursor.fetchall()]
    print(f"表数量: {len(tables)}")
    print(f"表名: {tables}")
    
    for table in tables:
        print(f"\n表: {table}")
        
        # 获取表结构
        cursor.execute(f"PRAGMA table_info({table})")
        columns = cursor.fetchall()
        print("字段:")
        for col in columns:
            print(f"  {col[1]} ({col[2]})")
        
        # 获取记录数
        cursor.execute(f"SELECT COUNT(*) FROM {table}")
        count = cursor.fetchone()[0]
        print(f"记录数: {count}")
        
        # 显示示例数据
        if count > 0:
            cursor.execute(f"SELECT * FROM {table} LIMIT 2")
            samples = cursor.fetchall()
            print("示例:")
            for sample in samples:
                print(f"  {sample}")
    
    conn.close()
else:
    print("文件不存在")
