"""
福彩3D特征服务接口
提供统一的特征获取API，支持单期查询、批量查询和历史特征计算
"""

import sqlite3
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
import pandas as pd

try:
    from ..database.models import LotteryData
    from .feature_calculator import LotteryFeatureCalculator
except ImportError:
    # 绝对导入作为备选方案
    import sys
    import os
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
    from database.models import LotteryData
    from data.feature_calculator import LotteryFeatureCalculator


class FeatureService:
    """福彩3D特征服务"""
    
    def __init__(self, db_path: str):
        """
        初始化特征服务
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = db_path
        self.calculator = LotteryFeatureCalculator()
    
    def get_features_for_issue(self, issue: str, include_trend: bool = True) -> Optional[Dict[str, Any]]:
        """
        获取指定期号的所有特征
        
        Args:
            issue: 期号
            include_trend: 是否包含走势特征
            
        Returns:
            Optional[Dict[str, Any]]: 特征字典，如果期号不存在返回None
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # 获取当前期数据
            cursor.execute("""
                SELECT issue, draw_date, hundreds, tens, units, sum_value, span, number_type
                FROM lottery_data WHERE issue = ?
            """, (issue,))
            
            row = cursor.fetchone()
            if not row:
                return None
            
            # 创建LotteryData对象
            current_data = LotteryData(
                issue=row[0], draw_date=row[1], hundreds=row[2], tens=row[3], 
                units=row[4], sum_value=row[5], span=row[6], number_type=row[7]
            )
            
            # 获取上期数据（用于走势计算）
            previous_data = None
            if include_trend:
                previous_data = self._get_previous_data(issue, cursor)
            
            # 计算特征
            features = current_data.get_features(previous_data)
            
            return features
            
        finally:
            conn.close()
    
    def get_batch_features(self, issues: List[str], include_trend: bool = True) -> Dict[str, Dict[str, Any]]:
        """
        批量获取多期特征
        
        Args:
            issues: 期号列表
            include_trend: 是否包含走势特征
            
        Returns:
            Dict[str, Dict[str, Any]]: 期号到特征字典的映射
        """
        result = {}
        
        for issue in issues:
            features = self.get_features_for_issue(issue, include_trend)
            if features:
                result[issue] = features
        
        return result
    
    def get_historical_features(self, start_issue: Optional[str] = None, 
                              end_issue: Optional[str] = None,
                              limit: Optional[int] = None,
                              include_trend: bool = True) -> List[Dict[str, Any]]:
        """
        获取历史特征数据
        
        Args:
            start_issue: 起始期号
            end_issue: 结束期号
            limit: 限制返回数量
            include_trend: 是否包含走势特征
            
        Returns:
            List[Dict[str, Any]]: 特征列表
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # 构建查询条件
            where_conditions = []
            params = []
            
            if start_issue:
                where_conditions.append("issue >= ?")
                params.append(start_issue)
            
            if end_issue:
                where_conditions.append("issue <= ?")
                params.append(end_issue)
            
            where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"
            
            # 构建查询语句
            query = f"""
                SELECT issue, draw_date, hundreds, tens, units, sum_value, span, number_type
                FROM lottery_data 
                WHERE {where_clause}
                ORDER BY issue
            """
            
            if limit:
                query += f" LIMIT {limit}"
            
            cursor.execute(query, params)
            rows = cursor.fetchall()
            
            # 转换为LotteryData对象并计算特征
            result = []
            previous_data = None
            
            for row in rows:
                current_data = LotteryData(
                    issue=row[0], draw_date=row[1], hundreds=row[2], tens=row[3],
                    units=row[4], sum_value=row[5], span=row[6], number_type=row[7]
                )
                
                # 计算特征
                features = current_data.get_features(previous_data if include_trend else None)
                result.append(features)
                
                # 更新上期数据
                if include_trend:
                    previous_data = current_data
            
            return result
            
        finally:
            conn.close()
    
    def get_features_dataframe(self, start_issue: Optional[str] = None,
                             end_issue: Optional[str] = None,
                             limit: Optional[int] = None,
                             include_trend: bool = True) -> pd.DataFrame:
        """
        获取特征数据的DataFrame格式
        
        Args:
            start_issue: 起始期号
            end_issue: 结束期号
            limit: 限制返回数量
            include_trend: 是否包含走势特征
            
        Returns:
            pd.DataFrame: 特征数据框
        """
        features_list = self.get_historical_features(start_issue, end_issue, limit, include_trend)
        return pd.DataFrame(features_list)
    
    def get_feature_statistics(self, feature_name: str, 
                             start_issue: Optional[str] = None,
                             end_issue: Optional[str] = None) -> Dict[str, Any]:
        """
        获取特征统计信息
        
        Args:
            feature_name: 特征名称
            start_issue: 起始期号
            end_issue: 结束期号
            
        Returns:
            Dict[str, Any]: 统计信息
        """
        df = self.get_features_dataframe(start_issue, end_issue, include_trend=False)
        
        if feature_name not in df.columns:
            return {'error': f'特征 {feature_name} 不存在'}
        
        feature_data = df[feature_name]
        
        # 根据数据类型计算不同的统计信息
        if pd.api.types.is_numeric_dtype(feature_data):
            stats = {
                'count': len(feature_data),
                'mean': feature_data.mean(),
                'std': feature_data.std(),
                'min': feature_data.min(),
                'max': feature_data.max(),
                'median': feature_data.median(),
                'q25': feature_data.quantile(0.25),
                'q75': feature_data.quantile(0.75)
            }
        else:
            # 分类数据统计
            value_counts = feature_data.value_counts()
            stats = {
                'count': len(feature_data),
                'unique_count': len(value_counts),
                'most_common': value_counts.index[0] if len(value_counts) > 0 else None,
                'most_common_count': value_counts.iloc[0] if len(value_counts) > 0 else 0,
                'value_counts': value_counts.to_dict()
            }
        
        return stats
    
    def calculate_feature_for_numbers(self, hundreds: int, tens: int, units: int,
                                    previous_hundreds: Optional[int] = None,
                                    previous_tens: Optional[int] = None,
                                    previous_units: Optional[int] = None) -> Dict[str, Any]:
        """
        为给定的号码计算特征（不需要数据库）
        
        Args:
            hundreds: 百位数字
            tens: 十位数字
            units: 个位数字
            previous_hundreds: 上期百位数字
            previous_tens: 上期十位数字
            previous_units: 上期个位数字
            
        Returns:
            Dict[str, Any]: 特征字典
        """
        result = self.calculator.calculate_all_features(
            hundreds, tens, units,
            previous_hundreds, previous_tens, previous_units
        )
        return result.to_dict()
    
    def _get_previous_data(self, current_issue: str, cursor) -> Optional[LotteryData]:
        """获取上期数据"""
        cursor.execute("""
            SELECT issue, draw_date, hundreds, tens, units, sum_value, span, number_type
            FROM lottery_data 
            WHERE issue < ? 
            ORDER BY issue DESC 
            LIMIT 1
        """, (current_issue,))
        
        row = cursor.fetchone()
        if not row:
            return None
        
        return LotteryData(
            issue=row[0], draw_date=row[1], hundreds=row[2], tens=row[3],
            units=row[4], sum_value=row[5], span=row[6], number_type=row[7]
        )
    
    def get_available_features(self) -> List[str]:
        """获取所有可用的特征名称"""
        # 创建一个示例来获取所有特征名称
        sample_features = self.calculate_feature_for_numbers(1, 2, 3)
        return list(sample_features.keys())
    
    def validate_database(self) -> Dict[str, Any]:
        """验证数据库连接和数据完整性"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 检查表是否存在
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name='lottery_data'
            """)
            
            if not cursor.fetchone():
                return {'status': 'error', 'message': 'lottery_data表不存在'}
            
            # 检查数据数量
            cursor.execute("SELECT COUNT(*) FROM lottery_data")
            count = cursor.fetchone()[0]
            
            # 检查最新期号
            cursor.execute("SELECT MAX(issue) FROM lottery_data")
            latest_issue = cursor.fetchone()[0]
            
            conn.close()
            
            return {
                'status': 'success',
                'total_records': count,
                'latest_issue': latest_issue,
                'available_features': len(self.get_available_features())
            }
            
        except Exception as e:
            return {'status': 'error', 'message': str(e)}
