#!/usr/bin/env python3
"""
智能排序算法

基于综合概率和约束分数排序
为P8智能交集融合系统提供智能排序功能

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import numpy as np
from typing import Dict, List, Tuple, Optional, Any
import logging
from datetime import datetime
from dataclasses import dataclass
import heapq

@dataclass
class RankingResult:
    """排序结果数据类"""
    combination: str
    rank: int
    probability: float
    constraint_score: float
    diversity_score: float
    final_score: float
    metadata: Dict[str, Any]

class IntelligentRanker:
    """智能排序器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化智能排序器
        
        Args:
            config: 配置参数
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 排序参数
        self.ranking_params = {
            'probability_weight': config.get('probability_weight', 0.5),    # 概率权重
            'constraint_weight': config.get('constraint_weight', 0.3),      # 约束权重
            'diversity_weight': config.get('diversity_weight', 0.2),        # 多样性权重
            'top_k': config.get('top_k', 20),                              # Top-K数量
            'min_score_threshold': config.get('min_score_threshold', 0.01), # 最小分数阈值
            'diversity_penalty': config.get('diversity_penalty', 0.1),      # 多样性惩罚
            'recency_weight': config.get('recency_weight', 0.05)           # 时效性权重
        }
        
        # 排序策略
        self.ranking_strategies = {
            'probability_first': self._probability_first_ranking,
            'constraint_first': self._constraint_first_ranking,
            'balanced': self._balanced_ranking,
            'diversity_enhanced': self._diversity_enhanced_ranking,
            'adaptive': self._adaptive_ranking
        }
        
        self.logger.info("智能排序器初始化完成")
    
    def rank_combinations(self, combinations: Dict[str, float], 
                         constraint_info: Dict[str, Any],
                         strategy: str = 'adaptive') -> List[RankingResult]:
        """
        对组合进行智能排序
        
        Args:
            combinations: 组合概率字典
            constraint_info: 约束信息
            strategy: 排序策略
            
        Returns:
            排序结果列表
        """
        if strategy not in self.ranking_strategies:
            raise ValueError(f"不支持的排序策略: {strategy}")
        
        try:
            # 执行排序
            ranking_results = self.ranking_strategies[strategy](combinations, constraint_info)
            
            # 后处理
            final_results = self._post_process_rankings(ranking_results)
            
            self.logger.info(f"使用{strategy}策略完成排序，生成{len(final_results)}个结果")
            return final_results
            
        except Exception as e:
            self.logger.error(f"智能排序失败: {e}")
            return self._create_fallback_rankings(combinations)
    
    def _probability_first_ranking(self, combinations: Dict[str, float], 
                                  constraint_info: Dict[str, Any]) -> List[RankingResult]:
        """概率优先排序"""
        results = []
        
        for combo, prob in combinations.items():
            # 计算约束分数
            constraint_score = self._calculate_constraint_score(combo, constraint_info)
            
            # 计算多样性分数
            diversity_score = self._calculate_diversity_score(combo, results)
            
            # 最终分数：概率为主
            final_score = (
                prob * self.ranking_params['probability_weight'] +
                constraint_score * self.ranking_params['constraint_weight'] +
                diversity_score * self.ranking_params['diversity_weight']
            )
            
            result = RankingResult(
                combination=combo,
                rank=0,  # 稍后设置
                probability=prob,
                constraint_score=constraint_score,
                diversity_score=diversity_score,
                final_score=final_score,
                metadata={'strategy': 'probability_first'}
            )
            
            results.append(result)
        
        # 按最终分数排序
        results.sort(key=lambda x: x.final_score, reverse=True)
        
        # 设置排名
        for i, result in enumerate(results):
            result.rank = i + 1
        
        return results[:self.ranking_params['top_k']]
    
    def _constraint_first_ranking(self, combinations: Dict[str, float], 
                                 constraint_info: Dict[str, Any]) -> List[RankingResult]:
        """约束优先排序"""
        results = []
        
        for combo, prob in combinations.items():
            # 计算约束分数
            constraint_score = self._calculate_constraint_score(combo, constraint_info)
            
            # 只有约束分数足够高的才考虑
            if constraint_score < 0.5:
                continue
            
            # 计算多样性分数
            diversity_score = self._calculate_diversity_score(combo, results)
            
            # 最终分数：约束为主
            final_score = (
                constraint_score * 0.6 +
                prob * 0.3 +
                diversity_score * 0.1
            )
            
            result = RankingResult(
                combination=combo,
                rank=0,
                probability=prob,
                constraint_score=constraint_score,
                diversity_score=diversity_score,
                final_score=final_score,
                metadata={'strategy': 'constraint_first'}
            )
            
            results.append(result)
        
        # 按最终分数排序
        results.sort(key=lambda x: x.final_score, reverse=True)
        
        # 设置排名
        for i, result in enumerate(results):
            result.rank = i + 1
        
        return results[:self.ranking_params['top_k']]
    
    def _balanced_ranking(self, combinations: Dict[str, float], 
                         constraint_info: Dict[str, Any]) -> List[RankingResult]:
        """平衡排序"""
        results = []
        
        for combo, prob in combinations.items():
            # 计算各项分数
            constraint_score = self._calculate_constraint_score(combo, constraint_info)
            diversity_score = self._calculate_diversity_score(combo, results)
            
            # 平衡的最终分数
            final_score = (
                prob * self.ranking_params['probability_weight'] +
                constraint_score * self.ranking_params['constraint_weight'] +
                diversity_score * self.ranking_params['diversity_weight']
            )
            
            result = RankingResult(
                combination=combo,
                rank=0,
                probability=prob,
                constraint_score=constraint_score,
                diversity_score=diversity_score,
                final_score=final_score,
                metadata={'strategy': 'balanced'}
            )
            
            results.append(result)
        
        # 按最终分数排序
        results.sort(key=lambda x: x.final_score, reverse=True)
        
        # 设置排名
        for i, result in enumerate(results):
            result.rank = i + 1
        
        return results[:self.ranking_params['top_k']]
    
    def _diversity_enhanced_ranking(self, combinations: Dict[str, float], 
                                   constraint_info: Dict[str, Any]) -> List[RankingResult]:
        """多样性增强排序"""
        results = []
        selected_combinations = []
        
        # 按概率排序的候选列表
        candidates = sorted(combinations.items(), key=lambda x: x[1], reverse=True)
        
        while len(results) < self.ranking_params['top_k'] and candidates:
            best_candidate = None
            best_score = -1
            best_index = -1
            
            for i, (combo, prob) in enumerate(candidates):
                # 计算约束分数
                constraint_score = self._calculate_constraint_score(combo, constraint_info)
                
                # 计算与已选择组合的多样性
                diversity_score = self._calculate_diversity_with_selected(combo, selected_combinations)
                
                # 综合分数，强调多样性
                final_score = (
                    prob * 0.4 +
                    constraint_score * 0.3 +
                    diversity_score * 0.3
                )
                
                if final_score > best_score:
                    best_score = final_score
                    best_candidate = (combo, prob)
                    best_index = i
            
            if best_candidate:
                combo, prob = best_candidate
                
                result = RankingResult(
                    combination=combo,
                    rank=len(results) + 1,
                    probability=prob,
                    constraint_score=self._calculate_constraint_score(combo, constraint_info),
                    diversity_score=self._calculate_diversity_with_selected(combo, selected_combinations),
                    final_score=best_score,
                    metadata={'strategy': 'diversity_enhanced'}
                )
                
                results.append(result)
                selected_combinations.append(combo)
                candidates.pop(best_index)
            else:
                break
        
        return results
    
    def _adaptive_ranking(self, combinations: Dict[str, float], 
                         constraint_info: Dict[str, Any]) -> List[RankingResult]:
        """自适应排序"""
        # 分析数据特征
        data_features = self._analyze_data_features(combinations, constraint_info)
        
        # 根据特征调整权重
        adaptive_weights = self._calculate_adaptive_weights(data_features)
        
        results = []
        
        for combo, prob in combinations.items():
            # 计算各项分数
            constraint_score = self._calculate_constraint_score(combo, constraint_info)
            diversity_score = self._calculate_diversity_score(combo, results)
            
            # 自适应最终分数
            final_score = (
                prob * adaptive_weights['probability'] +
                constraint_score * adaptive_weights['constraint'] +
                diversity_score * adaptive_weights['diversity']
            )
            
            result = RankingResult(
                combination=combo,
                rank=0,
                probability=prob,
                constraint_score=constraint_score,
                diversity_score=diversity_score,
                final_score=final_score,
                metadata={
                    'strategy': 'adaptive',
                    'adaptive_weights': adaptive_weights,
                    'data_features': data_features
                }
            )
            
            results.append(result)
        
        # 按最终分数排序
        results.sort(key=lambda x: x.final_score, reverse=True)
        
        # 设置排名
        for i, result in enumerate(results):
            result.rank = i + 1
        
        return results[:self.ranking_params['top_k']]
    
    def _calculate_constraint_score(self, combination: str, 
                                   constraint_info: Dict[str, Any]) -> float:
        """计算约束分数"""
        try:
            h, t, u = int(combination[0]), int(combination[1]), int(combination[2])
            sum_value = h + t + u
            span_value = max(h, t, u) - min(h, t, u)
            
            score = 1.0
            
            # 和值约束分数
            sum_constraints = constraint_info.get('sum_constraints', {})
            if 'sum_probabilities' in sum_constraints:
                sum_probs = sum_constraints['sum_probabilities']
                if len(sum_probs) > sum_value:
                    score *= (1 + sum_probs[sum_value])
            
            # 跨度约束分数
            span_constraints = constraint_info.get('span_constraints', {})
            if 'probabilities' in span_constraints:
                span_probs = span_constraints['probabilities']
                if len(span_probs) > span_value:
                    score *= (1 + span_probs[span_value])
            
            # 位置约束分数
            position_constraints = constraint_info.get('position_constraints', {})
            for pos, digit in zip(['hundreds', 'tens', 'units'], [h, t, u]):
                if pos in position_constraints and 'probabilities' in position_constraints[pos]:
                    probs = position_constraints[pos]['probabilities']
                    if len(probs) > digit:
                        score *= (1 + probs[digit] * 0.5)
            
            return min(score, 3.0)  # 限制最大分数
            
        except (ValueError, IndexError):
            return 0.1
    
    def _calculate_diversity_score(self, combination: str, 
                                  existing_results: List[RankingResult]) -> float:
        """计算多样性分数"""
        if not existing_results:
            return 1.0
        
        try:
            h, t, u = int(combination[0]), int(combination[1]), int(combination[2])
            current_features = self._extract_combination_features(combination)
            
            min_distance = float('inf')
            
            for result in existing_results:
                existing_features = self._extract_combination_features(result.combination)
                distance = self._calculate_feature_distance(current_features, existing_features)
                min_distance = min(min_distance, distance)
            
            # 距离越大，多样性分数越高
            diversity_score = min_distance / 10.0  # 归一化
            return min(diversity_score, 1.0)
            
        except (ValueError, IndexError):
            return 0.5
    
    def _calculate_diversity_with_selected(self, combination: str, 
                                         selected_combinations: List[str]) -> float:
        """计算与已选择组合的多样性"""
        if not selected_combinations:
            return 1.0
        
        current_features = self._extract_combination_features(combination)
        
        min_distance = float('inf')
        for selected in selected_combinations:
            selected_features = self._extract_combination_features(selected)
            distance = self._calculate_feature_distance(current_features, selected_features)
            min_distance = min(min_distance, distance)
        
        # 距离越大，多样性分数越高
        diversity_score = min_distance / 10.0
        return min(diversity_score, 1.0)
    
    def _extract_combination_features(self, combination: str) -> Tuple:
        """提取组合特征"""
        h, t, u = int(combination[0]), int(combination[1]), int(combination[2])
        
        return (
            h + t + u,  # 和值
            max(h, t, u) - min(h, t, u),  # 跨度
            len(set([h, t, u])),  # 不同数字个数
            h % 2 + t % 2 + u % 2,  # 奇数个数
            tuple(sorted([h, t, u]))  # 排序后的数字
        )
    
    def _calculate_feature_distance(self, features1: Tuple, features2: Tuple) -> float:
        """计算特征距离"""
        distance = 0.0
        
        # 和值距离
        distance += abs(features1[0] - features2[0]) * 0.3
        
        # 跨度距离
        distance += abs(features1[1] - features2[1]) * 0.3
        
        # 不同数字个数距离
        distance += abs(features1[2] - features2[2]) * 0.2
        
        # 奇数个数距离
        distance += abs(features1[3] - features2[3]) * 0.1
        
        # 数字组合距离
        if features1[4] != features2[4]:
            distance += 0.1
        
        return distance
    
    def _analyze_data_features(self, combinations: Dict[str, float], 
                              constraint_info: Dict[str, Any]) -> Dict[str, Any]:
        """分析数据特征"""
        probs = list(combinations.values())
        
        features = {
            'probability_entropy': -sum(p * np.log2(p) for p in probs if p > 0),
            'probability_variance': np.var(probs),
            'max_probability': max(probs),
            'min_probability': min(probs),
            'combination_count': len(combinations),
            'constraint_coverage': self._calculate_constraint_coverage(combinations, constraint_info)
        }
        
        return features
    
    def _calculate_constraint_coverage(self, combinations: Dict[str, float], 
                                     constraint_info: Dict[str, Any]) -> float:
        """计算约束覆盖率"""
        valid_count = 0
        
        for combo in combinations.keys():
            constraint_score = self._calculate_constraint_score(combo, constraint_info)
            if constraint_score > 0.5:
                valid_count += 1
        
        return valid_count / len(combinations) if combinations else 0.0
    
    def _calculate_adaptive_weights(self, data_features: Dict[str, Any]) -> Dict[str, float]:
        """计算自适应权重"""
        base_weights = {
            'probability': self.ranking_params['probability_weight'],
            'constraint': self.ranking_params['constraint_weight'],
            'diversity': self.ranking_params['diversity_weight']
        }
        
        # 根据数据特征调整权重
        if data_features['probability_entropy'] > 3.0:
            # 概率分布较均匀，增加约束权重
            base_weights['constraint'] *= 1.2
            base_weights['probability'] *= 0.9
        
        if data_features['constraint_coverage'] < 0.3:
            # 约束覆盖率低，增加约束权重
            base_weights['constraint'] *= 1.3
            base_weights['diversity'] *= 0.8
        
        if data_features['combination_count'] > 100:
            # 组合数量多，增加多样性权重
            base_weights['diversity'] *= 1.2
            base_weights['probability'] *= 0.9
        
        # 归一化权重
        total_weight = sum(base_weights.values())
        return {k: v / total_weight for k, v in base_weights.items()}
    
    def _post_process_rankings(self, results: List[RankingResult]) -> List[RankingResult]:
        """后处理排序结果"""
        # 过滤低分结果
        threshold = self.ranking_params['min_score_threshold']
        filtered_results = [r for r in results if r.final_score >= threshold]
        
        # 如果过滤后结果太少，保留原结果
        if len(filtered_results) < 5:
            filtered_results = results
        
        # 限制返回数量
        return filtered_results[:self.ranking_params['top_k']]
    
    def _create_fallback_rankings(self, combinations: Dict[str, float]) -> List[RankingResult]:
        """创建备用排序结果"""
        results = []
        
        # 简单按概率排序
        sorted_combos = sorted(combinations.items(), key=lambda x: x[1], reverse=True)
        
        for i, (combo, prob) in enumerate(sorted_combos[:self.ranking_params['top_k']]):
            result = RankingResult(
                combination=combo,
                rank=i + 1,
                probability=prob,
                constraint_score=0.5,
                diversity_score=0.5,
                final_score=prob,
                metadata={'strategy': 'fallback'}
            )
            results.append(result)
        
        return results
    
    def get_ranking_summary(self, results: List[RankingResult]) -> Dict[str, Any]:
        """获取排序摘要"""
        if not results:
            return {'error': 'no_results'}
        
        summary = {
            'total_results': len(results),
            'top_probability': results[0].probability,
            'top_constraint_score': results[0].constraint_score,
            'average_final_score': np.mean([r.final_score for r in results]),
            'score_variance': np.var([r.final_score for r in results]),
            'strategy_used': results[0].metadata.get('strategy', 'unknown')
        }
        
        return summary
