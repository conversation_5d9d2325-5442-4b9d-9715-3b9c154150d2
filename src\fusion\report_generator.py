#!/usr/bin/env python3
"""
评估报告生成器

创建详细的性能评估报告和可视化图表
为P8智能交集融合系统提供报告生成功能

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
import logging
import json
import sqlite3
from datetime import datetime, timedelta
from pathlib import Path
import base64
from io import BytesIO

class ReportGenerator:
    """评估报告生成器"""
    
    def __init__(self, db_path: str, output_dir: str = "reports"):
        """
        初始化报告生成器
        
        Args:
            db_path: 数据库路径
            output_dir: 报告输出目录
        """
        self.db_path = db_path
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger = logging.getLogger(__name__)
        
        # 报告模板
        self.html_template = self._get_html_template()
        
        self.logger.info("评估报告生成器初始化完成")
    
    def generate_performance_report(self, days: int = 30, 
                                   include_charts: bool = True) -> Dict[str, Any]:
        """
        生成性能评估报告
        
        Args:
            days: 统计天数
            include_charts: 是否包含图表
            
        Returns:
            报告数据和文件路径
        """
        try:
            self.logger.info(f"开始生成{days}天的性能评估报告")
            
            # 收集报告数据
            report_data = self._collect_report_data(days)
            
            # 生成图表
            charts = {}
            if include_charts:
                charts = self._generate_charts(report_data)
            
            # 生成HTML报告
            html_content = self._generate_html_report(report_data, charts)
            
            # 保存报告文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            html_file = self.output_dir / f"performance_report_{timestamp}.html"
            
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            # 生成JSON报告
            json_file = self.output_dir / f"performance_data_{timestamp}.json"
            
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False, default=str)
            
            result = {
                'report_data': report_data,
                'html_file': str(html_file),
                'json_file': str(json_file),
                'charts': charts,
                'generation_time': datetime.now().isoformat()
            }
            
            self.logger.info(f"性能评估报告生成完成: {html_file}")
            return result
            
        except Exception as e:
            self.logger.error(f"生成性能评估报告失败: {e}")
            return {'error': str(e)}
    
    def _collect_report_data(self, days: int) -> Dict[str, Any]:
        """收集报告数据"""
        cutoff_date = (datetime.now() - timedelta(days=days)).isoformat()
        
        report_data = {
            'period': {
                'days': days,
                'start_date': cutoff_date,
                'end_date': datetime.now().isoformat()
            },
            'summary': {},
            'predictions': {},
            'performance': {},
            'weights': {},
            'sessions': {},
            'trends': {}
        }
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                # 基本统计
                report_data['summary'] = self._get_summary_stats(conn, cutoff_date)
                
                # 预测统计
                report_data['predictions'] = self._get_prediction_stats(conn, cutoff_date)
                
                # 性能统计
                report_data['performance'] = self._get_performance_stats(conn, cutoff_date)
                
                # 权重统计
                report_data['weights'] = self._get_weight_stats(conn, cutoff_date)
                
                # 会话统计
                report_data['sessions'] = self._get_session_stats(conn, cutoff_date)
                
                # 趋势分析
                report_data['trends'] = self._get_trend_analysis(conn, cutoff_date)
                
        except Exception as e:
            self.logger.error(f"收集报告数据失败: {e}")
            report_data['error'] = str(e)
        
        return report_data
    
    def _get_summary_stats(self, conn: sqlite3.Connection, cutoff_date: str) -> Dict[str, Any]:
        """获取摘要统计"""
        cursor = conn.cursor()
        
        # 总预测数
        cursor.execute('''
            SELECT COUNT(*) FROM final_predictions
            WHERE created_at >= ?
        ''', (cutoff_date,))
        total_predictions = cursor.fetchone()[0]
        
        # 总评估数
        cursor.execute('''
            SELECT COUNT(*) FROM prediction_performance
            WHERE evaluated_at >= ?
        ''', (cutoff_date,))
        total_evaluations = cursor.fetchone()[0]
        
        # 成功会话数
        cursor.execute('''
            SELECT COUNT(*) FROM fusion_sessions
            WHERE created_at >= ? AND success = 1
        ''', (cutoff_date,))
        successful_sessions = cursor.fetchone()[0]
        
        # 总会话数
        cursor.execute('''
            SELECT COUNT(*) FROM fusion_sessions
            WHERE created_at >= ?
        ''', (cutoff_date,))
        total_sessions = cursor.fetchone()[0]
        
        return {
            'total_predictions': total_predictions,
            'total_evaluations': total_evaluations,
            'successful_sessions': successful_sessions,
            'total_sessions': total_sessions,
            'success_rate': successful_sessions / total_sessions if total_sessions > 0 else 0.0
        }
    
    def _get_prediction_stats(self, conn: sqlite3.Connection, cutoff_date: str) -> Dict[str, Any]:
        """获取预测统计"""
        cursor = conn.cursor()
        
        # 按融合方法统计
        cursor.execute('''
            SELECT fusion_method, COUNT(*) as count
            FROM final_predictions
            WHERE created_at >= ?
            GROUP BY fusion_method
            ORDER BY count DESC
        ''', (cutoff_date,))
        fusion_methods = dict(cursor.fetchall())
        
        # 按排序策略统计
        cursor.execute('''
            SELECT ranking_strategy, COUNT(*) as count
            FROM final_predictions
            WHERE created_at >= ?
            GROUP BY ranking_strategy
            ORDER BY count DESC
        ''', (cutoff_date,))
        ranking_strategies = dict(cursor.fetchall())
        
        # 按置信水平统计
        cursor.execute('''
            SELECT confidence_level, COUNT(*) as count
            FROM final_predictions
            WHERE created_at >= ?
            GROUP BY confidence_level
            ORDER BY count DESC
        ''', (cutoff_date,))
        confidence_levels = dict(cursor.fetchall())
        
        # 概率分布统计
        cursor.execute('''
            SELECT 
                AVG(combined_probability) as avg_probability,
                MIN(combined_probability) as min_probability,
                MAX(combined_probability) as max_probability,
                AVG(constraint_score) as avg_constraint_score,
                AVG(diversity_score) as avg_diversity_score
            FROM final_predictions
            WHERE created_at >= ?
        ''', (cutoff_date,))
        
        prob_stats = cursor.fetchone()
        
        return {
            'fusion_methods': fusion_methods,
            'ranking_strategies': ranking_strategies,
            'confidence_levels': confidence_levels,
            'probability_stats': {
                'avg_probability': prob_stats[0] or 0.0,
                'min_probability': prob_stats[1] or 0.0,
                'max_probability': prob_stats[2] or 0.0,
                'avg_constraint_score': prob_stats[3] or 0.0,
                'avg_diversity_score': prob_stats[4] or 0.0
            }
        }
    
    def _get_performance_stats(self, conn: sqlite3.Connection, cutoff_date: str) -> Dict[str, Any]:
        """获取性能统计"""
        cursor = conn.cursor()
        
        # 命中率统计
        cursor.execute('''
            SELECT 
                SUM(CASE WHEN hit_type = 'exact' THEN 1 ELSE 0 END) as exact_hits,
                SUM(CASE WHEN hit_type IN ('exact', 'position') THEN 1 ELSE 0 END) as position_hits,
                SUM(CASE WHEN top_k_hit = 1 THEN 1 ELSE 0 END) as top_k_hits,
                COUNT(*) as total_evaluations,
                AVG(overall_score) as avg_overall_score
            FROM prediction_performance
            WHERE evaluated_at >= ?
        ''', (cutoff_date,))
        
        hit_stats = cursor.fetchone()
        
        # 各位置准确率
        cursor.execute('''
            SELECT 
                AVG(CASE WHEN hundreds_accuracy THEN 1.0 ELSE 0.0 END) as hundreds_accuracy,
                AVG(CASE WHEN tens_accuracy THEN 1.0 ELSE 0.0 END) as tens_accuracy,
                AVG(CASE WHEN units_accuracy THEN 1.0 ELSE 0.0 END) as units_accuracy,
                AVG(CASE WHEN sum_accuracy THEN 1.0 ELSE 0.0 END) as sum_accuracy,
                AVG(CASE WHEN span_accuracy THEN 1.0 ELSE 0.0 END) as span_accuracy
            FROM prediction_performance
            WHERE evaluated_at >= ?
        ''', (cutoff_date,))
        
        accuracy_stats = cursor.fetchone()
        
        total_evals = hit_stats[3] if hit_stats[3] else 1
        
        return {
            'hit_rates': {
                'exact_hit_rate': (hit_stats[0] or 0) / total_evals,
                'position_hit_rate': (hit_stats[1] or 0) / total_evals,
                'top_k_hit_rate': (hit_stats[2] or 0) / total_evals
            },
            'accuracy_rates': {
                'hundreds_accuracy': accuracy_stats[0] or 0.0,
                'tens_accuracy': accuracy_stats[1] or 0.0,
                'units_accuracy': accuracy_stats[2] or 0.0,
                'sum_accuracy': accuracy_stats[3] or 0.0,
                'span_accuracy': accuracy_stats[4] or 0.0
            },
            'avg_overall_score': hit_stats[4] or 0.0,
            'total_evaluations': total_evals
        }
    
    def _get_weight_stats(self, conn: sqlite3.Connection, cutoff_date: str) -> Dict[str, Any]:
        """获取权重统计"""
        cursor = conn.cursor()
        
        # 当前权重
        cursor.execute('''
            SELECT predictor_name, weight_value, performance_score, accuracy_rate
            FROM fusion_weights
            WHERE is_active = 1
            ORDER BY predictor_name
        ''')
        
        current_weights = {}
        for row in cursor.fetchall():
            current_weights[row[0]] = {
                'weight': row[1],
                'performance_score': row[2] or 0.0,
                'accuracy_rate': row[3] or 0.0
            }
        
        # 权重历史
        cursor.execute('''
            SELECT timestamp, weights
            FROM weight_history
            WHERE timestamp >= ?
            ORDER BY timestamp
        ''', (cutoff_date,))
        
        weight_history = []
        for row in cursor.fetchall():
            try:
                weights_data = json.loads(row[1])
                weight_history.append({
                    'timestamp': row[0],
                    'weights': weights_data
                })
            except json.JSONDecodeError:
                continue
        
        return {
            'current_weights': current_weights,
            'weight_history': weight_history,
            'weight_changes_count': len(weight_history)
        }
    
    def _get_session_stats(self, conn: sqlite3.Connection, cutoff_date: str) -> Dict[str, Any]:
        """获取会话统计"""
        cursor = conn.cursor()
        
        # 执行时间统计
        cursor.execute('''
            SELECT 
                AVG(execution_time) as avg_execution_time,
                MIN(execution_time) as min_execution_time,
                MAX(execution_time) as max_execution_time,
                COUNT(*) as total_sessions
            FROM fusion_sessions
            WHERE created_at >= ? AND success = 1
        ''', (cutoff_date,))
        
        time_stats = cursor.fetchone()
        
        # 错误统计
        cursor.execute('''
            SELECT error_message, COUNT(*) as count
            FROM fusion_sessions
            WHERE created_at >= ? AND success = 0
            GROUP BY error_message
            ORDER BY count DESC
            LIMIT 10
        ''', (cutoff_date,))
        
        error_stats = dict(cursor.fetchall())
        
        return {
            'execution_time': {
                'avg_time': time_stats[0] or 0.0,
                'min_time': time_stats[1] or 0.0,
                'max_time': time_stats[2] or 0.0
            },
            'total_sessions': time_stats[3] or 0,
            'error_stats': error_stats
        }
    
    def _get_trend_analysis(self, conn: sqlite3.Connection, cutoff_date: str) -> Dict[str, Any]:
        """获取趋势分析"""
        cursor = conn.cursor()
        
        # 每日预测数量趋势
        cursor.execute('''
            SELECT 
                DATE(created_at) as date,
                COUNT(*) as prediction_count
            FROM final_predictions
            WHERE created_at >= ?
            GROUP BY DATE(created_at)
            ORDER BY date
        ''', (cutoff_date,))
        
        daily_predictions = [{'date': row[0], 'count': row[1]} for row in cursor.fetchall()]
        
        # 每日命中率趋势
        cursor.execute('''
            SELECT 
                DATE(evaluated_at) as date,
                AVG(CASE WHEN hit_type = 'exact' THEN 1.0 ELSE 0.0 END) as hit_rate
            FROM prediction_performance
            WHERE evaluated_at >= ?
            GROUP BY DATE(evaluated_at)
            ORDER BY date
        ''', (cutoff_date,))
        
        daily_hit_rates = [{'date': row[0], 'hit_rate': row[1] or 0.0} for row in cursor.fetchall()]
        
        return {
            'daily_predictions': daily_predictions,
            'daily_hit_rates': daily_hit_rates
        }
    
    def _generate_charts(self, report_data: Dict[str, Any]) -> Dict[str, str]:
        """生成图表（返回base64编码的图片）"""
        charts = {}
        
        try:
            import matplotlib.pyplot as plt
            import seaborn as sns
            
            # 设置中文字体
            plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
            plt.rcParams['axes.unicode_minus'] = False
            
            # 1. 命中率图表
            charts['hit_rates'] = self._create_hit_rate_chart(report_data, plt)
            
            # 2. 趋势图表
            charts['trends'] = self._create_trend_chart(report_data, plt)
            
            # 3. 权重分布图表
            charts['weights'] = self._create_weight_chart(report_data, plt)
            
            # 4. 性能分布图表
            charts['performance'] = self._create_performance_chart(report_data, plt)
            
        except ImportError:
            self.logger.warning("matplotlib未安装，跳过图表生成")
        except Exception as e:
            self.logger.error(f"生成图表失败: {e}")
        
        return charts
    
    def _create_hit_rate_chart(self, report_data: Dict[str, Any], plt) -> str:
        """创建命中率图表"""
        try:
            fig, ax = plt.subplots(figsize=(10, 6))
            
            hit_rates = report_data['performance']['hit_rates']
            categories = ['精确命中', '位置命中', 'Top-K命中']
            values = [
                hit_rates['exact_hit_rate'],
                hit_rates['position_hit_rate'],
                hit_rates['top_k_hit_rate']
            ]
            
            bars = ax.bar(categories, values, color=['#ff6b6b', '#4ecdc4', '#45b7d1'])
            ax.set_ylabel('命中率')
            ax.set_title('预测命中率统计')
            ax.set_ylim(0, 1)
            
            # 添加数值标签
            for bar, value in zip(bars, values):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                       f'{value:.2%}', ha='center', va='bottom')
            
            plt.tight_layout()
            
            # 转换为base64
            buffer = BytesIO()
            plt.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
            buffer.seek(0)
            chart_base64 = base64.b64encode(buffer.getvalue()).decode()
            plt.close()
            
            return chart_base64
            
        except Exception as e:
            self.logger.error(f"创建命中率图表失败: {e}")
            return ""
    
    def _create_trend_chart(self, report_data: Dict[str, Any], plt) -> str:
        """创建趋势图表"""
        try:
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
            
            # 预测数量趋势
            daily_preds = report_data['trends']['daily_predictions']
            if daily_preds:
                dates = [item['date'] for item in daily_preds]
                counts = [item['count'] for item in daily_preds]
                
                ax1.plot(dates, counts, marker='o', linewidth=2, markersize=4)
                ax1.set_title('每日预测数量趋势')
                ax1.set_ylabel('预测数量')
                ax1.tick_params(axis='x', rotation=45)
            
            # 命中率趋势
            daily_hits = report_data['trends']['daily_hit_rates']
            if daily_hits:
                dates = [item['date'] for item in daily_hits]
                hit_rates = [item['hit_rate'] for item in daily_hits]
                
                ax2.plot(dates, hit_rates, marker='s', linewidth=2, markersize=4, color='orange')
                ax2.set_title('每日命中率趋势')
                ax2.set_ylabel('命中率')
                ax2.set_ylim(0, max(hit_rates) * 1.2 if hit_rates else 1)
                ax2.tick_params(axis='x', rotation=45)
            
            plt.tight_layout()
            
            # 转换为base64
            buffer = BytesIO()
            plt.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
            buffer.seek(0)
            chart_base64 = base64.b64encode(buffer.getvalue()).decode()
            plt.close()
            
            return chart_base64
            
        except Exception as e:
            self.logger.error(f"创建趋势图表失败: {e}")
            return ""
    
    def _create_weight_chart(self, report_data: Dict[str, Any], plt) -> str:
        """创建权重分布图表"""
        try:
            fig, ax = plt.subplots(figsize=(10, 6))
            
            weights = report_data['weights']['current_weights']
            if weights:
                predictors = list(weights.keys())
                weight_values = [weights[p]['weight'] for p in predictors]
                
                bars = ax.bar(predictors, weight_values, color='skyblue')
                ax.set_ylabel('权重值')
                ax.set_title('当前预测器权重分布')
                
                # 添加数值标签
                for bar, value in zip(bars, weight_values):
                    height = bar.get_height()
                    ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                           f'{value:.2f}', ha='center', va='bottom')
            
            plt.tight_layout()
            
            # 转换为base64
            buffer = BytesIO()
            plt.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
            buffer.seek(0)
            chart_base64 = base64.b64encode(buffer.getvalue()).decode()
            plt.close()
            
            return chart_base64
            
        except Exception as e:
            self.logger.error(f"创建权重图表失败: {e}")
            return ""
    
    def _create_performance_chart(self, report_data: Dict[str, Any], plt) -> str:
        """创建性能分布图表"""
        try:
            fig, ax = plt.subplots(figsize=(10, 6))
            
            accuracy = report_data['performance']['accuracy_rates']
            positions = ['百位', '十位', '个位', '和值', '跨度']
            accuracies = [
                accuracy['hundreds_accuracy'],
                accuracy['tens_accuracy'],
                accuracy['units_accuracy'],
                accuracy['sum_accuracy'],
                accuracy['span_accuracy']
            ]
            
            bars = ax.bar(positions, accuracies, color=['#ff9999', '#66b3ff', '#99ff99', '#ffcc99', '#ff99cc'])
            ax.set_ylabel('准确率')
            ax.set_title('各位置预测准确率')
            ax.set_ylim(0, 1)
            
            # 添加数值标签
            for bar, value in zip(bars, accuracies):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                       f'{value:.2%}', ha='center', va='bottom')
            
            plt.tight_layout()
            
            # 转换为base64
            buffer = BytesIO()
            plt.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
            buffer.seek(0)
            chart_base64 = base64.b64encode(buffer.getvalue()).decode()
            plt.close()
            
            return chart_base64
            
        except Exception as e:
            self.logger.error(f"创建性能图表失败: {e}")
            return ""
    
    def _generate_html_report(self, report_data: Dict[str, Any], 
                             charts: Dict[str, str]) -> str:
        """生成HTML报告"""
        try:
            # 替换模板中的占位符
            html_content = self.html_template
            
            # 基本信息
            html_content = html_content.replace('{{REPORT_TITLE}}', 'P8智能交集融合系统性能报告')
            html_content = html_content.replace('{{GENERATION_TIME}}', datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            html_content = html_content.replace('{{PERIOD_DAYS}}', str(report_data['period']['days']))
            
            # 摘要数据
            summary = report_data['summary']
            html_content = html_content.replace('{{TOTAL_PREDICTIONS}}', str(summary['total_predictions']))
            html_content = html_content.replace('{{TOTAL_EVALUATIONS}}', str(summary['total_evaluations']))
            html_content = html_content.replace('{{SUCCESS_RATE}}', f"{summary['success_rate']:.2%}")
            
            # 性能数据
            performance = report_data['performance']
            html_content = html_content.replace('{{EXACT_HIT_RATE}}', f"{performance['hit_rates']['exact_hit_rate']:.2%}")
            html_content = html_content.replace('{{TOP_K_HIT_RATE}}', f"{performance['hit_rates']['top_k_hit_rate']:.2%}")
            html_content = html_content.replace('{{AVG_OVERALL_SCORE}}', f"{performance['avg_overall_score']:.3f}")
            
            # 插入图表
            for chart_name, chart_data in charts.items():
                placeholder = f'{{{{CHART_{chart_name.upper()}}}}}'
                if chart_data:
                    img_tag = f'<img src="data:image/png;base64,{chart_data}" style="max-width: 100%; height: auto;">'
                    html_content = html_content.replace(placeholder, img_tag)
                else:
                    html_content = html_content.replace(placeholder, '<p>图表生成失败</p>')
            
            # 详细数据表格
            html_content = html_content.replace('{{DETAILED_DATA}}', self._generate_data_tables(report_data))
            
            return html_content
            
        except Exception as e:
            self.logger.error(f"生成HTML报告失败: {e}")
            return f"<html><body><h1>报告生成失败</h1><p>{e}</p></body></html>"
    
    def _generate_data_tables(self, report_data: Dict[str, Any]) -> str:
        """生成数据表格HTML"""
        tables_html = ""
        
        try:
            # 权重表格
            weights = report_data['weights']['current_weights']
            if weights:
                tables_html += "<h3>当前权重配置</h3>"
                tables_html += "<table class='data-table'>"
                tables_html += "<tr><th>预测器</th><th>权重</th><th>性能分数</th><th>准确率</th></tr>"
                
                for predictor, data in weights.items():
                    tables_html += f"<tr><td>{predictor}</td><td>{data['weight']:.3f}</td>"
                    tables_html += f"<td>{data['performance_score']:.3f}</td>"
                    tables_html += f"<td>{data['accuracy_rate']:.2%}</td></tr>"
                
                tables_html += "</table>"
            
            # 融合方法统计表格
            fusion_methods = report_data['predictions']['fusion_methods']
            if fusion_methods:
                tables_html += "<h3>融合方法使用统计</h3>"
                tables_html += "<table class='data-table'>"
                tables_html += "<tr><th>融合方法</th><th>使用次数</th><th>占比</th></tr>"
                
                total = sum(fusion_methods.values())
                for method, count in fusion_methods.items():
                    percentage = count / total * 100 if total > 0 else 0
                    tables_html += f"<tr><td>{method}</td><td>{count}</td><td>{percentage:.1f}%</td></tr>"
                
                tables_html += "</table>"
            
        except Exception as e:
            tables_html += f"<p>数据表格生成失败: {e}</p>"
        
        return tables_html
    
    def _get_html_template(self) -> str:
        """获取HTML报告模板"""
        return '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{REPORT_TITLE}}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .header { background: #f4f4f4; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 30px; }
        .metric-card { background: #fff; border: 1px solid #ddd; padding: 15px; border-radius: 5px; text-align: center; }
        .metric-value { font-size: 2em; font-weight: bold; color: #333; }
        .metric-label { color: #666; margin-top: 5px; }
        .chart-container { margin: 20px 0; text-align: center; }
        .data-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        .data-table th, .data-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        .data-table th { background-color: #f2f2f2; }
        h1, h2, h3 { color: #333; }
        .footer { margin-top: 40px; padding-top: 20px; border-top: 1px solid #ddd; color: #666; }
    </style>
</head>
<body>
    <div class="header">
        <h1>{{REPORT_TITLE}}</h1>
        <p>生成时间: {{GENERATION_TIME}} | 统计周期: {{PERIOD_DAYS}}天</p>
    </div>
    
    <div class="summary">
        <div class="metric-card">
            <div class="metric-value">{{TOTAL_PREDICTIONS}}</div>
            <div class="metric-label">总预测数</div>
        </div>
        <div class="metric-card">
            <div class="metric-value">{{TOTAL_EVALUATIONS}}</div>
            <div class="metric-label">总评估数</div>
        </div>
        <div class="metric-card">
            <div class="metric-value">{{SUCCESS_RATE}}</div>
            <div class="metric-label">成功率</div>
        </div>
        <div class="metric-card">
            <div class="metric-value">{{EXACT_HIT_RATE}}</div>
            <div class="metric-label">精确命中率</div>
        </div>
        <div class="metric-card">
            <div class="metric-value">{{TOP_K_HIT_RATE}}</div>
            <div class="metric-label">Top-K命中率</div>
        </div>
        <div class="metric-card">
            <div class="metric-value">{{AVG_OVERALL_SCORE}}</div>
            <div class="metric-label">平均综合评分</div>
        </div>
    </div>
    
    <h2>性能图表</h2>
    
    <div class="chart-container">
        <h3>命中率统计</h3>
        {{CHART_HIT_RATES}}
    </div>
    
    <div class="chart-container">
        <h3>趋势分析</h3>
        {{CHART_TRENDS}}
    </div>
    
    <div class="chart-container">
        <h3>权重分布</h3>
        {{CHART_WEIGHTS}}
    </div>
    
    <div class="chart-container">
        <h3>性能分布</h3>
        {{CHART_PERFORMANCE}}
    </div>
    
    <h2>详细数据</h2>
    {{DETAILED_DATA}}
    
    <div class="footer">
        <p>报告由P8智能交集融合系统自动生成</p>
    </div>
</body>
</html>
        '''
