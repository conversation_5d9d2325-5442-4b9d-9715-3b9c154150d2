# sum_predictor_config.yaml
# P6和值预测器配置文件
# 创建日期: 2025-01-14

# 数据库配置
database:
  path: "data/lottery.db"
  backup_path: "data/backups/"

# 和值预测器配置
sum_predictor:
  position: "sum"
  prediction_range: [0, 27]
  target_type: "regression"

  # 模型配置
  models:
    xgb:
      objective: "reg:squarederror"
      n_estimators: 200
      max_depth: 6
      learning_rate: 0.1
      subsample: 0.8
      colsample_bytree: 0.8
      reg_alpha: 0.1
      reg_lambda: 1.0
      random_state: 42
      n_jobs: -1

    lgb:
      objective: "regression"
      n_estimators: 200
      max_depth: 6
      learning_rate: 0.1
      subsample: 0.8
      colsample_bytree: 0.8
      reg_alpha: 0.1
      reg_lambda: 1.0
      random_state: 42
      n_jobs: -1

    lstm:
      sequence_length: 20
      hidden_units: 64
      dropout_rate: 0.2
      epochs: 100
      batch_size: 32
      learning_rate: 0.001
      validation_split: 0.2

    distribution:
      objective: "multi:softprob"
      num_class: 28  # 0-27共28个类别
      max_depth: 6
      learning_rate: 0.1
      n_estimators: 200
      random_state: 42

    constraint:
      position_weight: 0.7  # 位置预测权重
      sum_weight: 0.3       # 和值预测权重
      tolerance: 1.5        # 约束容忍度
      max_iterations: 100   # 最大迭代次数

    ensemble:
      weights:
        xgb: 0.3
        lgb: 0.3
        lstm: 0.2
        distribution: 0.1
        constraint: 0.1

  # 和值专属配置
  sum_specific:
    # 分布预测配置
    distribution:
      bins: 28  # 0-27
      smoothing_factor: 0.1
      min_frequency: 5

    # 约束优化配置
    constraint:
      position_weight: 0.7  # 位置预测权重
      sum_weight: 0.3       # 和值预测权重
      tolerance: 1.5        # 约束容忍度
      max_iterations: 100   # 最大迭代次数

    # 评估指标配置
    evaluation:
      mae_threshold: 1.5    # MAE阈值
      rmse_threshold: 2.0   # RMSE阈值
      accuracy_1_threshold: 0.6  # ±1准确率阈值
      accuracy_2_threshold: 0.85 # ±2准确率阈值
      r2_threshold: 0.6     # R²阈值

# 特征工程配置
feature_engineering:
  # 和值特征配置
  sum_features:
    - "sum_trend"           # 和值趋势
    - "sum_volatility"      # 和值波动性
    - "sum_distribution"    # 和值分布
    - "sum_seasonality"     # 和值季节性
    - "sum_correlation"     # 和值相关性

  # 窗口大小配置
  windows:
    short_term: 10
    medium_term: 30
    long_term: 100

  # 缓存配置
  cache:
    enabled: true
    max_size: 1000
    ttl: 3600  # 1小时

# 训练配置
training:
  # 数据分割
  train_ratio: 0.8
  validation_ratio: 0.1
  test_ratio: 0.1

  # 交叉验证
  cv_folds: 5
  cv_strategy: "time_series"

  # 早停配置
  early_stopping:
    enabled: true
    patience: 10
    min_delta: 0.001

# 预测配置
prediction:
  # 输出格式
  output_format:
    decimal_places: 2
    include_confidence: true
    include_range: true
    include_distribution: true
    include_constraints: true

  # 约束检查
  constraint_check:
    enabled: true
    strict_mode: false

# 性能监控配置
monitoring:
  # 性能指标
  metrics:
    - "mae"
    - "rmse"
    - "accuracy_1"
    - "accuracy_2"
    - "r2_score"
    - "distribution_accuracy"

  # 监控周期
  evaluation_periods:
    - "daily"
    - "weekly"
    - "monthly"

  # 告警配置
  alerts:
    mae_threshold: 2.0
    accuracy_drop_threshold: 0.1

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/sum_predictor.log"
  max_size: "10MB"
  backup_count: 5

# 模型保存配置
model_storage:
  base_path: "models/sum_predictor/"
  auto_save: true
  versioning: true
  compression: true
