#!/usr/bin/env python3
"""
P9智能闭环优化器

该模块实现P9闭环自动优化系统的核心优化器，负责：
1. 自动化优化流程控制
2. P8系统组件集成和调用
3. 优化任务的执行和监控
4. 性能阈值监控和触发机制

作者: Augment Code AI Assistant
创建日期: 2025-01-14
版本: 1.0.0
"""

import sqlite3
import json
import logging
import threading
import time
import yaml
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum
from pathlib import Path

class OptimizationTaskType(Enum):
    """优化任务类型枚举"""
    DATA_UPDATE = "data_update"
    PERFORMANCE_EVAL = "performance_eval"
    FUSION_WEIGHT_ADJUST = "fusion_weight_adjust"
    PARAMETER_TUNE = "parameter_tune"
    MODEL_RETRAIN = "model_retrain"
    SYSTEM_MAINTENANCE = "system_maintenance"

class OptimizationStatus(Enum):
    """优化状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class OptimizationTask:
    """优化任务数据类"""
    task_type: OptimizationTaskType
    component_name: Optional[str] = None
    priority: int = 5
    scheduled_time: Optional[datetime] = None
    dependencies: Optional[List[str]] = None
    max_retries: int = 3
    retry_count: int = 0
    status: OptimizationStatus = OptimizationStatus.PENDING

class IntelligentClosedLoopOptimizer:
    """智能闭环优化器 - P9核心组件"""
    
    def __init__(self, db_path: str, config_path: str = "config/"):
        """
        初始化智能闭环优化器
        
        Args:
            db_path: 数据库路径
            config_path: 配置文件路径
        """
        self.db_path = db_path
        self.config_path = Path(config_path)
        self.logger = logging.getLogger(__name__)
        
        # 运行状态
        self.is_running = False
        self.optimization_thread = None
        
        # 配置
        self.optimization_config = {}
        self.performance_thresholds = {}
        
        # P8组件引用
        self.fusion_predictor = None
        self.performance_monitor = None
        self.weight_adjuster = None
        self.auto_trigger = None
        self.unified_interface = None
        self.data_collector = None
        
        # 初始化
        self._load_configuration()
        self._initialize_p8_components()
        self._ensure_optimization_tables()
        
        self.logger.info("智能闭环优化器初始化完成")
    
    def _load_configuration(self):
        """加载配置文件"""
        try:
            config_file = self.config_path / "p9_config.yaml"
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                
                self.optimization_config = config.get('optimization', {})
                self.performance_thresholds = config.get('performance_thresholds', {})
                
                self.logger.info("P9配置加载成功")
            else:
                self.logger.warning("P9配置文件不存在，使用默认配置")
                self._create_default_config()
                
        except Exception as e:
            self.logger.error(f"加载P9配置失败: {e}")
            self._create_default_config()
    
    def _create_default_config(self):
        """创建默认配置"""
        self.optimization_config = {
            'enabled': True,
            'intervals': {
                'data_update': 3600,
                'performance_check': 1800
            }
        }
        
        self.performance_thresholds = {
            'hundreds_accuracy': 0.30,
            'fusion_hit_rate': 0.15
        }
    
    def _initialize_p8_components(self):
        """初始化P8组件"""
        try:
            # 尝试导入P8组件
            try:
                from ..fusion.fusion_predictor import FusionPredictor
                self.fusion_predictor = FusionPredictor(self.db_path)
                self.logger.info("FusionPredictor初始化成功")
            except ImportError:
                self.logger.warning("FusionPredictor导入失败")
            
            try:
                from ..fusion.performance_monitor import PerformanceMonitor
                # 创建默认监控配置
                monitor_config = {
                    'alert_thresholds': {
                        'cpu_usage': 80.0,
                        'memory_usage': 85.0,
                        'response_time': 1000.0
                    },
                    'monitoring_interval': 60,
                    'history_retention_days': 30
                }
                self.performance_monitor = PerformanceMonitor(self.db_path, monitor_config)
                self.logger.info("PerformanceMonitor初始化成功")
            except ImportError:
                self.logger.warning("PerformanceMonitor导入失败")
            except Exception as e:
                self.logger.error(f"PerformanceMonitor初始化失败: {e}")
            
            try:
                from ..fusion.dynamic_weight_adjuster import DynamicWeightAdjuster
                # 创建默认权重调整配置
                weight_config = {
                    'adjustment_strategy': 'adaptive',
                    'learning_rate': 0.01,
                    'momentum': 0.9,
                    'decay_rate': 0.95,
                    'min_weight': 0.1,
                    'max_weight': 0.9
                }
                self.weight_adjuster = DynamicWeightAdjuster(self.db_path, weight_config)
                self.logger.info("DynamicWeightAdjuster初始化成功")
            except ImportError:
                self.logger.warning("DynamicWeightAdjuster导入失败")
            except Exception as e:
                self.logger.error(f"DynamicWeightAdjuster初始化失败: {e}")
            
            try:
                from ..predictors.unified_predictor_interface import UnifiedPredictorInterface
                self.unified_interface = UnifiedPredictorInterface(self.db_path)
                self.logger.info("UnifiedPredictorInterface初始化成功")
            except ImportError:
                self.logger.warning("UnifiedPredictorInterface导入失败")
                
        except Exception as e:
            self.logger.error(f"P8组件初始化失败: {e}")
    
    def _ensure_optimization_tables(self):
        """确保优化相关表存在"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建优化日志表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS optimization_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    optimization_type TEXT NOT NULL,
                    trigger_reason TEXT NOT NULL,
                    component_name TEXT,
                    start_time TIMESTAMP NOT NULL,
                    end_time TIMESTAMP,
                    status TEXT NOT NULL DEFAULT 'running',
                    details TEXT,
                    performance_before TEXT,
                    performance_after TEXT,
                    improvement_score REAL,
                    rollback_reason TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 创建自动优化配置表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS auto_optimization_config (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    config_name TEXT NOT NULL UNIQUE,
                    config_type TEXT NOT NULL,
                    component_name TEXT,
                    config_value TEXT NOT NULL,
                    default_value TEXT,
                    min_value REAL,
                    max_value REAL,
                    is_active BOOLEAN DEFAULT TRUE,
                    auto_adjust BOOLEAN DEFAULT FALSE,
                    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_by TEXT DEFAULT 'system',
                    update_reason TEXT
                )
            """)
            
            conn.commit()
            conn.close()
            
            self.logger.info("优化数据库表初始化完成")
            
        except Exception as e:
            self.logger.error(f"优化数据库表初始化失败: {e}")
    
    def start_optimization_loop(self):
        """启动优化循环"""
        if self.is_running:
            self.logger.warning("优化循环已在运行")
            return
        
        self.is_running = True
        self.optimization_thread = threading.Thread(target=self._optimization_loop, daemon=True)
        self.optimization_thread.start()
        
        self.logger.info("智能闭环优化循环已启动")
    
    def stop_optimization_loop(self):
        """停止优化循环"""
        self.is_running = False
        if self.optimization_thread:
            self.optimization_thread.join(timeout=10)
        
        self.logger.info("智能闭环优化循环已停止")
    
    def _optimization_loop(self):
        """优化循环主逻辑"""
        while self.is_running:
            try:
                # 检查性能阈值
                if self._check_performance_thresholds():
                    self.logger.info("检测到性能下降，触发优化")
                    self._trigger_optimization("performance_degradation")
                
                # 定期数据更新检查
                if self._should_update_data():
                    self.logger.info("触发定期数据更新")
                    self._trigger_optimization("scheduled_update")
                
                # 休眠
                time.sleep(60)  # 每分钟检查一次
                
            except Exception as e:
                self.logger.error(f"优化循环异常: {e}")
                time.sleep(30)  # 异常时减少检查频率
    
    def _check_performance_thresholds(self) -> bool:
        """检查性能阈值"""
        try:
            if not self.performance_monitor:
                return False
            
            # 获取最新性能数据
            recent_performance = self.get_recent_performance()
            
            # 检查各项阈值
            for metric, threshold in self.performance_thresholds.items():
                current_value = recent_performance.get(metric, 0)
                if current_value < threshold:
                    self.logger.warning(f"性能指标 {metric} 低于阈值: {current_value} < {threshold}")
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"检查性能阈值失败: {e}")
            return False
    
    def _should_update_data(self) -> bool:
        """检查是否需要更新数据"""
        try:
            update_interval = self.optimization_config.get('intervals', {}).get('data_update', 3600)
            
            # 检查上次更新时间
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT MAX(start_time) FROM optimization_logs 
                WHERE optimization_type = 'data_update' AND status = 'completed'
            """)
            
            last_update = cursor.fetchone()[0]
            conn.close()
            
            if not last_update:
                return True
            
            last_update_time = datetime.fromisoformat(last_update)
            time_since_update = (datetime.now() - last_update_time).total_seconds()
            
            return time_since_update >= update_interval
            
        except Exception as e:
            self.logger.error(f"检查数据更新时间失败: {e}")
            return False
    
    def _trigger_optimization(self, trigger_reason: str):
        """触发优化"""
        try:
            # 记录优化开始
            optimization_id = self._log_optimization_start("auto_optimization", trigger_reason)
            
            # 执行优化逻辑
            success = self._execute_optimization(trigger_reason)
            
            # 记录优化结果
            self._log_optimization_end(optimization_id, success)
            
        except Exception as e:
            self.logger.error(f"触发优化失败: {e}")
    
    def _execute_optimization(self, trigger_reason: str) -> bool:
        """执行优化逻辑"""
        try:
            if trigger_reason == "performance_degradation":
                return self._handle_performance_degradation()
            elif trigger_reason == "scheduled_update":
                return self._handle_scheduled_update()
            else:
                self.logger.warning(f"未知的触发原因: {trigger_reason}")
                return False
                
        except Exception as e:
            self.logger.error(f"执行优化失败: {e}")
            return False
    
    def _handle_performance_degradation(self) -> bool:
        """处理性能下降"""
        try:
            # 调整融合权重
            if self.weight_adjuster:
                result = self.weight_adjuster.adjust_weights()
                if result.get('success', False):
                    self.logger.info("融合权重调整成功")
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"处理性能下降失败: {e}")
            return False
    
    def _handle_scheduled_update(self) -> bool:
        """处理定期更新"""
        try:
            # 更新数据
            if self.data_collector:
                result = self.data_collector.collect_latest_data()
                if result.get('success', False):
                    self.logger.info("数据更新成功")
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"处理定期更新失败: {e}")
            return False
    
    def get_recent_performance(self) -> Dict[str, float]:
        """获取最近的性能数据"""
        try:
            if self.performance_monitor:
                return self.performance_monitor.get_recent_performance()
            else:
                # 模拟性能数据
                return {
                    'hundreds_accuracy': 0.32,
                    'fusion_hit_rate': 0.16
                }
                
        except Exception as e:
            self.logger.error(f"获取性能数据失败: {e}")
            return {}
    
    def _log_optimization_start(self, optimization_type: str, trigger_reason: str) -> int:
        """记录优化开始"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO optimization_logs 
                (optimization_type, trigger_reason, start_time, status)
                VALUES (?, ?, ?, 'running')
            """, (optimization_type, trigger_reason, datetime.now().isoformat()))
            
            optimization_id = cursor.lastrowid
            conn.commit()
            conn.close()
            
            return optimization_id
            
        except Exception as e:
            self.logger.error(f"记录优化开始失败: {e}")
            return 0
    
    def _log_optimization_end(self, optimization_id: int, success: bool):
        """记录优化结束"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            status = 'completed' if success else 'failed'
            
            cursor.execute("""
                UPDATE optimization_logs 
                SET end_time = ?, status = ?, updated_at = ?
                WHERE id = ?
            """, (datetime.now().isoformat(), status, datetime.now().isoformat(), optimization_id))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"记录优化结束失败: {e}")
    
    def get_optimization_status(self) -> Dict[str, Any]:
        """获取优化状态"""
        return {
            'is_running': self.is_running,
            'optimization_config': self.optimization_config,
            'performance_thresholds': self.performance_thresholds,
            'p8_components': {
                'fusion_predictor': self.fusion_predictor is not None,
                'performance_monitor': self.performance_monitor is not None,
                'weight_adjuster': self.weight_adjuster is not None,
                'unified_interface': self.unified_interface is not None
            }
        }
