#!/usr/bin/env python3
"""
P8智能交集融合系统工具类

提供配置管理、日志设置、错误处理等辅助功能

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import os
import yaml
import json
import logging
import logging.handlers
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime, timedelta
from pathlib import Path
import sqlite3
import hashlib

class FusionConfigManager:
    """融合系统配置管理器"""
    
    def __init__(self, config_path: str = "config/fusion_config.yaml"):
        """
        初始化配置管理器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.config = self._load_config()
        self._validate_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            if Path(self.config_path).exists():
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                return config
            else:
                raise FileNotFoundError(f"配置文件不存在: {self.config_path}")
                
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'fusion': {
                'default_method': 'adaptive_fusion',
                'probability_weight': 0.5,
                'constraint_weight': 0.3,
                'diversity_weight': 0.2
            },
            'ranking': {
                'default_strategy': 'adaptive',
                'top_k': 20,
                'min_score_threshold': 0.01
            },
            'weights': {
                'learning_rate': 0.1,
                'decay_factor': 0.95,
                'min_weight': 0.1,
                'max_weight': 2.0
            },
            'constraints': {
                'sum_tolerance': 2.0,
                'span_tolerance': 1.0,
                'consistency_weight': 0.3
            },
            'database': {
                'path': 'data/lottery.db',
                'timeout': 30
            },
            'logging': {
                'level': 'INFO',
                'file': 'logs/fusion_system.log'
            }
        }
    
    def _validate_config(self):
        """验证配置有效性"""
        required_sections = ['fusion', 'ranking', 'weights', 'constraints', 'database', 'logging']
        
        for section in required_sections:
            if section not in self.config:
                raise ValueError(f"配置文件缺少必需的节: {section}")
        
        # 验证权重范围
        weights_config = self.config['weights']
        if weights_config['min_weight'] >= weights_config['max_weight']:
            raise ValueError("最小权重必须小于最大权重")
        
        # 验证概率权重和
        fusion_config = self.config['fusion']
        weight_sum = (fusion_config['probability_weight'] + 
                     fusion_config['constraint_weight'] + 
                     fusion_config['diversity_weight'])
        
        if abs(weight_sum - 1.0) > 0.1:
            print(f"警告: 融合权重和不等于1.0: {weight_sum}")
    
    def get(self, key_path: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key_path: 配置键路径，如 'fusion.default_method'
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key_path.split('.')
        value = self.config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key_path: str, value: Any):
        """
        设置配置值
        
        Args:
            key_path: 配置键路径
            value: 配置值
        """
        keys = key_path.split('.')
        config = self.config
        
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        
        config[keys[-1]] = value
    
    def save_config(self, backup: bool = True):
        """
        保存配置文件
        
        Args:
            backup: 是否备份原文件
        """
        try:
            if backup and Path(self.config_path).exists():
                backup_path = f"{self.config_path}.backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                Path(self.config_path).rename(backup_path)
            
            with open(self.config_path, 'w', encoding='utf-8') as f:
                yaml.dump(self.config, f, default_flow_style=False, allow_unicode=True)
                
        except Exception as e:
            raise RuntimeError(f"保存配置文件失败: {e}")

class FusionLogger:
    """融合系统日志管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化日志管理器
        
        Args:
            config: 日志配置
        """
        self.config = config
        self._setup_logging()
    
    def _setup_logging(self):
        """设置日志系统"""
        # 创建日志目录
        log_file = self.config.get('file', 'logs/fusion_system.log')
        log_dir = Path(log_file).parent
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # 设置日志级别
        level = getattr(logging, self.config.get('level', 'INFO').upper())
        
        # 设置日志格式
        formatter = logging.Formatter(
            self.config.get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        )
        
        # 配置根日志器
        root_logger = logging.getLogger()
        root_logger.setLevel(level)
        
        # 清除现有处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # 文件处理器（带轮转）
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=self.config.get('max_bytes', 10485760),  # 10MB
            backupCount=self.config.get('backup_count', 5)
        )
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)

class FusionValidator:
    """融合系统验证器"""
    
    @staticmethod
    def validate_issue(issue: str) -> bool:
        """验证期号格式"""
        if not issue or not isinstance(issue, str):
            return False
        
        # 基本长度检查
        if len(issue) < 6:
            return False
        
        # 数字检查
        if not issue.replace('-', '').isdigit():
            return False
        
        return True
    
    @staticmethod
    def validate_combination(combination: str) -> bool:
        """验证号码组合格式"""
        if not combination or len(combination) != 3:
            return False
        
        try:
            h, t, u = int(combination[0]), int(combination[1]), int(combination[2])
            return all(0 <= digit <= 9 for digit in [h, t, u])
        except (ValueError, IndexError):
            return False
    
    @staticmethod
    def validate_weights(weights: Dict[str, float]) -> bool:
        """验证权重配置"""
        if not weights:
            return False
        
        # 检查权重值范围
        for name, weight in weights.items():
            if not isinstance(weight, (int, float)) or weight < 0:
                return False
        
        return True
    
    @staticmethod
    def validate_database_connection(db_path: str) -> bool:
        """验证数据库连接"""
        try:
            with sqlite3.connect(db_path, timeout=5) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                return True
        except Exception:
            return False

class FusionMetrics:
    """融合系统指标计算器"""
    
    @staticmethod
    def calculate_hit_rate(predictions: List[Dict[str, Any]], 
                          actual_results: List[str]) -> float:
        """计算命中率"""
        if not predictions or not actual_results:
            return 0.0
        
        hits = 0
        total = min(len(predictions), len(actual_results))
        
        for i in range(total):
            pred = predictions[i]
            actual = actual_results[i]
            
            if len(actual) == 3:
                pred_combo = f"{pred['hundreds']}{pred['tens']}{pred['units']}"
                if pred_combo == actual:
                    hits += 1
        
        return hits / total if total > 0 else 0.0
    
    @staticmethod
    def calculate_top_k_hit_rate(predictions: List[List[Dict[str, Any]]], 
                                actual_results: List[str], k: int = 10) -> float:
        """计算Top-K命中率"""
        if not predictions or not actual_results:
            return 0.0
        
        hits = 0
        total = min(len(predictions), len(actual_results))
        
        for i in range(total):
            pred_list = predictions[i][:k]  # 取前K个
            actual = actual_results[i]
            
            if len(actual) == 3:
                for pred in pred_list:
                    pred_combo = f"{pred['hundreds']}{pred['tens']}{pred['units']}"
                    if pred_combo == actual:
                        hits += 1
                        break
        
        return hits / total if total > 0 else 0.0
    
    @staticmethod
    def calculate_confidence_accuracy(predictions: List[Dict[str, Any]], 
                                    actual_results: List[str]) -> float:
        """计算置信度准确性"""
        if not predictions or not actual_results:
            return 0.0
        
        confidence_errors = []
        total = min(len(predictions), len(actual_results))
        
        for i in range(total):
            pred = predictions[i]
            actual = actual_results[i]
            
            confidence = pred.get('confidence_level', 'medium')
            pred_combo = f"{pred['hundreds']}{pred['tens']}{pred['units']}"
            
            # 转换置信度为数值
            confidence_value = {'high': 0.8, 'medium': 0.5, 'low': 0.2}.get(confidence, 0.5)
            
            # 实际准确性
            actual_accuracy = 1.0 if pred_combo == actual else 0.0
            
            # 计算误差
            error = abs(confidence_value - actual_accuracy)
            confidence_errors.append(error)
        
        return 1.0 - (sum(confidence_errors) / len(confidence_errors)) if confidence_errors else 0.0

class FusionCache:
    """融合系统缓存管理器"""
    
    def __init__(self, max_size: int = 1000, ttl: int = 3600):
        """
        初始化缓存管理器
        
        Args:
            max_size: 最大缓存条目数
            ttl: 缓存过期时间（秒）
        """
        self.max_size = max_size
        self.ttl = ttl
        self.cache = {}
        self.timestamps = {}
    
    def _generate_key(self, *args, **kwargs) -> str:
        """生成缓存键"""
        key_data = str(args) + str(sorted(kwargs.items()))
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        if key not in self.cache:
            return None
        
        # 检查过期时间
        if datetime.now().timestamp() - self.timestamps[key] > self.ttl:
            self.delete(key)
            return None
        
        return self.cache[key]
    
    def set(self, key: str, value: Any):
        """设置缓存值"""
        # 检查缓存大小
        if len(self.cache) >= self.max_size:
            self._evict_oldest()
        
        self.cache[key] = value
        self.timestamps[key] = datetime.now().timestamp()
    
    def delete(self, key: str):
        """删除缓存值"""
        self.cache.pop(key, None)
        self.timestamps.pop(key, None)
    
    def clear(self):
        """清空缓存"""
        self.cache.clear()
        self.timestamps.clear()
    
    def _evict_oldest(self):
        """淘汰最旧的缓存条目"""
        if not self.timestamps:
            return
        
        oldest_key = min(self.timestamps.items(), key=lambda x: x[1])[0]
        self.delete(oldest_key)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        return {
            'size': len(self.cache),
            'max_size': self.max_size,
            'ttl': self.ttl,
            'hit_rate': getattr(self, '_hit_count', 0) / max(getattr(self, '_access_count', 1), 1)
        }
