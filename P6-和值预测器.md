# P6-和值预测器

## 项目概述
**前置条件**：P3-P5独立位置预测器完成
**核心目标**：基于BaseIndependentPredictor架构预测三位数和值0-27
**设计理念**：独立和值预测，与位置预测器协同约束优化
**预计时间**：1周

## 技术要求

### 预测目标
- **输入**：和值专用特征向量
- **输出**：0-27范围内的数值预测
- **约束**：预测值在[0,27]区间内
- **精度要求**：预测误差±1.5以内

### 模型架构（基于BaseIndependentPredictor）
- **XGBSumModel**：XGBoost回归器，处理非线性关系
- **LGBSumModel**：LightGBM回归器，快速训练和预测
- **LSTMSumModel**：LSTM回归器，捕获时序依赖
- **DistributionSumModel**：分布预测模型，预测和值概率分布（专属特征）
- **ConstraintSumModel**：约束优化模型，与位置预测协同（专属特征）
- **EnsembleSumModel**：集成融合模型，多模型加权融合

### 数据库设计（基于标准模式+专属特征）
```sql
-- 和值预测结果表（标准表结构+专属字段）
CREATE TABLE sum_predictions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    issue TEXT NOT NULL,
    model_type TEXT NOT NULL,           -- xgb/lgb/lstm/distribution/constraint/ensemble
    predicted_digit REAL NOT NULL,     -- 统一字段名：预测和值（0-27）
    confidence REAL NOT NULL,          -- 预测置信度
    probabilities TEXT,                 -- JSON格式：和值概率分布（专属）

    -- 和值专属字段
    prediction_range_min INTEGER,      -- 预测范围最小值
    prediction_range_max INTEGER,      -- 预测范围最大值
    distribution_entropy REAL,         -- 分布熵值（专属特征）
    constraint_score REAL,             -- 约束一致性分数（专属特征）

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 索引优化
    UNIQUE(issue, model_type)
);

-- 和值模型性能表（标准表结构+专属指标）
CREATE TABLE sum_model_performance (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    model_type TEXT NOT NULL,
    evaluation_period TEXT NOT NULL,
    accuracy REAL NOT NULL,             -- 标准字段：主要准确率指标

    -- 回归专属性能指标
    mae REAL NOT NULL,                  -- 平均绝对误差（专属）
    rmse REAL NOT NULL,                 -- 均方根误差（专属）
    accuracy_1 REAL NOT NULL,           -- ±1准确率（专属）
    accuracy_2 REAL NOT NULL,           -- ±2准确率（专属）
    r2_score REAL NOT NULL,             -- R²分数（专属）
    distribution_accuracy REAL,         -- 分布预测准确率（专属）

    -- 标准性能字段
    avg_confidence REAL,                -- 平均置信度
    training_time REAL,                 -- 训练时间
    prediction_time REAL,               -- 预测时间
    model_size INTEGER,                 -- 模型大小

    evaluated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 和值分布统计表（专属特征表）
CREATE TABLE sum_distribution_stats (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    sum_value INTEGER NOT NULL,         -- 和值（0-27）
    frequency INTEGER NOT NULL,         -- 出现频次
    probability REAL NOT NULL,          -- 概率
    avg_span REAL,                      -- 该和值的平均跨度
    common_patterns TEXT,               -- 常见组合模式(JSON)
    seasonal_frequency TEXT,            -- 季节性频次(JSON)
    correlation_with_positions TEXT,    -- 与位置预测的相关性(JSON)
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 索引和约束
    UNIQUE(sum_value),
    CHECK(sum_value >= 0 AND sum_value <= 27),
    CHECK(probability >= 0 AND probability <= 1)
);

-- 和值约束规则表（专属特征表）
CREATE TABLE sum_constraint_rules (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    rule_name TEXT NOT NULL,
    rule_description TEXT NOT NULL,
    rule_type TEXT NOT NULL,            -- 规则类型：position/sum/span/pattern

    -- 约束条件
    min_sum INTEGER,                    -- 最小和值
    max_sum INTEGER,                    -- 最大和值
    span_constraint TEXT,               -- 跨度约束条件(JSON)
    pattern_constraint TEXT,            -- 模式约束条件(JSON)
    position_constraint TEXT,           -- 位置约束条件(JSON)

    -- 规则配置
    weight REAL DEFAULT 1.0,            -- 规则权重
    priority INTEGER DEFAULT 1,         -- 规则优先级
    is_active BOOLEAN DEFAULT TRUE,     -- 是否启用

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 约束检查
    CHECK(weight >= 0 AND weight <= 1),
    CHECK(priority >= 1),
    CHECK(min_sum >= 0 AND max_sum <= 27),
    CHECK(min_sum <= max_sum)
);

-- 创建索引优化查询性能
CREATE INDEX idx_sum_predictions_issue ON sum_predictions(issue);
CREATE INDEX idx_sum_predictions_model_type ON sum_predictions(model_type);
CREATE INDEX idx_sum_predictions_created_at ON sum_predictions(created_at);

CREATE INDEX idx_sum_performance_model_type ON sum_model_performance(model_type);
CREATE INDEX idx_sum_performance_period ON sum_model_performance(evaluation_period);

CREATE INDEX idx_sum_distribution_value ON sum_distribution_stats(sum_value);
CREATE INDEX idx_sum_distribution_probability ON sum_distribution_stats(probability DESC);

CREATE INDEX idx_sum_rules_type ON sum_constraint_rules(rule_type);
CREATE INDEX idx_sum_rules_active ON sum_constraint_rules(is_active);
CREATE INDEX idx_sum_rules_priority ON sum_constraint_rules(priority DESC);
```

## 核心功能实现

### 1. 和值预测器主类（基于BaseIndependentPredictor）
```python
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
import sqlite3
import json
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import xgboost as xgb
import lightgbm as lgb
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout
from scipy import stats
from scipy.optimize import minimize

# 导入统一基类
from src.predictors.base_independent_predictor import BaseIndependentPredictor
from src.data.sum_data_access import SumDataAccess

class SumPredictor(BaseIndependentPredictor):
    def __init__(self, db_path: str):
        """
        初始化和值预测器

        Args:
            db_path: 数据库路径
        """
        # 继承BaseIndependentPredictor，position设为'sum'
        super().__init__('sum', db_path)

        # 和值预测器专属属性
        self.sum_distribution = None
        self.constraint_rules = None
        self.data_access = SumDataAccess(db_path)

        # 和值预测的特殊配置
        self.prediction_range = (0, 27)  # 和值范围0-27
        self.target_type = 'regression'  # 回归问题

        self.logger.info("初始化和值预测器完成")
        
    @abstractmethod
    def build_model(self):
        """构建模型"""
        pass
    
    @abstractmethod
    def train(self, X: np.ndarray, y: np.ndarray):
        """训练模型"""
        pass
    
    @abstractmethod
    def predict_sum(self, X: np.ndarray) -> np.ndarray:
        """预测和值"""
        pass
    
    def load_data(self) -> Tuple[np.ndarray, np.ndarray]:
        """加载和值训练数据"""
        conn = sqlite3.connect(self.db_path)
        
        feature_query = """
            SELECT fd.feature_vector, ld.sum_value
            FROM feature_data fd
            JOIN lottery_data ld ON fd.issue = ld.issue
            WHERE fd.feature_type = 'sum'
            ORDER BY fd.issue
        """
        
        cursor = conn.cursor()
        cursor.execute(feature_query)
        rows = cursor.fetchall()
        
        if not rows:
            raise ValueError("没有找到和值特征数据")
        
        X = []
        y = []
        
        for row in rows:
            feature_vector = json.loads(row[0])
            sum_target = row[1]
            
            X.append(feature_vector)
            y.append(sum_target)
        
        # 获取特征名称
        cursor.execute("""
            SELECT feature_names FROM feature_data 
            WHERE feature_type = 'sum' 
            LIMIT 1
        """)
        feature_names_row = cursor.fetchone()
        if feature_names_row:
            self.feature_names = json.loads(feature_names_row[0])
        
        conn.close()
        
        return np.array(X), np.array(y)
    
    def build_sum_distribution(self):
        """构建和值分布统计"""
        conn = sqlite3.connect(self.db_path)
        
        # 统计和值分布
        query = """
            SELECT sum_value, span, hundreds, tens, units, 
                   strftime('%m', draw_date) as month,
                   COUNT(*) as frequency
            FROM lottery_data
            GROUP BY sum_value, span, hundreds, tens, units, month
            ORDER BY sum_value
        """
        
        df = pd.read_sql_query(query, conn)
        
        # 计算每个和值的统计信息
        sum_stats = {}
        
        for sum_val in range(28):  # 0-27
            sum_data = df[df['sum_value'] == sum_val]
            
            if len(sum_data) > 0:
                frequency = sum_data['frequency'].sum()
                avg_span = sum_data['span'].mean()
                
                # 常见组合模式
                patterns = []
                for _, row in sum_data.iterrows():
                    pattern = f"{row['hundreds']}{row['tens']}{row['units']}"
                    patterns.append(pattern)
                
                # 季节性频次
                seasonal_freq = {}
                for month in range(1, 13):
                    month_data = sum_data[sum_data['month'] == str(month)]
                    seasonal_freq[str(month)] = month_data['frequency'].sum()
                
                sum_stats[sum_val] = {
                    'frequency': frequency,
                    'probability': frequency / df['frequency'].sum(),
                    'avg_span': avg_span,
                    'common_patterns': patterns[:10],  # 前10个常见模式
                    'seasonal_frequency': seasonal_freq
                }
            else:
                sum_stats[sum_val] = {
                    'frequency': 0,
                    'probability': 0,
                    'avg_span': 0,
                    'common_patterns': [],
                    'seasonal_frequency': {}
                }
        
        self.sum_distribution = sum_stats
        
        # 保存到数据库
        self.save_sum_distribution()
        
        conn.close()
        
        return sum_stats
    
    def save_sum_distribution(self):
        """保存和值分布到数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 清空旧数据
        cursor.execute("DELETE FROM sum_distribution_stats")
        
        for sum_val, stats in self.sum_distribution.items():
            cursor.execute("""
                INSERT INTO sum_distribution_stats 
                (sum_value, frequency, probability, avg_span, common_patterns, seasonal_frequency)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (sum_val, stats['frequency'], stats['probability'], 
                  stats['avg_span'], json.dumps(stats['common_patterns']), 
                  json.dumps(stats['seasonal_frequency'])))
        
        conn.commit()
        conn.close()
    
    def load_constraint_rules(self):
        """加载约束规则"""
        conn = sqlite3.connect(self.db_path)
        
        query = """
            SELECT rule_name, rule_description, min_sum, max_sum, 
                   span_constraint, pattern_constraint, weight
            FROM sum_constraint_rules
            WHERE is_active = TRUE
        """
        
        df = pd.read_sql_query(query, conn)
        self.constraint_rules = df.to_dict('records')
        
        conn.close()
        
        return self.constraint_rules
    
    def apply_constraints(self, predicted_sum: float, 
                         position_predictions: Optional[Dict] = None) -> float:
        """应用约束条件优化预测"""
        if self.constraint_rules is None:
            self.load_constraint_rules()
        
        # 基础约束：确保在[0,27]范围内
        constrained_sum = np.clip(predicted_sum, 0, 27)
        
        # 如果有位置预测，进行约束优化
        if position_predictions is not None:
            constrained_sum = self.optimize_with_position_constraints(
                constrained_sum, position_predictions
            )
        
        return constrained_sum
    
    def optimize_with_position_constraints(self, predicted_sum: float, 
                                         position_predictions: Dict) -> float:
        """基于位置预测的约束优化"""
        hundreds_prob = position_predictions.get('hundreds_prob')
        tens_prob = position_predictions.get('tens_prob')
        units_prob = position_predictions.get('units_prob')
        
        if (hundreds_prob is None or tens_prob is None or units_prob is None):
            return predicted_sum
        
        # 计算期望和值
        expected_sum = 0
        for h in range(10):
            for t in range(10):
                for u in range(10):
                    combination_sum = h + t + u
                    combination_prob = hundreds_prob[h] * tens_prob[t] * units_prob[u]
                    expected_sum += combination_sum * combination_prob
        
        # 加权融合预测和值与期望和值
        weight = 0.7  # 预测和值权重
        optimized_sum = weight * predicted_sum + (1 - weight) * expected_sum
        
        return np.clip(optimized_sum, 0, 27)
    
    def evaluate_model(self, X_test: np.ndarray, y_test: np.ndarray) -> Dict:
        """评估模型性能"""
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        y_pred = self.predict_sum(X_test)
        
        # 计算各种误差指标
        mae = mean_absolute_error(y_test, y_pred)
        rmse = np.sqrt(mean_squared_error(y_test, y_pred))
        r2 = r2_score(y_test, y_pred)
        
        # 计算不同精度的准确率
        accuracy_1 = np.mean(np.abs(y_test - y_pred) <= 1)
        accuracy_2 = np.mean(np.abs(y_test - y_pred) <= 2)
        
        return {
            'mae': mae,
            'rmse': rmse,
            'r2_score': r2,
            'accuracy_1': accuracy_1,
            'accuracy_2': accuracy_2
        }
```

### 2. XGBoost和值预测器
```python
class XGBSumPredictor(BaseSumPredictor):
    def __init__(self, db_path: str):
        super().__init__(db_path)
        self.model_params = {
            'objective': 'reg:squarederror',
            'max_depth': 6,
            'learning_rate': 0.1,
            'subsample': 0.8,
            'colsample_bytree': 0.8,
            'reg_alpha': 0.1,
            'reg_lambda': 1.0,
            'n_estimators': 200,
            'random_state': 42,
            'n_jobs': -1
        }
    
    def build_model(self):
        """构建XGBoost回归模型"""
        self.model = xgb.XGBRegressor(**self.model_params)
        return self.model
    
    def train(self, X: np.ndarray, y: np.ndarray):
        """训练XGBoost模型"""
        if self.model is None:
            self.build_model()
        
        # 构建和值分布
        self.build_sum_distribution()
        
        # 分割训练集和验证集
        X_train, X_val, y_train, y_val = train_test_split(
            X, y, test_size=0.2, random_state=42
        )
        
        # 训练模型
        self.model.fit(
            X_train, y_train,
            eval_set=[(X_val, y_val)],
            early_stopping_rounds=20,
            verbose=False
        )
        
        self.is_trained = True
        
        # 评估模型
        val_performance = self.evaluate_model(X_val, y_val)
        print(f"XGBoost和值预测器验证集性能: {val_performance}")
        
        return val_performance
    
    def predict_sum(self, X: np.ndarray) -> np.ndarray:
        """预测和值"""
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        predictions = self.model.predict(X)
        
        # 应用约束
        constrained_predictions = []
        for pred in predictions:
            constrained_pred = self.apply_constraints(pred)
            constrained_predictions.append(constrained_pred)
        
        return np.array(constrained_predictions)
    
    def predict_with_confidence(self, X: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """预测和值及置信度"""
        predictions = self.predict_sum(X)
        
        # 基于分布计算置信度
        confidences = []
        for pred in predictions:
            # 找到最接近的整数和值
            closest_sum = int(round(pred))
            closest_sum = np.clip(closest_sum, 0, 27)
            
            # 基于历史概率计算置信度
            if self.sum_distribution and closest_sum in self.sum_distribution:
                confidence = self.sum_distribution[closest_sum]['probability']
            else:
                confidence = 0.01  # 最小置信度
            
            confidences.append(confidence)
        
        return predictions, np.array(confidences)
```

### 3. 分布预测器
```python
class DistributionSumPredictor(BaseSumPredictor):
    def __init__(self, db_path: str):
        super().__init__(db_path)
        self.distribution_model = None
        
    def build_model(self):
        """构建分布预测模型"""
        # 使用多分类模型预测和值分布
        self.model = xgb.XGBClassifier(
            objective='multi:softprob',
            num_class=28,  # 0-27共28个类别
            max_depth=6,
            learning_rate=0.1,
            n_estimators=200,
            random_state=42
        )
        return self.model
    
    def train(self, X: np.ndarray, y: np.ndarray):
        """训练分布预测模型"""
        if self.model is None:
            self.build_model()
        
        self.build_sum_distribution()
        
        X_train, X_val, y_train, y_val = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        self.model.fit(
            X_train, y_train,
            eval_set=[(X_val, y_val)],
            early_stopping_rounds=20,
            verbose=False
        )
        
        self.is_trained = True
        
        # 评估分布预测准确率
        y_prob = self.model.predict_proba(X_val)
        y_pred = np.argmax(y_prob, axis=1)
        distribution_accuracy = np.mean(y_val == y_pred)
        
        val_performance = self.evaluate_model(X_val, y_val)
        val_performance['distribution_accuracy'] = distribution_accuracy
        
        print(f"分布和值预测器验证集性能: {val_performance}")
        
        return val_performance
    
    def predict_sum(self, X: np.ndarray) -> np.ndarray:
        """基于分布预测和值"""
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        # 获取概率分布
        probabilities = self.model.predict_proba(X)
        
        # 计算期望和值
        sum_values = np.arange(28)  # 0-27
        expected_sums = np.dot(probabilities, sum_values)
        
        return expected_sums
    
    def predict_distribution(self, X: np.ndarray) -> np.ndarray:
        """预测和值概率分布"""
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        return self.model.predict_proba(X)
    
    def get_prediction_range(self, X: np.ndarray, confidence_level: float = 0.8) -> List[Tuple[int, int]]:
        """获取预测范围"""
        probabilities = self.predict_distribution(X)
        
        ranges = []
        for prob in probabilities:
            # 找到累积概率达到confidence_level的范围
            sorted_indices = np.argsort(prob)[::-1]
            cumulative_prob = 0
            selected_sums = []
            
            for idx in sorted_indices:
                cumulative_prob += prob[idx]
                selected_sums.append(idx)
                if cumulative_prob >= confidence_level:
                    break
            
            if selected_sums:
                min_sum = min(selected_sums)
                max_sum = max(selected_sums)
                ranges.append((min_sum, max_sum))
            else:
                ranges.append((0, 27))
        
        return ranges
```

### 4. 约束优化预测器
```python
class ConstraintSumPredictor(BaseSumPredictor):
    def __init__(self, db_path: str):
        super().__init__(db_path)
        self.base_predictor = XGBSumPredictor(db_path)
        self.distribution_predictor = DistributionSumPredictor(db_path)
        
    def build_model(self):
        """构建约束优化模型"""
        self.base_predictor.build_model()
        self.distribution_predictor.build_model()
        self.load_constraint_rules()
        return self.base_predictor.model
    
    def train(self, X: np.ndarray, y: np.ndarray):
        """训练约束优化模型"""
        # 训练基础预测器
        base_performance = self.base_predictor.train(X, y)
        
        # 训练分布预测器
        dist_performance = self.distribution_predictor.train(X, y)
        
        self.is_trained = True
        
        return {
            'base': base_performance,
            'distribution': dist_performance
        }
    
    def predict_sum(self, X: np.ndarray) -> np.ndarray:
        """约束优化预测"""
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        # 获取基础预测
        base_predictions = self.base_predictor.predict_sum(X)
        
        # 获取分布预测
        dist_predictions = self.distribution_predictor.predict_sum(X)
        
        # 获取分布信息
        distributions = self.distribution_predictor.predict_distribution(X)
        
        # 约束优化融合
        optimized_predictions = []
        
        for i in range(len(X)):
            base_pred = base_predictions[i]
            dist_pred = dist_predictions[i]
            distribution = distributions[i]
            
            # 多目标优化
            def objective(x):
                # 目标1：接近基础预测
                base_loss = (x - base_pred) ** 2
                
                # 目标2：接近分布预测
                dist_loss = (x - dist_pred) ** 2
                
                # 目标3：符合历史分布
                x_int = int(round(np.clip(x, 0, 27)))
                dist_loss_hist = -np.log(distribution[x_int] + 1e-8)
                
                # 加权组合
                total_loss = 0.4 * base_loss + 0.3 * dist_loss + 0.3 * dist_loss_hist
                return total_loss
            
            # 约束优化
            result = minimize(objective, x0=base_pred, bounds=[(0, 27)], method='L-BFGS-B')
            optimized_pred = result.x[0]
            
            optimized_predictions.append(optimized_pred)
        
        return np.array(optimized_predictions)
    
    def predict_with_constraints(self, X: np.ndarray, 
                               position_predictions: Optional[Dict] = None) -> Dict:
        """带约束的完整预测"""
        base_pred = self.predict_sum(X)[0]
        
        # 应用位置约束
        if position_predictions:
            constrained_pred = self.optimize_with_position_constraints(
                base_pred, position_predictions
            )
        else:
            constrained_pred = base_pred
        
        # 获取预测范围
        ranges = self.distribution_predictor.get_prediction_range(X)
        pred_range = ranges[0] if ranges else (0, 27)
        
        # 计算置信度
        distributions = self.distribution_predictor.predict_distribution(X)
        closest_sum = int(round(np.clip(constrained_pred, 0, 27)))
        confidence = distributions[0][closest_sum]
        
        # 计算分布熵
        entropy = -np.sum(distributions[0] * np.log(distributions[0] + 1e-8))
        
        # 计算约束一致性分数
        constraint_score = self.calculate_constraint_score(
            constrained_pred, position_predictions
        )
        
        return {
            'predicted_sum': constrained_pred,
            'confidence': confidence,
            'prediction_range': pred_range,
            'distribution_entropy': entropy,
            'constraint_score': constraint_score,
            'distribution': distributions[0].tolist()
        }
    
    def calculate_constraint_score(self, predicted_sum: float, 
                                 position_predictions: Optional[Dict] = None) -> float:
        """计算约束一致性分数"""
        if position_predictions is None:
            return 0.5
        
        hundreds_prob = position_predictions.get('hundreds_prob')
        tens_prob = position_predictions.get('tens_prob')
        units_prob = position_predictions.get('units_prob')
        
        if (hundreds_prob is None or tens_prob is None or units_prob is None):
            return 0.5
        
        # 计算与位置预测的一致性
        consistency_scores = []
        target_sum = int(round(predicted_sum))
        
        for h in range(10):
            for t in range(10):
                for u in range(10):
                    if h + t + u == target_sum:
                        combination_prob = hundreds_prob[h] * tens_prob[t] * units_prob[u]
                        consistency_scores.append(combination_prob)
        
        # 返回所有可能组合的概率和
        return sum(consistency_scores) if consistency_scores else 0.0
```

### 5. 集成和值预测器
```python
class EnsembleSumPredictor:
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.xgb_predictor = XGBSumPredictor(db_path)
        self.lgb_predictor = LGBSumPredictor(db_path)  # 类似XGB实现
        self.distribution_predictor = DistributionSumPredictor(db_path)
        self.constraint_predictor = ConstraintSumPredictor(db_path)
        
        # 初始权重
        self.weights = {
            'xgb': 0.3,
            'lgb': 0.3,
            'distribution': 0.2,
            'constraint': 0.2
        }
        
        self.is_trained = False
    
    def train_all_models(self):
        """训练所有模型"""
        X, y = self.xgb_predictor.load_data()
        
        print("训练XGBoost和值预测器...")
        xgb_performance = self.xgb_predictor.train(X, y)
        
        print("训练LightGBM和值预测器...")
        lgb_performance = self.lgb_predictor.train(X, y)
        
        print("训练分布和值预测器...")
        dist_performance = self.distribution_predictor.train(X, y)
        
        print("训练约束优化和值预测器...")
        constraint_performance = self.constraint_predictor.train(X, y)
        
        # 根据性能调整权重
        self.adjust_weights(xgb_performance, lgb_performance, 
                          dist_performance, constraint_performance)
        
        self.is_trained = True
        
        return {
            'xgb': xgb_performance,
            'lgb': lgb_performance,
            'distribution': dist_performance,
            'constraint': constraint_performance,
            'weights': self.weights
        }
    
    def adjust_weights(self, xgb_perf: Dict, lgb_perf: Dict, 
                      dist_perf: Dict, constraint_perf: Dict):
        """根据性能调整权重"""
        # 使用MAE作为主要指标（越小越好）
        performances = {
            'xgb': 1 / (xgb_perf.get('mae', 1) + 0.1),
            'lgb': 1 / (lgb_perf.get('mae', 1) + 0.1),
            'distribution': 1 / (dist_perf.get('mae', 1) + 0.1),
            'constraint': 1 / (constraint_perf['base'].get('mae', 1) + 0.1)
        }
        
        total_perf = sum(performances.values())
        
        if total_perf > 0:
            for model_name in self.weights:
                self.weights[model_name] = performances[model_name] / total_perf
        
        print(f"调整后的和值预测器权重: {self.weights}")
    
    def predict_sum(self, X: np.ndarray, 
                   position_predictions: Optional[Dict] = None) -> np.ndarray:
        """集成预测和值"""
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        # 获取各模型预测
        xgb_pred = self.xgb_predictor.predict_sum(X)
        lgb_pred = self.lgb_predictor.predict_sum(X)
        dist_pred = self.distribution_predictor.predict_sum(X)
        
        # 约束预测需要位置信息
        if position_predictions:
            constraint_pred = self.constraint_predictor.predict_with_constraints(
                X, position_predictions
            )['predicted_sum']
            constraint_pred = np.array([constraint_pred])
        else:
            constraint_pred = self.constraint_predictor.predict_sum(X)
        
        # 加权融合
        ensemble_pred = (
            self.weights['xgb'] * xgb_pred +
            self.weights['lgb'] * lgb_pred +
            self.weights['distribution'] * dist_pred +
            self.weights['constraint'] * constraint_pred
        )
        
        return ensemble_pred
    
    def predict_next_period(self, issue: str, 
                          position_predictions: Optional[Dict] = None) -> Dict:
        """预测下一期和值"""
        # 获取最新特征
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT feature_vector FROM feature_data 
            WHERE feature_type = 'sum' 
            ORDER BY issue DESC 
            LIMIT 1
        """)
        
        row = cursor.fetchone()
        if not row:
            raise ValueError("没有找到最新的和值特征数据")
        
        latest_features = np.array(json.loads(row[0])).reshape(1, -1)
        
        # 预测和值
        predicted_sum = self.predict_sum(latest_features, position_predictions)[0]
        
        # 获取详细预测信息
        detailed_prediction = self.constraint_predictor.predict_with_constraints(
            latest_features, position_predictions
        )
        
        # 保存预测结果
        self.save_prediction(issue, detailed_prediction)
        
        conn.close()
        
        return {
            'issue': issue,
            'predicted_sum': predicted_sum,
            'confidence': detailed_prediction['confidence'],
            'prediction_range': detailed_prediction['prediction_range'],
            'distribution_entropy': detailed_prediction['distribution_entropy'],
            'constraint_score': detailed_prediction['constraint_score']
        }
    
    def save_prediction(self, issue: str, prediction_details: Dict):
        """保存预测结果"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            INSERT OR REPLACE INTO sum_predictions 
            (issue, model_type, predicted_sum, confidence, 
             prediction_range_min, prediction_range_max, 
             distribution_entropy, constraint_score)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (issue, 'ensemble', prediction_details['predicted_sum'], 
              prediction_details['confidence'], 
              prediction_details['prediction_range'][0],
              prediction_details['prediction_range'][1],
              prediction_details['distribution_entropy'],
              prediction_details['constraint_score']))
        
        conn.commit()
        conn.close()
```

## 成功标准

### 模型性能
- [ ] XGBoost MAE < 1.5
- [ ] LightGBM MAE < 1.5
- [ ] 分布预测准确率 > 60%
- [ ] 约束优化MAE < 1.3
- [ ] 集成模型MAE < 1.2

### 预测精度
- [ ] ±1准确率 > 60%
- [ ] ±2准确率 > 85%
- [ ] R²分数 > 0.6
- [ ] 约束一致性分数 > 0.7

### 系统稳定性
- [ ] 预测值在合理范围内
- [ ] 约束优化收敛稳定
- [ ] 分布预测合理

## 标准组件设计

### 1. SumDataAccess数据访问层
```python
#!/usr/bin/env python3
"""
和值预测器数据访问层

提供和值预测器相关的数据库操作功能，包括：
- 预测结果的保存和查询
- 模型性能数据的管理
- 和值分布统计和约束规则管理

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import sqlite3
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from pathlib import Path

class SumDataAccess:
    """和值预测器数据访问类"""

    def __init__(self, db_path: str):
        """
        初始化数据访问层

        Args:
            db_path: 数据库文件路径
        """
        self.db_path = db_path
        self.logger = logging.getLogger("SumDataAccess")

        # 验证数据库文件存在
        if not Path(db_path).exists():
            raise FileNotFoundError(f"数据库文件不存在: {db_path}")

        # 验证必要的表是否存在
        self._verify_tables()

    def save_prediction_result(self, prediction_result: Dict[str, Any]) -> bool:
        """
        保存预测结果

        Args:
            prediction_result: 预测结果字典

        Returns:
            保存是否成功
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                INSERT INTO sum_predictions
                (issue, model_type, predicted_sum, confidence,
                 prediction_range_min, prediction_range_max,
                 distribution_entropy, constraint_score)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                prediction_result['issue'],
                prediction_result['model_type'],
                prediction_result['predicted_sum'],
                prediction_result['confidence'],
                prediction_result.get('prediction_range_min'),
                prediction_result.get('prediction_range_max'),
                prediction_result.get('distribution_entropy'),
                prediction_result.get('constraint_score')
            ))

            conn.commit()
            conn.close()

            self.logger.info(f"保存预测结果成功: {prediction_result['issue']}")
            return True

        except Exception as e:
            self.logger.error(f"保存预测结果失败: {e}")
            return False

    def get_prediction_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """
        获取预测历史

        Args:
            limit: 返回记录数量限制

        Returns:
            预测历史列表
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT issue, model_type, predicted_sum, confidence,
                       prediction_range_min, prediction_range_max,
                       distribution_entropy, constraint_score, created_at
                FROM sum_predictions
                ORDER BY created_at DESC
                LIMIT ?
            """, (limit,))

            rows = cursor.fetchall()
            conn.close()

            results = []
            for row in rows:
                results.append({
                    'issue': row[0],
                    'model_type': row[1],
                    'predicted_sum': row[2],
                    'confidence': row[3],
                    'prediction_range_min': row[4],
                    'prediction_range_max': row[5],
                    'distribution_entropy': row[6],
                    'constraint_score': row[7],
                    'created_at': row[8]
                })

            return results

        except Exception as e:
            self.logger.error(f"获取预测历史失败: {e}")
            return []

    def save_performance_metrics(self, performance_data: Dict[str, Any]) -> bool:
        """
        保存模型性能指标

        Args:
            performance_data: 性能数据字典

        Returns:
            保存是否成功
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                INSERT INTO sum_model_performance
                (model_type, evaluation_period, mae, rmse, accuracy_1,
                 accuracy_2, r2_score, distribution_accuracy)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                performance_data['model_type'],
                performance_data['evaluation_period'],
                performance_data['mae'],
                performance_data['rmse'],
                performance_data['accuracy_1'],
                performance_data['accuracy_2'],
                performance_data['r2_score'],
                performance_data.get('distribution_accuracy', 0.0)
            ))

            conn.commit()
            conn.close()

            self.logger.info(f"保存性能指标成功: {performance_data['model_type']}")
            return True

        except Exception as e:
            self.logger.error(f"保存性能指标失败: {e}")
            return False

    def get_performance_history(self, model_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取性能历史"""
        # 实现获取性能历史的逻辑
        pass

    def get_accuracy_statistics(self, days: int = 30) -> Dict[str, float]:
        """获取准确率统计"""
        # 实现准确率统计的逻辑
        pass

    def update_sum_distribution(self, sum_stats: Dict[int, Dict]) -> bool:
        """更新和值分布统计"""
        # 实现和值分布更新的逻辑
        pass

    def get_constraint_rules(self) -> List[Dict[str, Any]]:
        """获取约束规则"""
        # 实现约束规则获取的逻辑
        pass

    def _verify_tables(self):
        """验证必要的表是否存在"""
        required_tables = ['sum_predictions', 'sum_model_performance',
                          'sum_distribution_stats', 'sum_constraint_rules']

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        for table in required_tables:
            cursor.execute("""
                SELECT name FROM sqlite_master
                WHERE type='table' AND name=?
            """, (table,))

            if not cursor.fetchone():
                self.logger.warning(f"表 {table} 不存在，将创建")
                self._create_table(cursor, table)

        conn.commit()
        conn.close()
```

### 2. 训练脚本设计
```python
#!/usr/bin/env python3
"""
和值预测器训练脚本

使用方法:
python scripts/train_sum_predictor.py --model all --save-models
python scripts/train_sum_predictor.py --model xgb --epochs 100
python scripts/train_sum_predictor.py --model ensemble --validate

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import sys
import argparse
import logging
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.predictors.sum_predictor import SumPredictor
from config.config_loader import load_config

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('logs/train_sum_predictor.log'),
            logging.StreamHandler()
        ]
    )

def train_model(model_type: str, config: dict, save_models: bool = False):
    """训练指定模型"""
    logger = logging.getLogger("TrainSumPredictor")

    try:
        # 初始化预测器
        predictor = SumPredictor(config['database']['path'])

        if model_type == 'all':
            # 训练所有模型
            logger.info("开始训练所有和值预测模型")
            performance = predictor.train_all_models()

            for model_name, metrics in performance.items():
                logger.info(f"{model_name} - MAE: {metrics['mae']:.3f}, "
                           f"RMSE: {metrics['rmse']:.3f}, "
                           f"±1准确率: {metrics['accuracy_1']:.3f}")

        elif model_type == 'xgb':
            # 训练XGBoost模型
            logger.info("训练XGBoost和值预测模型")
            performance = predictor.train_xgb_model()

        elif model_type == 'lgb':
            # 训练LightGBM模型
            logger.info("训练LightGBM和值预测模型")
            performance = predictor.train_lgb_model()

        elif model_type == 'lstm':
            # 训练LSTM模型
            logger.info("训练LSTM和值预测模型")
            performance = predictor.train_lstm_model()

        elif model_type == 'ensemble':
            # 训练集成模型
            logger.info("训练集成和值预测模型")
            performance = predictor.train_ensemble_model()

        else:
            raise ValueError(f"不支持的模型类型: {model_type}")

        # 保存模型
        if save_models:
            predictor.save_all_models()
            logger.info("模型保存完成")

        logger.info("训练完成")
        return True

    except Exception as e:
        logger.error(f"训练失败: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='和值预测器训练脚本')
    parser.add_argument('--model', type=str, default='all',
                       choices=['all', 'xgb', 'lgb', 'lstm', 'ensemble'],
                       help='要训练的模型类型')
    parser.add_argument('--save-models', action='store_true',
                       help='是否保存训练好的模型')
    parser.add_argument('--config', type=str,
                       default='config/sum_predictor_config.yaml',
                       help='配置文件路径')

    args = parser.parse_args()

    # 设置日志
    setup_logging()
    logger = logging.getLogger("TrainSumPredictor")

    # 加载配置
    try:
        config = load_config(args.config)
        logger.info(f"加载配置文件: {args.config}")
    except Exception as e:
        logger.error(f"加载配置文件失败: {e}")
        return False

    # 训练模型
    success = train_model(args.model, config, args.save_models)

    if success:
        logger.info("🎉 和值预测器训练成功完成!")
        return True
    else:
        logger.error("❌ 和值预测器训练失败!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
```

### 3. 预测脚本设计
```python
#!/usr/bin/env python3
"""
和值预测脚本

使用方法:
python scripts/predict_sum.py --issue 2025206 --model ensemble
python scripts/predict_sum.py --issue 2025206 --model all --show-details
python scripts/predict_sum.py --history 20

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import sys
import argparse
import logging
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.predictors.sum_predictor import SumPredictor
from config.config_loader import load_config

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def predict_sum(issue: str, model_type: str, config: dict, show_details: bool = False):
    """预测和值"""
    logger = logging.getLogger("PredictSum")

    try:
        # 初始化预测器
        predictor = SumPredictor(config['database']['path'])

        # 加载模型
        predictor.load_all_models()

        if model_type == 'all':
            # 使用所有模型预测
            logger.info(f"使用所有模型预测期号 {issue} 的和值")

            results = {}
            for model in ['xgb', 'lgb', 'lstm', 'ensemble']:
                result = predictor.predict_next_period(issue, model)
                results[model] = result

                print(f"{model.upper()}模型预测: {result['predicted_sum']:.2f} "
                      f"(置信度: {result['confidence']:.3f})")

            # 显示详细信息
            if show_details:
                ensemble_result = results['ensemble']
                print(f"\n详细信息:")
                print(f"预测范围: {ensemble_result.get('prediction_range', 'N/A')}")
                print(f"分布熵值: {ensemble_result.get('distribution_entropy', 'N/A'):.3f}")
                print(f"约束分数: {ensemble_result.get('constraint_score', 'N/A'):.3f}")

        else:
            # 使用指定模型预测
            logger.info(f"使用{model_type}模型预测期号 {issue} 的和值")

            result = predictor.predict_next_period(issue, model_type)

            print(f"期号: {issue}")
            print(f"预测和值: {result['predicted_sum']:.2f}")
            print(f"置信度: {result['confidence']:.3f}")

            if show_details and 'prediction_range' in result:
                print(f"预测范围: {result['prediction_range']}")
                print(f"分布熵值: {result.get('distribution_entropy', 'N/A'):.3f}")
                print(f"约束分数: {result.get('constraint_score', 'N/A'):.3f}")

        return True

    except Exception as e:
        logger.error(f"预测失败: {e}")
        return False

def show_prediction_history(limit: int, config: dict):
    """显示预测历史"""
    logger = logging.getLogger("PredictSum")

    try:
        predictor = SumPredictor(config['database']['path'])
        history = predictor.data_access.get_prediction_history(limit)

        if not history:
            print("没有找到预测历史记录")
            return True

        print(f"\n最近 {len(history)} 次和值预测记录:")
        print("-" * 80)
        print(f"{'期号':<10} {'模型':<10} {'预测值':<8} {'置信度':<8} {'预测时间':<20}")
        print("-" * 80)

        for record in history:
            print(f"{record['issue']:<10} {record['model_type']:<10} "
                  f"{record['predicted_sum']:<8.2f} {record['confidence']:<8.3f} "
                  f"{record['created_at']:<20}")

        return True

    except Exception as e:
        logger.error(f"获取预测历史失败: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='和值预测脚本')
    parser.add_argument('--issue', type=str, help='要预测的期号')
    parser.add_argument('--model', type=str, default='ensemble',
                       choices=['xgb', 'lgb', 'lstm', 'ensemble', 'all'],
                       help='使用的模型类型')
    parser.add_argument('--show-details', action='store_true',
                       help='显示详细预测信息')
    parser.add_argument('--history', type=int, help='显示预测历史记录数量')
    parser.add_argument('--config', type=str,
                       default='config/sum_predictor_config.yaml',
                       help='配置文件路径')

    args = parser.parse_args()

    # 设置日志
    setup_logging()
    logger = logging.getLogger("PredictSum")

    # 加载配置
    try:
        config = load_config(args.config)
        logger.info(f"加载配置文件: {args.config}")
    except Exception as e:
        logger.error(f"加载配置文件失败: {e}")
        return False

    # 执行操作
    if args.history:
        # 显示预测历史
        success = show_prediction_history(args.history, config)
    elif args.issue:
        # 执行预测
        success = predict_sum(args.issue, args.model, config, args.show_details)
    else:
        parser.print_help()
        return False

    if success:
        logger.info("🎉 操作成功完成!")
        return True
    else:
        logger.error("❌ 操作失败!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
```

### 4. 配置文件设计
```yaml
# sum_predictor_config.yaml
# 和值预测器配置文件

# 数据库配置
database:
  path: "data/lottery.db"
  backup_path: "data/backups/"

# 和值预测器配置
sum_predictor:
  position: "sum"
  prediction_range: [0, 27]
  target_type: "regression"

  # 模型配置
  models:
    xgb:
      n_estimators: 200
      max_depth: 6
      learning_rate: 0.1
      subsample: 0.8
      colsample_bytree: 0.8
      random_state: 42

    lgb:
      n_estimators: 200
      max_depth: 6
      learning_rate: 0.1
      subsample: 0.8
      colsample_bytree: 0.8
      random_state: 42

    lstm:
      sequence_length: 20
      hidden_units: 64
      dropout_rate: 0.2
      epochs: 100
      batch_size: 32
      learning_rate: 0.001

    ensemble:
      weights:
        xgb: 0.3
        lgb: 0.3
        lstm: 0.2
        distribution: 0.1
        constraint: 0.1

  # 和值专属配置
  sum_specific:
    # 分布预测配置
    distribution:
      bins: 28  # 0-27
      smoothing_factor: 0.1
      min_frequency: 5

    # 约束优化配置
    constraint:
      position_weight: 0.7  # 位置预测权重
      sum_weight: 0.3       # 和值预测权重
      tolerance: 1.5        # 约束容忍度
      max_iterations: 100   # 最大迭代次数

    # 评估指标配置
    evaluation:
      mae_threshold: 1.5    # MAE阈值
      rmse_threshold: 2.0   # RMSE阈值
      accuracy_1_threshold: 0.6  # ±1准确率阈值
      accuracy_2_threshold: 0.85 # ±2准确率阈值
      r2_threshold: 0.6     # R²阈值

# 特征工程配置
feature_engineering:
  # 和值特征配置
  sum_features:
    - "sum_trend"           # 和值趋势
    - "sum_volatility"      # 和值波动性
    - "sum_distribution"    # 和值分布
    - "sum_seasonality"     # 和值季节性
    - "sum_correlation"     # 和值相关性

  # 窗口大小配置
  windows:
    short_term: 10
    medium_term: 30
    long_term: 100

  # 缓存配置
  cache:
    enabled: true
    max_size: 1000
    ttl: 3600  # 1小时

# 训练配置
training:
  # 数据分割
  train_ratio: 0.8
  validation_ratio: 0.1
  test_ratio: 0.1

  # 交叉验证
  cv_folds: 5
  cv_strategy: "time_series"

  # 早停配置
  early_stopping:
    enabled: true
    patience: 10
    min_delta: 0.001

# 预测配置
prediction:
  # 输出格式
  output_format:
    decimal_places: 2
    include_confidence: true
    include_range: true
    include_distribution: true
    include_constraints: true

  # 约束检查
  constraint_check:
    enabled: true
    strict_mode: false

# 性能监控配置
monitoring:
  # 性能指标
  metrics:
    - "mae"
    - "rmse"
    - "accuracy_1"
    - "accuracy_2"
    - "r2_score"
    - "distribution_accuracy"

  # 监控周期
  evaluation_periods:
    - "daily"
    - "weekly"
    - "monthly"

  # 告警配置
  alerts:
    mae_threshold: 2.0
    accuracy_drop_threshold: 0.1

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/sum_predictor.log"
  max_size: "10MB"
  backup_count: 5

# 模型保存配置
model_storage:
  base_path: "models/sum_predictor/"
  auto_save: true
  versioning: true
  compression: true
```

## P6专属特征保留验证 ✅

### 1. 约束优化功能完整保留 ✅
- **位置约束优化**: `optimize_with_position_constraints()` - 与P3-P5位置预测协同
- **约束规则管理**: `sum_constraint_rules` 表 - 存储和管理约束规则
- **约束一致性评分**: `constraint_score` 字段 - 量化约束满足程度
- **多目标优化**: `ConstraintSumPredictor` - 基于scipy.optimize的约束优化

### 2. 分布预测功能完整保留 ✅
- **概率分布预测**: `DistributionSumPredictor` - 预测0-27的完整概率分布
- **分布统计管理**: `sum_distribution_stats` 表 - 历史分布统计
- **分布熵计算**: `distribution_entropy` 字段 - 预测不确定性量化
- **期望值计算**: 基于概率分布的期望和值计算

### 3. 回归评估指标完整保留 ✅
- **MAE (平均绝对误差)**: 主要回归评估指标
- **RMSE (均方根误差)**: 回归模型标准评估
- **±1准确率**: 和值预测特有的精度指标
- **±2准确率**: 和值预测特有的容忍度指标
- **R²分数**: 回归模型拟合度评估

### 4. 和值数学特性完整保留 ✅
- **预测范围**: 0-27（28个可能值）
- **回归问题**: `target_type = 'regression'`
- **数学约束**: `h + t + u = sum` 的约束关系
- **范围约束**: `prediction_range = (0, 27)`

### 5. 专属配置和接口完整保留 ✅
- **专属配置**: `sum_specific` 配置节，包含分布和约束配置
- **专属特征**: 和值趋势、波动性、分布、季节性、相关性
- **专属方法**: 17个标准方法 + 和值专属方法
- **专属数据访问**: `SumDataAccess` 类，9个标准方法 + 专属方法

## 部署说明

```python
# 使用示例
from src.predictors.sum_predictor import SumPredictor

# 初始化预测器
predictor = SumPredictor("data/lottery.db")

# 训练所有模型
performance = predictor.train_all_models()
print(f"和值预测器训练完成: {performance}")

# 预测下一期（可选位置预测信息）
position_predictions = {
    'hundreds_prob': np.array([0.1, 0.2, 0.15, 0.1, 0.05, 0.1, 0.1, 0.05, 0.1, 0.05]),
    'tens_prob': np.array([0.05, 0.1, 0.2, 0.15, 0.1, 0.1, 0.1, 0.05, 0.1, 0.05]),
    'units_prob': np.array([0.08, 0.12, 0.18, 0.12, 0.08, 0.12, 0.12, 0.08, 0.05, 0.05])
}

prediction = predictor.predict_next_period("2024001", position_predictions)
print(f"和值预测结果: {prediction}")
```

## 下一步
完成P6后，进入**P7-跨度预测器**开发阶段。
