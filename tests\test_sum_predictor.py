#!/usr/bin/env python3
"""
P6和值预测器单元测试

测试数据访问层、模型、主预测器等各个组件
确保所有功能正常工作

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import unittest
import sys
import os
import tempfile
import sqlite3
import numpy as np
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data.sum_data_access import SumDataAccess
from src.predictors.sum_predictor import SumPredictor
from src.predictors.models.xgb_sum_model import XGBSumModel
from src.predictors.models.lgb_sum_model import LGBSumModel
from src.predictors.models.distribution_sum_model import DistributionSumModel

class TestSumDataAccess(unittest.TestCase):
    """测试和值数据访问层"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建临时数据库
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.db_path = self.temp_db.name
        self.temp_db.close()
        
        # 创建测试数据库结构
        self._create_test_database()
        
        # 初始化数据访问层
        self.data_access = SumDataAccess(self.db_path)
    
    def tearDown(self):
        """清理测试环境"""
        if os.path.exists(self.db_path):
            os.unlink(self.db_path)
    
    def _create_test_database(self):
        """创建测试数据库结构"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建lottery_data表
        cursor.execute('''
            CREATE TABLE lottery_data (
                id INTEGER PRIMARY KEY,
                issue TEXT,
                hundreds INTEGER,
                tens INTEGER,
                units INTEGER,
                draw_date TEXT
            )
        ''')
        
        # 插入测试数据
        test_data = [
            ('2024001', 1, 2, 3, '2024-01-01'),
            ('2024002', 4, 5, 6, '2024-01-02'),
            ('2024003', 7, 8, 9, '2024-01-03'),
            ('2024004', 0, 1, 2, '2024-01-04'),
            ('2024005', 3, 4, 5, '2024-01-05'),
        ]
        
        cursor.executemany(
            'INSERT INTO lottery_data (issue, hundreds, tens, units, draw_date) VALUES (?, ?, ?, ?, ?)',
            test_data
        )
        
        # 创建和值预测相关表
        cursor.execute('''
            CREATE TABLE sum_predictions (
                id INTEGER PRIMARY KEY,
                issue TEXT,
                model_type TEXT,
                predicted_digit REAL,
                confidence REAL,
                probabilities TEXT,
                prediction_range_min INTEGER,
                prediction_range_max INTEGER,
                distribution_entropy REAL,
                constraint_score REAL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(issue, model_type)
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE sum_model_performance (
                id INTEGER PRIMARY KEY,
                model_type TEXT,
                evaluation_period TEXT,
                accuracy REAL,
                mae REAL,
                rmse REAL,
                accuracy_1 REAL,
                accuracy_2 REAL,
                r2_score REAL,
                distribution_accuracy REAL,
                avg_confidence REAL,
                training_time REAL,
                prediction_time REAL,
                model_size INTEGER,
                evaluated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def test_save_prediction_result(self):
        """测试保存预测结果"""
        prediction_result = {
            'issue': '2024006',
            'model_type': 'xgb',
            'predicted_digit': 15.5,
            'confidence': 0.85,
            'prediction_range_min': 13,
            'prediction_range_max': 18,
            'distribution_entropy': 2.1,
            'constraint_score': 0.9,
            'probabilities': [0.1, 0.2, 0.3, 0.4]
        }
        
        result = self.data_access.save_prediction_result(prediction_result)
        self.assertTrue(result)
    
    def test_get_prediction_history(self):
        """测试获取预测历史"""
        # 先保存一些预测结果
        for i in range(3):
            prediction_result = {
                'issue': f'202400{6+i}',
                'model_type': 'test',
                'predicted_digit': 10 + i,
                'confidence': 0.8,
            }
            self.data_access.save_prediction_result(prediction_result)
        
        # 获取历史记录
        history = self.data_access.get_prediction_history(limit=5)
        self.assertGreaterEqual(len(history), 3)
        self.assertEqual(history[0]['model_type'], 'test')
    
    def test_save_performance_metrics(self):
        """测试保存性能指标"""
        performance_data = {
            'model_type': 'xgb',
            'evaluation_period': '2024-01',
            'accuracy': 0.75,
            'mae': 1.2,
            'rmse': 1.8,
            'accuracy_1': 0.65,
            'accuracy_2': 0.85,
            'r2_score': 0.6,
            'distribution_accuracy': 0.7,
            'avg_confidence': 0.8,
            'training_time': 120.5,
            'prediction_time': 0.1,
            'model_size': 1024
        }
        
        result = self.data_access.save_performance_metrics(performance_data)
        self.assertTrue(result)
    
    def test_get_performance_history(self):
        """测试获取性能历史"""
        # 先保存性能数据
        performance_data = {
            'model_type': 'test_model',
            'evaluation_period': '2024-01',
            'accuracy': 0.75,
            'mae': 1.2,
            'rmse': 1.8,
            'accuracy_1': 0.65,
            'accuracy_2': 0.85,
            'r2_score': 0.6
        }
        self.data_access.save_performance_metrics(performance_data)
        
        # 获取历史记录
        history = self.data_access.get_performance_history('test_model')
        self.assertGreater(len(history), 0)
        self.assertEqual(history[0]['model_type'], 'test_model')

class TestSumModels(unittest.TestCase):
    """测试和值预测模型"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建临时数据库
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.db_path = self.temp_db.name
        self.temp_db.close()
        
        # 创建测试数据库
        self._create_test_database()
    
    def tearDown(self):
        """清理测试环境"""
        if os.path.exists(self.db_path):
            os.unlink(self.db_path)
    
    def _create_test_database(self):
        """创建测试数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建lottery_data表
        cursor.execute('''
            CREATE TABLE lottery_data (
                id INTEGER PRIMARY KEY,
                issue TEXT,
                hundreds INTEGER,
                tens INTEGER,
                units INTEGER,
                draw_date TEXT
            )
        ''')
        
        # 插入更多测试数据用于模型训练
        test_data = []
        for i in range(50):
            issue = f'2024{i+1:03d}'
            hundreds = i % 10
            tens = (i + 1) % 10
            units = (i + 2) % 10
            date = f'2024-01-{(i % 30) + 1:02d}'
            test_data.append((issue, hundreds, tens, units, date))
        
        cursor.executemany(
            'INSERT INTO lottery_data (issue, hundreds, tens, units, draw_date) VALUES (?, ?, ?, ?, ?)',
            test_data
        )
        
        conn.commit()
        conn.close()
    
    def test_xgb_sum_model_basic(self):
        """测试XGBoost和值模型基本功能"""
        try:
            model = XGBSumModel(self.db_path)
            
            # 测试模型构建
            model.build_model()
            self.assertIsNotNone(model.model)
            
            # 测试数据加载
            X, y = model.load_data()
            self.assertGreater(len(X), 0)
            self.assertGreater(len(y), 0)
            self.assertEqual(len(X), len(y))
            
            # 测试模型训练（使用小数据集）
            if len(X) > 10:  # 确保有足够的数据
                X_small = X[:20]  # 使用前20个样本
                y_small = y[:20]
                performance = model.train(X_small, y_small)
                self.assertIsInstance(performance, dict)
                self.assertIn('accuracy', performance)
                self.assertTrue(model.is_trained)
                
                # 测试预测
                predictions = model.predict(X_small[:5])
                self.assertEqual(len(predictions), 5)
                self.assertTrue(all(0 <= p <= 27 for p in predictions))
                
        except ImportError:
            self.skipTest("XGBoost not available")
    
    def test_lgb_sum_model_basic(self):
        """测试LightGBM和值模型基本功能"""
        try:
            model = LGBSumModel(self.db_path)
            
            # 测试模型构建
            model.build_model()
            self.assertIsNotNone(model.model)
            
            # 测试数据加载
            X, y = model.load_data()
            self.assertGreater(len(X), 0)
            
        except ImportError:
            self.skipTest("LightGBM not available")
    
    def test_distribution_sum_model_basic(self):
        """测试分布预测和值模型基本功能"""
        try:
            model = DistributionSumModel(self.db_path)
            
            # 测试模型构建
            model.build_model()
            self.assertIsNotNone(model.model)
            
            # 测试数据加载
            X, y = model.load_data()
            self.assertGreater(len(X), 0)
            
            # 验证类别数量
            self.assertEqual(model.num_classes, 28)
            self.assertEqual(len(model.sum_range), 28)
            
        except ImportError:
            self.skipTest("XGBoost not available for distribution model")

class TestSumPredictor(unittest.TestCase):
    """测试和值预测器主类"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建临时数据库
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.db_path = self.temp_db.name
        self.temp_db.close()
        
        # 创建测试数据库
        self._create_test_database()
        
        # 创建临时配置文件
        self.temp_config = tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.yaml')
        self.config_path = self.temp_config.name
        self.temp_config.write('''
sum_predictor:
  models:
    xgb:
      n_estimators: 10
    lgb:
      n_estimators: 10
    ensemble:
      weights:
        xgb: 0.5
        lgb: 0.5
''')
        self.temp_config.close()
    
    def tearDown(self):
        """清理测试环境"""
        if os.path.exists(self.db_path):
            os.unlink(self.db_path)
        if os.path.exists(self.config_path):
            os.unlink(self.config_path)
    
    def _create_test_database(self):
        """创建测试数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建lottery_data表
        cursor.execute('''
            CREATE TABLE lottery_data (
                id INTEGER PRIMARY KEY,
                issue TEXT,
                hundreds INTEGER,
                tens INTEGER,
                units INTEGER,
                draw_date TEXT
            )
        ''')
        
        # 插入测试数据
        test_data = []
        for i in range(30):
            issue = f'2024{i+1:03d}'
            hundreds = i % 10
            tens = (i + 1) % 10
            units = (i + 2) % 10
            date = f'2024-01-{(i % 30) + 1:02d}'
            test_data.append((issue, hundreds, tens, units, date))
        
        cursor.executemany(
            'INSERT INTO lottery_data (issue, hundreds, tens, units, draw_date) VALUES (?, ?, ?, ?, ?)',
            test_data
        )
        
        conn.commit()
        conn.close()
    
    def test_sum_predictor_initialization(self):
        """测试和值预测器初始化"""
        predictor = SumPredictor(self.db_path, self.config_path)
        
        # 验证基本属性
        self.assertEqual(predictor.position, 'sum')
        self.assertEqual(predictor.prediction_range, (0, 27))
        self.assertEqual(predictor.target_type, 'regression')
        self.assertIsNotNone(predictor.data_access)
        self.assertIsInstance(predictor.config, dict)
    
    def test_build_model(self):
        """测试模型构建"""
        predictor = SumPredictor(self.db_path, self.config_path)
        
        # 构建模型
        predictor.build_model()
        
        # 验证模型已创建
        expected_models = ['xgb', 'lgb', 'lstm', 'distribution', 'constraint', 'ensemble']
        for model_name in expected_models:
            self.assertIn(model_name, predictor.models)
            self.assertIsNotNone(predictor.models[model_name])
    
    def test_model_switching(self):
        """测试模型切换"""
        predictor = SumPredictor(self.db_path, self.config_path)
        predictor.build_model()
        
        # 测试切换到不同模型
        for model_name in ['xgb', 'lgb', 'ensemble']:
            predictor.switch_model(model_name)
            self.assertEqual(predictor.current_model, model_name)
        
        # 测试切换到无效模型
        with self.assertRaises(ValueError):
            predictor.switch_model('invalid_model')
    
    def test_get_available_models(self):
        """测试获取可用模型列表"""
        predictor = SumPredictor(self.db_path, self.config_path)
        predictor.build_model()
        
        available_models = predictor.get_available_models()
        expected_models = ['xgb', 'lgb', 'lstm', 'distribution', 'constraint', 'ensemble']
        
        for model_name in expected_models:
            self.assertIn(model_name, available_models)

class TestSumPredictorIntegration(unittest.TestCase):
    """测试和值预测器集成功能"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建临时数据库
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.db_path = self.temp_db.name
        self.temp_db.close()
        
        # 创建测试数据库
        self._create_test_database()
    
    def tearDown(self):
        """清理测试环境"""
        if os.path.exists(self.db_path):
            os.unlink(self.db_path)
    
    def _create_test_database(self):
        """创建测试数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建lottery_data表
        cursor.execute('''
            CREATE TABLE lottery_data (
                id INTEGER PRIMARY KEY,
                issue TEXT,
                hundreds INTEGER,
                tens INTEGER,
                units INTEGER,
                draw_date TEXT
            )
        ''')
        
        # 插入足够的测试数据
        test_data = []
        for i in range(100):
            issue = f'2024{i+1:03d}'
            hundreds = i % 10
            tens = (i + 1) % 10
            units = (i + 2) % 10
            date = f'2024-{((i // 30) % 12) + 1:02d}-{(i % 30) + 1:02d}'
            test_data.append((issue, hundreds, tens, units, date))
        
        cursor.executemany(
            'INSERT INTO lottery_data (issue, hundreds, tens, units, draw_date) VALUES (?, ?, ?, ?, ?)',
            test_data
        )
        
        conn.commit()
        conn.close()
    
    def test_end_to_end_workflow(self):
        """测试端到端工作流程"""
        predictor = SumPredictor(self.db_path)
        
        # 1. 构建模型
        predictor.build_model()
        self.assertGreater(len(predictor.models), 0)
        
        # 2. 生成测试特征
        test_features = np.array([[13.5] * 10])  # 示例特征
        
        # 3. 测试预测（不需要训练的简单测试）
        try:
            # 切换到ensemble模型（通常更稳定）
            predictor.switch_model('ensemble')
            
            # 由于模型未训练，这里主要测试接口
            self.assertEqual(predictor.current_model, 'ensemble')
            
        except Exception as e:
            # 如果出现错误，确保是预期的（模型未训练）
            self.assertIn('训练', str(e).lower() or '未训练' in str(e).lower())

if __name__ == '__main__':
    # 设置测试环境
    import logging
    logging.basicConfig(level=logging.WARNING)  # 减少测试时的日志输出
    
    # 运行测试
    unittest.main(verbosity=2)
