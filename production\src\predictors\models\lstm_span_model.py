"""
P7跨度预测器 - LSTM回归模型

实现LSTM回归模型，捕获时序依赖
包括时序数据准备、LSTM网络构建、训练和预测

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import numpy as np
import pandas as pd
import pickle
import logging
from typing import Dict, List, Tuple, Optional, Any
from pathlib import Path

try:
    import tensorflow as tf
    from tensorflow.keras.models import Sequential
    from tensorflow.keras.layers import LSTM, Dense, Dropout
    from tensorflow.keras.optimizers import Adam
    from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
    TENSORFLOW_AVAILABLE = True
except ImportError:
    TENSORFLOW_AVAILABLE = False
    tf = None

from .base_span_model import BaseSpanModel

class LSTMSpanModel(BaseSpanModel):
    """LSTM跨度回归模型"""
    
    def __init__(self, db_path: str, config_path: Optional[str] = None):
        """
        初始化LSTM跨度模型
        
        Args:
            db_path: 数据库路径
            config_path: 配置文件路径
        """
        super().__init__(db_path, config_path)
        
        if not TENSORFLOW_AVAILABLE:
            raise ImportError("TensorFlow未安装，请运行: pip install tensorflow")
        
        self.model_type = 'lstm'
        self.model = None
        self.scaler = None
        self.is_trained = False
        
        # 加载LSTM配置
        self.lstm_config = self.config.get('span_predictor', {}).get('models', {}).get('lstm', {})
        
        # 默认参数
        self.default_params = {
            'sequence_length': 10,
            'hidden_units': 64,
            'dropout_rate': 0.2,
            'epochs': 100,
            'batch_size': 32,
            'learning_rate': 0.001,
            'early_stopping_patience': 10,
            'validation_split': 0.2
        }
        
        # 合并配置参数
        self.params = {**self.default_params, **self.lstm_config}
        
        # 初始化数据标准化器
        from sklearn.preprocessing import StandardScaler
        self.scaler = StandardScaler()
        
        self.logger.info(f"LSTM跨度模型初始化完成，参数: {self.params}")
    
    def build_model(self) -> bool:
        """
        构建LSTM模型
        
        Returns:
            是否构建成功
        """
        try:
            # 创建LSTM模型
            self.model = Sequential([
                LSTM(
                    self.params['hidden_units'],
                    return_sequences=True,
                    input_shape=(self.params['sequence_length'], 1)
                ),
                Dropout(self.params['dropout_rate']),
                
                LSTM(
                    self.params['hidden_units'] // 2,
                    return_sequences=False
                ),
                Dropout(self.params['dropout_rate']),
                
                Dense(32, activation='relu'),
                Dropout(self.params['dropout_rate'] / 2),
                
                Dense(1, activation='linear')  # 回归输出
            ])
            
            # 编译模型
            optimizer = Adam(learning_rate=self.params['learning_rate'])
            self.model.compile(
                optimizer=optimizer,
                loss='mse',
                metrics=['mae']
            )
            
            self.logger.info("LSTM跨度模型构建成功")
            return True
            
        except Exception as e:
            self.logger.error(f"LSTM跨度模型构建失败: {e}")
            return False
    
    def prepare_sequences(self, data: np.ndarray, sequence_length: int) -> Tuple[np.ndarray, np.ndarray]:
        """
        准备时序数据
        
        Args:
            data: 原始数据
            sequence_length: 序列长度
            
        Returns:
            特征序列和目标值
        """
        X, y = [], []
        
        for i in range(sequence_length, len(data)):
            # 输入序列
            X.append(data[i-sequence_length:i])
            # 目标值
            y.append(data[i])
        
        return np.array(X), np.array(y)
    
    def load_data(self) -> Tuple[np.ndarray, np.ndarray]:
        """
        加载训练数据
        
        Returns:
            特征矩阵和目标向量
        """
        try:
            # 从数据库加载数据
            data = self.data_access.load_lottery_data()
            
            if data.empty:
                raise ValueError("数据库中没有数据")
            
            # 计算跨度
            data['span'] = data[['hundreds', 'tens', 'units']].max(axis=1) - data[['hundreds', 'tens', 'units']].min(axis=1)
            
            # 按时间排序
            data = data.sort_values('id').reset_index(drop=True)
            
            # 提取跨度序列
            span_data = data['span'].values.astype(np.float32)
            
            # 数据标准化
            span_data_scaled = self.scaler.fit_transform(span_data.reshape(-1, 1)).flatten()
            
            # 准备时序数据
            X, y = self.prepare_sequences(span_data_scaled, self.params['sequence_length'])
            
            # 重塑X为LSTM输入格式 (samples, timesteps, features)
            X = X.reshape(X.shape[0], X.shape[1], 1)
            
            self.logger.info(f"加载LSTM跨度数据: {len(X)} 个样本, 序列长度: {self.params['sequence_length']}")
            return X, y
            
        except Exception as e:
            self.logger.error(f"加载LSTM跨度数据失败: {e}")
            raise
    
    def train(self, X: Optional[np.ndarray] = None, y: Optional[np.ndarray] = None) -> Dict[str, Any]:
        """
        训练模型
        
        Args:
            X: 特征矩阵（可选）
            y: 目标向量（可选）
            
        Returns:
            训练性能指标
        """
        try:
            # 如果没有提供数据，从数据库加载
            if X is None or y is None:
                X, y = self.load_data()
            
            # 构建模型
            if self.model is None:
                self.build_model()
            
            # 数据分割
            from sklearn.model_selection import train_test_split
            X_train, X_val, y_train, y_val = train_test_split(
                X, y, test_size=0.2, random_state=42
            )
            
            # 设置回调函数
            callbacks = [
                EarlyStopping(
                    monitor='val_loss',
                    patience=self.params['early_stopping_patience'],
                    restore_best_weights=True
                ),
                ReduceLROnPlateau(
                    monitor='val_loss',
                    factor=0.5,
                    patience=5,
                    min_lr=1e-6
                )
            ]
            
            # 训练模型
            history = self.model.fit(
                X_train, y_train,
                epochs=self.params['epochs'],
                batch_size=self.params['batch_size'],
                validation_data=(X_val, y_val),
                callbacks=callbacks,
                verbose=0
            )
            
            self.is_trained = True
            
            # 评估性能
            performance = self.evaluate(X_val, y_val)
            
            # 添加训练历史信息
            performance['final_train_loss'] = float(history.history['loss'][-1])
            performance['final_val_loss'] = float(history.history['val_loss'][-1])
            performance['epochs_trained'] = len(history.history['loss'])
            
            self.logger.info(f"LSTM跨度模型训练完成: {performance}")
            return performance
            
        except Exception as e:
            self.logger.error(f"LSTM跨度模型训练失败: {e}")
            raise
    
    def predict(self, X: np.ndarray) -> np.ndarray:
        """
        预测跨度
        
        Args:
            X: 特征矩阵
            
        Returns:
            预测结果
        """
        if not self.is_trained or self.model is None:
            raise ValueError("模型尚未训练")
        
        try:
            # 确保输入格式正确
            if len(X.shape) == 2:
                X = X.reshape(X.shape[0], X.shape[1], 1)
            
            # 预测
            predictions_scaled = self.model.predict(X, verbose=0)
            
            # 反标准化
            predictions = self.scaler.inverse_transform(predictions_scaled).flatten()
            
            # 跨度范围约束 (0-9)
            predictions = np.clip(predictions, 0, 9)
            
            return predictions
            
        except Exception as e:
            self.logger.error(f"LSTM跨度预测失败: {e}")
            raise
    
    def predict_with_confidence(self, X: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        带置信度的预测
        
        Args:
            X: 特征矩阵
            
        Returns:
            预测结果和置信度
        """
        predictions = self.predict(X)
        
        # 基于模型损失计算置信度
        try:
            # 计算预测的方差作为不确定性度量
            if len(X.shape) == 2:
                X = X.reshape(X.shape[0], X.shape[1], 1)
            
            # 多次预测计算方差（Monte Carlo Dropout）
            mc_predictions = []
            for _ in range(10):
                pred = self.model.predict(X, verbose=0)
                pred = self.scaler.inverse_transform(pred).flatten()
                mc_predictions.append(pred)
            
            mc_predictions = np.array(mc_predictions)
            prediction_std = np.std(mc_predictions, axis=0)
            
            # 基于标准差计算置信度
            confidences = 1.0 / (1.0 + prediction_std)
            confidences = np.clip(confidences, 0.5, 0.95)
            
        except Exception:
            # 如果Monte Carlo方法失败，使用默认置信度
            confidences = np.full(len(predictions), 0.7)
        
        return predictions, confidences
    
    def predict_sequence(self, last_sequence: np.ndarray, steps: int = 1) -> np.ndarray:
        """
        序列预测（预测未来多步）
        
        Args:
            last_sequence: 最后的序列数据
            steps: 预测步数
            
        Returns:
            预测序列
        """
        if not self.is_trained or self.model is None:
            raise ValueError("模型尚未训练")
        
        try:
            # 标准化输入序列
            sequence_scaled = self.scaler.transform(last_sequence.reshape(-1, 1)).flatten()
            
            predictions = []
            current_sequence = sequence_scaled[-self.params['sequence_length']:].copy()
            
            for _ in range(steps):
                # 预测下一个值
                X_input = current_sequence.reshape(1, self.params['sequence_length'], 1)
                next_pred_scaled = self.model.predict(X_input, verbose=0)[0, 0]
                
                # 反标准化
                next_pred = self.scaler.inverse_transform([[next_pred_scaled]])[0, 0]
                next_pred = np.clip(next_pred, 0, 9)
                predictions.append(next_pred)
                
                # 更新序列
                current_sequence = np.append(current_sequence[1:], next_pred_scaled)
            
            return np.array(predictions)
            
        except Exception as e:
            self.logger.error(f"LSTM序列预测失败: {e}")
            raise
    
    def save_model(self, filepath: str) -> bool:
        """
        保存模型
        
        Args:
            filepath: 保存路径
            
        Returns:
            是否保存成功
        """
        if not self.is_trained or self.model is None:
            self.logger.warning("模型尚未训练，无法保存")
            return False
        
        try:
            # 确保目录存在
            Path(filepath).parent.mkdir(parents=True, exist_ok=True)
            
            # 保存Keras模型
            model_path = f"{filepath}_keras_model"
            self.model.save(model_path)
            
            # 保存其他数据
            metadata = {
                'scaler': self.scaler,
                'params': self.params,
                'model_type': self.model_type,
                'is_trained': self.is_trained
            }
            
            with open(f"{filepath}_metadata.pkl", 'wb') as f:
                pickle.dump(metadata, f)
            
            self.logger.info(f"LSTM跨度模型已保存到: {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存LSTM跨度模型失败: {e}")
            return False
    
    def load_model(self, filepath: str) -> bool:
        """
        加载模型
        
        Args:
            filepath: 模型文件路径
            
        Returns:
            是否加载成功
        """
        try:
            model_path = f"{filepath}_keras_model"
            metadata_path = f"{filepath}_metadata.pkl"
            
            if not Path(model_path).exists() or not Path(metadata_path).exists():
                self.logger.error(f"模型文件不存在: {filepath}")
                return False
            
            # 加载Keras模型
            self.model = tf.keras.models.load_model(model_path)
            
            # 加载元数据
            with open(metadata_path, 'rb') as f:
                metadata = pickle.load(f)
            
            self.scaler = metadata['scaler']
            self.params = metadata.get('params', self.params)
            self.is_trained = metadata.get('is_trained', True)
            
            self.logger.info(f"LSTM跨度模型已从 {filepath} 加载")
            return True
            
        except Exception as e:
            self.logger.error(f"加载LSTM跨度模型失败: {e}")
            return False
