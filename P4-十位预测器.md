# P4-十位预测器

## 📋 项目概述
**设计理念**: 🎯 **独立位置预测** - 十位作为完全独立的随机变量进行预测
**前置条件**：P2-特征工程系统完成
**核心目标**：专门预测十位数字0-9的概率分布
**预计时间**：1-2周
**技术基础**：基于P2的PredictorFeatureInterface和十位专用特征生成器

## 🎯 设计原则

### 独立性原则
- **完全独立**: 十位预测不依赖百位、个位的任何信息
- **专注优化**: 专门针对十位特征进行深度优化
- **简单可靠**: 避免复杂的关联性分析，确保预测稳定性
- **并行友好**: 支持与P3、P5并行开发和训练

### 随机性本质
- **符合彩票本质**: 每个位置理论上是独立的随机变量
- **避免噪音干扰**: 不引入可能的虚假关联性
- **专注本位**: 深度挖掘十位自身的统计规律

## 🔧 技术要求

### 预测目标
- **输入**：十位专用特征向量（基于P2系统）
- **输出**：0-9每个数字的概率分布 (shape: 10,)
- **约束**：概率和为1，每个概率在[0,1]区间

### 模型架构
- **XGBoost分类器**：主力模型，处理非线性关系
- **LightGBM分类器**：辅助模型，快速训练
- **LSTM分类器**：时序模型，捕获时间依赖
- **集成融合**：多模型加权融合

### 数据库设计（独立架构）
```sql
-- 十位预测结果表（独立设计）
CREATE TABLE tens_predictions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    issue TEXT NOT NULL,
    model_type TEXT NOT NULL,           -- xgb/lgb/lstm/ensemble
    prob_0 REAL NOT NULL,
    prob_1 REAL NOT NULL,
    prob_2 REAL NOT NULL,
    prob_3 REAL NOT NULL,
    prob_4 REAL NOT NULL,
    prob_5 REAL NOT NULL,
    prob_6 REAL NOT NULL,
    prob_7 REAL NOT NULL,
    prob_8 REAL NOT NULL,
    prob_9 REAL NOT NULL,
    predicted_digit INTEGER,            -- 最高概率的数字
    confidence REAL,                    -- 预测置信度
    hundreds_correlation REAL,          -- 与百位的关联度
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 十位模型性能表
CREATE TABLE tens_model_performance (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    model_type TEXT NOT NULL,
    evaluation_period TEXT NOT NULL,
    accuracy REAL NOT NULL,
    top3_accuracy REAL NOT NULL,
    avg_confidence REAL NOT NULL,
    correlation_accuracy REAL,          -- 关联预测准确率
    precision_per_digit TEXT,
    recall_per_digit TEXT,
    evaluated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 百位-十位关联模式表
CREATE TABLE hundreds_tens_correlation (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    hundreds_digit INTEGER NOT NULL,
    tens_digit INTEGER NOT NULL,
    frequency INTEGER NOT NULL,
    probability REAL NOT NULL,
    last_appearance TEXT,               -- 最后出现期号
    avg_interval REAL,                  -- 平均间隔期数
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 核心功能实现

### 1. 十位预测器基类
```python
import numpy as np
import pandas as pd
from abc import ABC, abstractmethod
from typing import Dict, List, Tuple, Optional
import sqlite3
import json
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report
import xgboost as xgb
import lightgbm as lgb
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout, Input, concatenate
from tensorflow.keras.utils import to_categorical

class BaseTensPredictor(ABC):
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.model = None
        self.feature_names = None
        self.is_trained = False
        self.correlation_matrix = None
        
    @abstractmethod
    def build_model(self):
        """构建模型"""
        pass
    
    @abstractmethod
    def train(self, X: np.ndarray, y: np.ndarray, hundreds_data: Optional[np.ndarray] = None):
        """训练模型"""
        pass
    
    @abstractmethod
    def predict_probability(self, X: np.ndarray, hundreds_prob: Optional[np.ndarray] = None) -> np.ndarray:
        """预测概率分布"""
        pass
    
    def load_data(self) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """加载十位训练数据"""
        conn = sqlite3.connect(self.db_path)
        
        # 获取十位特征和百位数据
        feature_query = """
            SELECT fd.feature_vector, ld.tens, ld.hundreds
            FROM feature_data fd
            JOIN lottery_data ld ON fd.issue = ld.issue
            WHERE fd.feature_type = 'tens'
            ORDER BY fd.issue
        """
        
        cursor = conn.cursor()
        cursor.execute(feature_query)
        rows = cursor.fetchall()
        
        if not rows:
            raise ValueError("没有找到十位特征数据")
        
        X = []
        y = []
        hundreds_data = []
        
        for row in rows:
            feature_vector = json.loads(row[0])
            tens_target = row[1]
            hundreds_value = row[2]
            
            X.append(feature_vector)
            y.append(tens_target)
            hundreds_data.append(hundreds_value)
        
        # 获取特征名称
        cursor.execute("""
            SELECT feature_names FROM feature_data 
            WHERE feature_type = 'tens' 
            LIMIT 1
        """)
        feature_names_row = cursor.fetchone()
        if feature_names_row:
            self.feature_names = json.loads(feature_names_row[0])
        
        conn.close()
        
        return np.array(X), np.array(y), np.array(hundreds_data)
    
    def build_correlation_matrix(self):
        """构建百位-十位关联矩阵"""
        conn = sqlite3.connect(self.db_path)
        
        # 统计百位-十位组合频次
        query = """
            SELECT hundreds, tens, COUNT(*) as frequency
            FROM lottery_data
            GROUP BY hundreds, tens
            ORDER BY hundreds, tens
        """
        
        df = pd.read_sql_query(query, conn)
        
        # 构建10x10关联矩阵
        correlation_matrix = np.zeros((10, 10))
        
        for _, row in df.iterrows():
            h, t, freq = row['hundreds'], row['tens'], row['frequency']
            correlation_matrix[h][t] = freq
        
        # 转换为概率矩阵
        row_sums = correlation_matrix.sum(axis=1, keepdims=True)
        row_sums[row_sums == 0] = 1  # 避免除零
        self.correlation_matrix = correlation_matrix / row_sums
        
        # 保存到数据库
        self.save_correlation_matrix(df)
        
        conn.close()
        
        return self.correlation_matrix
    
    def save_correlation_matrix(self, df: pd.DataFrame):
        """保存关联矩阵到数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 清空旧数据
        cursor.execute("DELETE FROM hundreds_tens_correlation")
        
        # 计算总数用于概率计算
        total_count = df['frequency'].sum()
        
        for _, row in df.iterrows():
            h, t, freq = row['hundreds'], row['tens'], row['frequency']
            probability = freq / total_count
            
            cursor.execute("""
                INSERT INTO hundreds_tens_correlation 
                (hundreds_digit, tens_digit, frequency, probability)
                VALUES (?, ?, ?, ?)
            """, (h, t, freq, probability))
        
        conn.commit()
        conn.close()
    
    def get_correlation_enhanced_features(self, X: np.ndarray, hundreds_prob: np.ndarray) -> np.ndarray:
        """获取关联增强特征"""
        if self.correlation_matrix is None:
            self.build_correlation_matrix()
        
        enhanced_features = []
        
        for i, features in enumerate(X):
            # 基础特征
            base_features = features.tolist()
            
            # 添加关联特征
            if hundreds_prob is not None and i < len(hundreds_prob):
                # 基于百位概率计算十位期望概率
                expected_tens_prob = np.dot(hundreds_prob[i], self.correlation_matrix)
                base_features.extend(expected_tens_prob.tolist())
                
                # 添加最可能的百位数字
                most_likely_hundreds = np.argmax(hundreds_prob[i])
                base_features.append(most_likely_hundreds)
                
                # 添加百位置信度
                hundreds_confidence = np.max(hundreds_prob[i])
                base_features.append(hundreds_confidence)
            else:
                # 如果没有百位概率，用零填充
                base_features.extend([0] * 12)  # 10个期望概率 + 1个数字 + 1个置信度
            
            enhanced_features.append(base_features)
        
        return np.array(enhanced_features)
```

### 2. XGBoost十位预测器
```python
class XGBTensPredictor(BaseTensPredictor):
    def __init__(self, db_path: str):
        super().__init__(db_path)
        self.model_params = {
            'objective': 'multi:softprob',
            'num_class': 10,
            'max_depth': 6,
            'learning_rate': 0.1,
            'subsample': 0.8,
            'colsample_bytree': 0.8,
            'reg_alpha': 0.1,
            'reg_lambda': 1.0,
            'n_estimators': 200,
            'random_state': 42,
            'n_jobs': -1
        }
    
    def build_model(self):
        """构建XGBoost模型"""
        self.model = xgb.XGBClassifier(**self.model_params)
        return self.model
    
    def train(self, X: np.ndarray, y: np.ndarray, hundreds_data: Optional[np.ndarray] = None):
        """训练XGBoost模型"""
        if self.model is None:
            self.build_model()
        
        # 构建关联矩阵
        self.build_correlation_matrix()
        
        # 如果有百位数据，创建百位概率（用于训练时的特征增强）
        if hundreds_data is not None:
            # 创建one-hot编码作为"完美"的百位概率
            hundreds_prob = np.eye(10)[hundreds_data]
            X_enhanced = self.get_correlation_enhanced_features(X, hundreds_prob)
        else:
            X_enhanced = X
        
        # 分割训练集和验证集
        X_train, X_val, y_train, y_val = train_test_split(
            X_enhanced, y, test_size=0.2, random_state=42, stratify=y
        )
        
        # 训练模型
        self.model.fit(
            X_train, y_train,
            eval_set=[(X_val, y_val)],
            early_stopping_rounds=20,
            verbose=False
        )
        
        self.is_trained = True
        
        # 评估模型
        val_performance = self.evaluate_model(X_val, y_val)
        print(f"XGBoost十位预测器验证集性能: {val_performance}")
        
        return val_performance
    
    def predict_probability(self, X: np.ndarray, hundreds_prob: Optional[np.ndarray] = None) -> np.ndarray:
        """预测概率分布"""
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        # 增强特征
        if hundreds_prob is not None:
            X_enhanced = self.get_correlation_enhanced_features(X, hundreds_prob)
        else:
            # 如果没有百位概率，使用均匀分布
            uniform_prob = np.full((len(X), 10), 0.1)
            X_enhanced = self.get_correlation_enhanced_features(X, uniform_prob)
        
        probabilities = self.model.predict_proba(X_enhanced)
        return probabilities
    
    def evaluate_model(self, X_test: np.ndarray, y_test: np.ndarray) -> Dict:
        """评估模型性能"""
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        y_prob = self.model.predict_proba(X_test)
        y_pred = np.argmax(y_prob, axis=1)
        
        accuracy = accuracy_score(y_test, y_pred)
        top3_accuracy = np.mean([
            y_test[i] in np.argsort(y_prob[i])[-3:] 
            for i in range(len(y_test))
        ])
        avg_confidence = np.mean(np.max(y_prob, axis=1))
        
        report = classification_report(y_test, y_pred, output_dict=True, zero_division=0)
        precision_per_digit = {str(i): report.get(str(i), {}).get('precision', 0) for i in range(10)}
        recall_per_digit = {str(i): report.get(str(i), {}).get('recall', 0) for i in range(10)}
        
        return {
            'accuracy': accuracy,
            'top3_accuracy': top3_accuracy,
            'avg_confidence': avg_confidence,
            'precision_per_digit': precision_per_digit,
            'recall_per_digit': recall_per_digit
        }
```

### 3. 关联增强预测器
```python
class CorrelationTensPredictor(BaseTensPredictor):
    def __init__(self, db_path: str):
        super().__init__(db_path)
        self.base_predictor = XGBTensPredictor(db_path)
        self.correlation_weight = 0.3  # 关联性权重
        
    def build_model(self):
        """构建关联增强模型"""
        self.base_predictor.build_model()
        self.build_correlation_matrix()
        return self.base_predictor.model
    
    def train(self, X: np.ndarray, y: np.ndarray, hundreds_data: Optional[np.ndarray] = None):
        """训练关联增强模型"""
        # 训练基础模型
        performance = self.base_predictor.train(X, y, hundreds_data)
        self.is_trained = True
        return performance
    
    def predict_probability(self, X: np.ndarray, hundreds_prob: Optional[np.ndarray] = None) -> np.ndarray:
        """关联增强预测"""
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        # 获取基础预测
        base_prob = self.base_predictor.predict_probability(X, hundreds_prob)
        
        if hundreds_prob is None or self.correlation_matrix is None:
            return base_prob
        
        # 计算关联增强概率
        enhanced_prob = []
        
        for i in range(len(X)):
            # 基于百位概率和关联矩阵计算期望十位概率
            correlation_prob = np.dot(hundreds_prob[i], self.correlation_matrix)
            
            # 融合基础预测和关联预测
            final_prob = (
                (1 - self.correlation_weight) * base_prob[i] +
                self.correlation_weight * correlation_prob
            )
            
            # 归一化
            final_prob = final_prob / np.sum(final_prob)
            enhanced_prob.append(final_prob)
        
        return np.array(enhanced_prob)
    
    def optimize_correlation_weight(self, X_val: np.ndarray, y_val: np.ndarray, 
                                  hundreds_prob_val: np.ndarray):
        """优化关联权重"""
        best_weight = 0.3
        best_accuracy = 0
        
        for weight in np.arange(0.1, 0.8, 0.1):
            self.correlation_weight = weight
            y_prob = self.predict_probability(X_val, hundreds_prob_val)
            y_pred = np.argmax(y_prob, axis=1)
            accuracy = accuracy_score(y_val, y_pred)
            
            if accuracy > best_accuracy:
                best_accuracy = accuracy
                best_weight = weight
        
        self.correlation_weight = best_weight
        print(f"最优关联权重: {best_weight:.1f}, 准确率: {best_accuracy:.4f}")
        
        return best_weight
```

### 4. 集成十位预测器
```python
class EnsembleTensPredictor:
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.xgb_predictor = XGBTensPredictor(db_path)
        self.lgb_predictor = LGBTensPredictor(db_path)  # 类似XGB实现
        self.correlation_predictor = CorrelationTensPredictor(db_path)
        
        # 初始权重
        self.weights = {
            'xgb': 0.35,
            'lgb': 0.35,
            'correlation': 0.3
        }
        
        self.is_trained = False
    
    def train_all_models(self):
        """训练所有模型"""
        X, y, hundreds_data = self.xgb_predictor.load_data()
        
        print("训练XGBoost十位预测器...")
        xgb_performance = self.xgb_predictor.train(X, y, hundreds_data)
        
        print("训练LightGBM十位预测器...")
        lgb_performance = self.lgb_predictor.train(X, y, hundreds_data)
        
        print("训练关联增强十位预测器...")
        correlation_performance = self.correlation_predictor.train(X, y, hundreds_data)
        
        # 根据性能调整权重
        self.adjust_weights(xgb_performance, lgb_performance, correlation_performance)
        
        self.is_trained = True
        
        return {
            'xgb': xgb_performance,
            'lgb': lgb_performance,
            'correlation': correlation_performance,
            'weights': self.weights
        }
    
    def adjust_weights(self, xgb_perf: Dict, lgb_perf: Dict, corr_perf: Dict):
        """根据性能调整权重"""
        xgb_acc = xgb_perf.get('accuracy', 0)
        lgb_acc = lgb_perf.get('accuracy', 0)
        corr_acc = corr_perf.get('accuracy', 0)
        
        total_acc = xgb_acc + lgb_acc + corr_acc
        
        if total_acc > 0:
            self.weights['xgb'] = xgb_acc / total_acc
            self.weights['lgb'] = lgb_acc / total_acc
            self.weights['correlation'] = corr_acc / total_acc
        
        print(f"调整后的十位预测器权重: {self.weights}")
    
    def predict_probability(self, X: np.ndarray, hundreds_prob: Optional[np.ndarray] = None) -> np.ndarray:
        """集成预测概率分布"""
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        # 获取各模型预测
        xgb_prob = self.xgb_predictor.predict_probability(X, hundreds_prob)
        lgb_prob = self.lgb_predictor.predict_probability(X, hundreds_prob)
        corr_prob = self.correlation_predictor.predict_probability(X, hundreds_prob)
        
        # 加权融合
        ensemble_prob = (
            self.weights['xgb'] * xgb_prob +
            self.weights['lgb'] * lgb_prob +
            self.weights['correlation'] * corr_prob
        )
        
        # 确保概率和为1
        ensemble_prob = ensemble_prob / np.sum(ensemble_prob, axis=1, keepdims=True)
        
        return ensemble_prob
    
    def predict_next_period(self, issue: str, hundreds_prob: Optional[np.ndarray] = None) -> Dict:
        """预测下一期十位"""
        # 获取最新特征
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT feature_vector FROM feature_data 
            WHERE feature_type = 'tens' 
            ORDER BY issue DESC 
            LIMIT 1
        """)
        
        row = cursor.fetchone()
        if not row:
            raise ValueError("没有找到最新的十位特征数据")
        
        latest_features = np.array(json.loads(row[0])).reshape(1, -1)
        
        # 预测概率
        probabilities = self.predict_probability(latest_features, hundreds_prob)[0]
        
        # 计算与百位的关联度
        correlation_score = 0.0
        if hundreds_prob is not None:
            correlation_matrix = self.correlation_predictor.correlation_matrix
            if correlation_matrix is not None:
                expected_prob = np.dot(hundreds_prob[0], correlation_matrix)
                correlation_score = float(np.dot(probabilities, expected_prob))
        
        # 保存预测结果
        self.save_prediction(issue, probabilities, correlation_score)
        
        conn.close()
        
        return {
            'issue': issue,
            'probabilities': probabilities.tolist(),
            'predicted_digit': int(np.argmax(probabilities)),
            'confidence': float(np.max(probabilities)),
            'correlation_score': correlation_score,
            'top3_digits': np.argsort(probabilities)[-3:].tolist()[::-1]
        }
    
    def save_prediction(self, issue: str, probabilities: np.ndarray, correlation_score: float):
        """保存预测结果"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        predicted_digit = int(np.argmax(probabilities))
        confidence = float(np.max(probabilities))
        
        cursor.execute("""
            INSERT OR REPLACE INTO tens_predictions 
            (issue, model_type, prob_0, prob_1, prob_2, prob_3, prob_4, 
             prob_5, prob_6, prob_7, prob_8, prob_9, predicted_digit, 
             confidence, hundreds_correlation)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (issue, 'ensemble', *probabilities.tolist(), predicted_digit, 
              confidence, correlation_score))
        
        conn.commit()
        conn.close()
```

## 成功标准

### 模型性能
- [ ] XGBoost准确率 > 35%
- [ ] LightGBM准确率 > 35%
- [ ] 关联增强准确率 > 38%
- [ ] 集成模型准确率 > 40%

### 关联性分析
- [ ] 百位-十位关联矩阵构建完成
- [ ] 关联增强效果显著
- [ ] 关联权重自动优化

### 预测质量
- [ ] Top3准确率 > 70%
- [ ] 平均置信度 > 0.15
- [ ] 关联度计算准确

## 部署说明

```python
# 使用示例
predictor = EnsembleTensPredictor("data/lottery.db")

# 训练所有模型
performance = predictor.train_all_models()
print(f"十位预测器训练完成: {performance}")

# 预测下一期（需要百位概率）
hundreds_prob = np.array([[0.1, 0.2, 0.15, 0.1, 0.05, 0.1, 0.1, 0.05, 0.1, 0.05]])
prediction = predictor.predict_next_period("2024001", hundreds_prob)
print(f"十位预测结果: {prediction}")
```

## 下一步
完成P4后，进入**P5-个位预测器**开发阶段。
