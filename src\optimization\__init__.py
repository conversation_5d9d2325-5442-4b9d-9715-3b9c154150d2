#!/usr/bin/env python3
"""
P9闭环自动优化系统

该包包含P9闭环自动优化系统的所有核心组件，实现了基于P8智能融合系统的
完全自动化闭环优化流程。

核心组件：
- IntelligentClosedLoopOptimizer: 智能闭环优化器
- IntelligentOptimizationManager: 智能优化管理器
- TaskQueueManager: 任务队列管理器
- PerformanceAnalyzer: 性能分析器
- IntelligentDecisionEngine: 智能决策引擎
- ExceptionHandler: 异常处理器
- P8IntegrationLayer: P8集成层

作者: Augment Code AI Assistant
创建日期: 2025-01-14
版本: 1.0.0
"""

__version__ = "1.0.0"
__author__ = "Augment Code AI Assistant"

# 导入核心组件
try:
    from .intelligent_closed_loop_optimizer import (
        IntelligentClosedLoopOptimizer,
        OptimizationTask,
        OptimizationTaskType,
        OptimizationStatus
    )
except ImportError:
    IntelligentClosedLoopOptimizer = None
    OptimizationTask = None
    OptimizationTaskType = None
    OptimizationStatus = None

try:
    from .intelligent_optimization_manager import IntelligentOptimizationManager
except ImportError:
    IntelligentOptimizationManager = None

try:
    from .task_queue_manager import (
        OptimizationTaskQueue,
        TaskPriority,
        TaskQueueItem
    )
except ImportError:
    OptimizationTaskQueue = None
    TaskPriority = None
    TaskQueueItem = None

try:
    from .performance_analyzer import (
        PerformanceAnalyzer,
        PerformanceMetric,
        PerformanceStatus,
        TrendDirection
    )
except ImportError:
    PerformanceAnalyzer = None
    PerformanceMetric = None
    PerformanceStatus = None
    TrendDirection = None

try:
    from .intelligent_decision_engine import (
        IntelligentDecisionEngine,
        DecisionContext,
        DecisionResult,
        DecisionType,
        RiskLevel
    )
except ImportError:
    IntelligentDecisionEngine = None
    DecisionContext = None
    DecisionResult = None
    DecisionType = None
    RiskLevel = None

try:
    from .exception_handler import (
        ExceptionHandler,
        SystemException,
        ExceptionSeverity,
        RecoveryAction
    )
except ImportError:
    ExceptionHandler = None
    SystemException = None
    ExceptionSeverity = None
    RecoveryAction = None

try:
    from .p8_integration_layer import P8IntegrationLayer
except ImportError:
    P8IntegrationLayer = None

__all__ = [
    # 核心优化器
    'IntelligentClosedLoopOptimizer',
    'OptimizationTask',
    'OptimizationTaskType', 
    'OptimizationStatus',
    
    # 管理器
    'IntelligentOptimizationManager',
    
    # 任务队列
    'OptimizationTaskQueue',
    'TaskPriority',
    'TaskQueueItem',
    
    # 性能分析
    'PerformanceAnalyzer',
    'PerformanceMetric',
    'PerformanceStatus',
    'TrendDirection',
    
    # 决策引擎
    'IntelligentDecisionEngine',
    'DecisionContext',
    'DecisionResult',
    'DecisionType',
    'RiskLevel',
    
    # 异常处理
    'ExceptionHandler',
    'SystemException',
    'ExceptionSeverity',
    'RecoveryAction',
    
    # P8集成
    'P8IntegrationLayer'
]
