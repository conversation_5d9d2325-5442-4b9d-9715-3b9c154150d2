#!/usr/bin/env python3
import sqlite3
import os

# 检查数据库文件
db_path = "data/fucai3d.db"
print(f"检查数据库文件: {db_path}")
print(f"文件存在: {os.path.exists(db_path)}")

if os.path.exists(db_path):
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 查看所有表
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = cursor.fetchall()
    print(f"数据库表: {tables}")
    
    # 检查final_predictions表
    try:
        cursor.execute("SELECT COUNT(*) FROM final_predictions")
        count = cursor.fetchone()[0]
        print(f"final_predictions表记录数: {count}")
        
        if count > 0:
            cursor.execute("SELECT * FROM final_predictions LIMIT 3")
            rows = cursor.fetchall()
            print(f"前3条记录: {rows}")
    except Exception as e:
        print(f"查询final_predictions表失败: {e}")
    
    conn.close()
else:
    print("数据库文件不存在!")
