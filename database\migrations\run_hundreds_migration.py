#!/usr/bin/env python3
"""
P3-百位预测器数据库迁移执行脚本

执行百位预测器相关数据库表的创建
"""

import sqlite3
import os
import sys
from pathlib import Path

def run_migration():
    """执行数据库迁移"""
    
    # 获取项目根目录
    project_root = Path(__file__).parent.parent.parent
    db_path = project_root / "data" / "lottery.db"
    sql_file = Path(__file__).parent / "create_hundreds_tables.sql"
    
    print(f"🔧 开始执行P3-百位预测器数据库迁移...")
    print(f"数据库路径: {db_path}")
    print(f"SQL文件路径: {sql_file}")
    
    # 检查数据库文件是否存在
    if not db_path.exists():
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    # 检查SQL文件是否存在
    if not sql_file.exists():
        print(f"❌ SQL文件不存在: {sql_file}")
        return False
    
    try:
        # 连接数据库
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # 读取SQL文件
        with open(sql_file, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        # 执行SQL脚本
        cursor.executescript(sql_content)
        conn.commit()
        
        # 验证表是否创建成功
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE 'hundreds_%'")
        tables = cursor.fetchall()
        
        print(f"✅ 数据库迁移执行成功！")
        print(f"创建的表: {[table[0] for table in tables]}")
        
        # 验证表结构
        for table_name, in tables:
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            print(f"📋 表 {table_name} 包含 {len(columns)} 个字段")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库迁移失败: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

def verify_migration():
    """验证迁移结果"""
    
    project_root = Path(__file__).parent.parent.parent
    db_path = project_root / "data" / "lottery.db"
    
    try:
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # 检查表是否存在
        required_tables = ['hundreds_predictions', 'hundreds_model_performance']
        
        for table in required_tables:
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table,))
            result = cursor.fetchone()
            
            if result:
                print(f"✅ 表 {table} 存在")
                
                # 检查表结构
                cursor.execute(f"PRAGMA table_info({table})")
                columns = cursor.fetchall()
                print(f"   包含字段: {len(columns)} 个")
                
                # 检查记录数
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"   记录数: {count}")
                
            else:
                print(f"❌ 表 {table} 不存在")
                return False
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 P3-百位预测器数据库迁移工具")
    print("=" * 50)
    
    # 执行迁移
    if run_migration():
        print("\n🔍 验证迁移结果...")
        if verify_migration():
            print("\n🎉 P3数据库迁移完成！")
            sys.exit(0)
        else:
            print("\n❌ 迁移验证失败")
            sys.exit(1)
    else:
        print("\n❌ 数据库迁移失败")
        sys.exit(1)
