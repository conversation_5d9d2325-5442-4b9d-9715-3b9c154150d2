#!/usr/bin/env python3
"""
统一日志管理器
为P9系统提供结构化日志记录功能
"""

import logging
import json
import uuid
import time
import threading
from datetime import datetime
from typing import Dict, Any, Optional, Union
from pathlib import Path
import os


class StructuredLogger:
    """结构化日志记录器"""
    
    def __init__(self, name: str, log_dir: str = "logs", level: str = "INFO"):
        """
        初始化日志记录器
        
        Args:
            name: 日志记录器名称
            log_dir: 日志文件目录
            level: 日志级别
        """
        self.name = name
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        
        # 创建logger
        self.logger = logging.getLogger(name)
        self.logger.setLevel(getattr(logging, level.upper()))
        
        # 避免重复添加handler
        if not self.logger.handlers:
            self._setup_handlers()
        
        # 线程本地存储，用于请求追踪
        self._local = threading.local()
    
    def _setup_handlers(self):
        """设置日志处理器"""
        # 文件处理器
        log_file = self.log_dir / f"{self.name}.log"
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # 设置格式器
        formatter = StructuredFormatter()
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # 添加处理器
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
    
    def set_request_id(self, request_id: Optional[str] = None) -> str:
        """设置当前请求ID"""
        if request_id is None:
            request_id = f"req_{uuid.uuid4().hex[:8]}"
        self._local.request_id = request_id
        return request_id
    
    def get_request_id(self) -> Optional[str]:
        """获取当前请求ID"""
        return getattr(self._local, 'request_id', None)
    
    def _log(self, level: str, message: str, **context):
        """内部日志记录方法"""
        # 添加基础上下文
        log_context = {
            'timestamp': datetime.now().isoformat(),
            'component': self.name,
            'request_id': self.get_request_id(),
            **context
        }
        
        # 创建结构化日志记录
        record = {
            'message': message,
            'context': log_context
        }
        
        # 记录日志
        getattr(self.logger, level.lower())(json.dumps(record, ensure_ascii=False))
    
    def debug(self, message: str, **context):
        """调试级别日志"""
        self._log('DEBUG', message, **context)
    
    def info(self, message: str, **context):
        """信息级别日志"""
        self._log('INFO', message, **context)
    
    def warning(self, message: str, **context):
        """警告级别日志"""
        self._log('WARNING', message, **context)
    
    def error(self, message: str, **context):
        """错误级别日志"""
        self._log('ERROR', message, **context)
    
    def critical(self, message: str, **context):
        """严重错误级别日志"""
        self._log('CRITICAL', message, **context)
    
    def api_call(self, method: str, duration: float, status: str = "success", **context):
        """API调用专用日志"""
        self.info(
            f"API调用: {method}",
            method=method,
            duration=f"{duration:.3f}s",
            status=status,
            **context
        )
    
    def performance(self, operation: str, duration: float, **context):
        """性能监控专用日志"""
        self.info(
            f"性能监控: {operation}",
            operation=operation,
            duration=f"{duration:.3f}s",
            type="performance",
            **context
        )
    
    def cache_operation(self, operation: str, key: str, hit: bool = None, **context):
        """缓存操作专用日志"""
        self.debug(
            f"缓存操作: {operation}",
            operation=operation,
            cache_key=key,
            cache_hit=hit,
            type="cache",
            **context
        )
    
    def database_operation(self, operation: str, table: str, duration: float = None, **context):
        """数据库操作专用日志"""
        log_context = {
            'operation': operation,
            'table': table,
            'type': 'database',
            **context
        }
        
        if duration is not None:
            log_context['duration'] = f"{duration:.3f}s"
        
        self.debug(f"数据库操作: {operation} on {table}", **log_context)


class StructuredFormatter(logging.Formatter):
    """结构化日志格式器"""
    
    def format(self, record):
        """格式化日志记录"""
        try:
            # 尝试解析JSON格式的消息
            log_data = json.loads(record.getMessage())
            message = log_data.get('message', '')
            context = log_data.get('context', {})
            
            # 构建格式化的日志字符串
            timestamp = context.get('timestamp', datetime.now().isoformat())
            component = context.get('component', 'Unknown')
            request_id = context.get('request_id', 'N/A')
            
            # 基础格式
            formatted = f"[{timestamp}] [{record.levelname}] [{component}] [{message}]"
            
            # 添加请求ID（如果存在）
            if request_id != 'N/A':
                formatted += f" [req:{request_id}]"
            
            # 添加其他上下文信息
            other_context = {k: v for k, v in context.items() 
                           if k not in ['timestamp', 'component', 'request_id']}
            
            if other_context:
                context_str = json.dumps(other_context, ensure_ascii=False)
                formatted += f" {context_str}"
            
            return formatted
            
        except (json.JSONDecodeError, KeyError):
            # 如果不是JSON格式，使用默认格式
            return f"[{datetime.now().isoformat()}] [{record.levelname}] [{record.name}] [{record.getMessage()}]"


# 全局日志管理器
_loggers: Dict[str, StructuredLogger] = {}
_lock = threading.Lock()


def get_logger(name: str, log_dir: str = "logs", level: str = "INFO") -> StructuredLogger:
    """
    获取或创建日志记录器
    
    Args:
        name: 日志记录器名称
        log_dir: 日志文件目录
        level: 日志级别
    
    Returns:
        StructuredLogger实例
    """
    with _lock:
        if name not in _loggers:
            _loggers[name] = StructuredLogger(name, log_dir, level)
        return _loggers[name]


def get_api_logger() -> StructuredLogger:
    """获取API专用日志记录器"""
    return get_logger("P9SystemAdapter", level="INFO")


def get_performance_logger() -> StructuredLogger:
    """获取性能监控专用日志记录器"""
    return get_logger("PerformanceMonitor", level="DEBUG")


def get_cache_logger() -> StructuredLogger:
    """获取缓存专用日志记录器"""
    return get_logger("CacheManager", level="DEBUG")


def get_database_logger() -> StructuredLogger:
    """获取数据库专用日志记录器"""
    return get_logger("DatabaseManager", level="DEBUG")


class LogContext:
    """日志上下文管理器"""
    
    def __init__(self, logger: StructuredLogger, request_id: Optional[str] = None, **context):
        self.logger = logger
        self.request_id = request_id
        self.context = context
        self.start_time = None
        self.old_request_id = None
    
    def __enter__(self):
        self.start_time = time.time()
        self.old_request_id = self.logger.get_request_id()
        
        if self.request_id:
            self.logger.set_request_id(self.request_id)
        
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        duration = time.time() - self.start_time
        
        if exc_type is None:
            self.logger.info("操作完成", duration=f"{duration:.3f}s", **self.context)
        else:
            self.logger.error(
                f"操作失败: {exc_val}",
                duration=f"{duration:.3f}s",
                exception_type=exc_type.__name__,
                **self.context
            )
        
        # 恢复原来的请求ID
        if self.old_request_id:
            self.logger.set_request_id(self.old_request_id)


def log_api_call(func):
    """API调用日志装饰器"""
    def wrapper(*args, **kwargs):
        logger = get_api_logger()
        request_id = logger.set_request_id()
        
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            duration = time.time() - start_time
            logger.api_call(func.__name__, duration, status="success")
            return result
        except Exception as e:
            duration = time.time() - start_time
            logger.api_call(func.__name__, duration, status="error", error=str(e))
            raise
    
    return wrapper


def log_async_api_call(func):
    """异步API调用日志装饰器"""
    async def wrapper(*args, **kwargs):
        logger = get_api_logger()
        request_id = logger.set_request_id()
        
        start_time = time.time()
        try:
            result = await func(*args, **kwargs)
            duration = time.time() - start_time
            logger.api_call(func.__name__, duration, status="success")
            return result
        except Exception as e:
            duration = time.time() - start_time
            logger.api_call(func.__name__, duration, status="error", error=str(e))
            raise
    
    return wrapper
