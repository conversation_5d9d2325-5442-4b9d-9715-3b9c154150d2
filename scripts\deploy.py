#!/usr/bin/env python3
"""
简化的部署脚本
一键部署完整的福彩3D数据库
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def main():
    """主函数"""
    print("🚀 福彩3D完整数据库一键部署")
    print("=" * 60)
    
    try:
        # 导入部署模块
        from deploy_complete_database import main as deploy_main
        
        # 执行部署
        success = deploy_main()
        
        if success:
            print("\n" + "=" * 60)
            print("🎉 部署成功完成！")
            print("💾 数据库文件：data/lottery.db")
            print("📊 包含从2002001到最新一期的完整数据")
            print("✅ 严格遵守'禁止虚拟数据'原则")
            print("🔄 可定期运行此脚本更新数据")
        else:
            print("\n" + "=" * 60)
            print("❌ 部署失败")
            print("🔧 请检查错误信息并重试")
        
        return success
        
    except Exception as e:
        print(f"❌ 部署异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🎯 下一步操作：")
        print("1. 验证数据：python -c \"import sqlite3; conn=sqlite3.connect('data/lottery.db'); print('记录数:', conn.execute('SELECT COUNT(*) FROM lottery_records').fetchone()[0]); conn.close()\"")
        print("2. 查看最新数据：python -c \"import sqlite3; conn=sqlite3.connect('data/lottery.db'); [print(f'期号:{r[0]} 号码:{r[1]}') for r in conn.execute('SELECT period, numbers FROM lottery_records ORDER BY period DESC LIMIT 5').fetchall()]; conn.close()\"")
        print("3. 启动API服务：python src/api/production_main.py")
        print("4. 启动UI界面：python src/ui/main.py")
    
    sys.exit(0 if success else 1)
