#!/usr/bin/env python3
"""
分析 data 目录下的数据库文件
"""
import sqlite3
import os
from pathlib import Path

def analyze_database(db_path):
    """分析单个数据库文件"""
    print(f"\n{'='*60}")
    print(f"分析数据库: {db_path}")
    print(f"{'='*60}")
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print(f"数据库大小: {os.path.getsize(db_path) / 1024:.2f} KB")
        print(f"表数量: {len(tables)}")

        for table_name, in tables:
            print(f"\n表名: {table_name}")
            
            # 获取表结构
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            
            print("   字段结构:")
            for col in columns:
                col_id, name, data_type, not_null, default, pk = col
                pk_str = " (主键)" if pk else ""
                not_null_str = " NOT NULL" if not_null else ""
                default_str = f" DEFAULT {default}" if default else ""
                print(f"     - {name}: {data_type}{not_null_str}{default_str}{pk_str}")
            
            # 获取记录数量
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"   记录数量: {count}")

            # 显示前几条记录示例
            if count > 0:
                cursor.execute(f"SELECT * FROM {table_name} LIMIT 3")
                samples = cursor.fetchall()
                print("   示例数据:")
                for i, sample in enumerate(samples, 1):
                    print(f"     {i}. {sample}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 分析数据库时出错: {e}")

def find_database_usage(db_name):
    """查找数据库在代码中的使用情况"""
    print(f"\n查找 {db_name} 的使用情况:")

    # 搜索Python文件中的数据库引用
    python_files = list(Path('.').rglob('*.py'))

    usage_files = []
    for py_file in python_files:
        try:
            with open(py_file, 'r', encoding='utf-8') as f:
                content = f.read()
                if db_name in content:
                    usage_files.append(str(py_file))
        except:
            continue

    if usage_files:
        print(f"   使用该数据库的文件:")
        for file in usage_files:
            print(f"     - {file}")
    else:
        print(f"   未找到使用 {db_name} 的Python文件")

def main():
    """主函数"""
    print("福彩3D项目数据库分析报告")
    print("=" * 60)
    
    # 数据库文件列表
    databases = [
        'data/lottery.db',
        'data/fucai3d.db', 
        'data/alerts.db'
    ]
    
    # 分析每个数据库
    for db_path in databases:
        analyze_database(db_path)
        
        # 查找使用情况
        db_name = os.path.basename(db_path)
        find_database_usage(db_name)
    
    print(f"\n{'='*60}")
    print("数据库分析完成")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
