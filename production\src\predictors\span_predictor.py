"""
P7跨度预测器主类

基于BaseIndependentPredictor架构开发跨度预测器
实现6种模型和双重约束优化功能，依赖P6和值预测器完成

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import sys
import os
from pathlib import Path
import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Tuple, Optional, Any
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from .base_independent_predictor import BaseIndependentPredictor
from .models.xgb_span_model import XGBSpanModel
from .models.lgb_span_model import LGBSpanModel
from .models.lstm_span_model import LSTMSpanModel
from .models.classification_span_model import ClassificationSpanModel
from .models.constraint_span_model import ConstraintSpanModel
from .models.ensemble_span_model import EnsembleSpanModel
from ..data.span_data_access import SpanDataAccess

class SpanPredictor(BaseIndependentPredictor):
    """P7跨度预测器主类"""

    def __init__(self, db_path: str, config_path: Optional[str] = None):
        """
        初始化跨度预测器

        Args:
            db_path: 数据库路径
            config_path: 配置文件路径
        """
        # 继承BaseIndependentPredictor，position设为'span'
        super().__init__('span', db_path)

        # 跨度预测器专属属性
        self.data_access = SpanDataAccess(db_path)
        self.config = self._load_config(config_path)

        # 跨度预测的特殊配置
        self.prediction_range = (0, 9)  # 跨度范围0-9
        self.target_type = 'regression'  # 主要是回归问题

        # 初始化所有模型
        self.models = {}
        self.current_model = 'ensemble'  # 默认使用集成模型

        # 位置预测器引用（用于双重约束优化）
        self.position_predictors = {
            'hundreds': None,
            'tens': None,
            'units': None
        }

        # 和值预测器引用（用于双重约束优化）
        self.sum_predictor = None

        # 跨度专属配置
        self.span_config = self.config.get('span_predictor', {}).get('span_config', {})
        self.dual_constraints_enabled = self.span_config.get('enable_dual_constraints', True)
        self.pattern_analysis_enabled = self.span_config.get('enable_pattern_analysis', True)

        self.logger.info("初始化跨度预测器完成")

    def _load_config(self, config_path: Optional[str] = None) -> Dict[str, Any]:
        """加载配置文件"""
        if config_path is None:
            config_path = "config/span_predictor_config.yaml"
        
        try:
            import yaml
            config_file = Path(config_path)
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    return yaml.safe_load(f)
            else:
                self.logger.warning(f"配置文件不存在: {config_path}")
                return {}
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            return {}

    def build_model(self):
        """构建所有模型"""
        try:
            # 初始化所有6种模型
            self.models['xgb'] = XGBSpanModel(self.db_path, self.config)
            self.models['lgb'] = LGBSpanModel(self.db_path, self.config)
            self.models['lstm'] = LSTMSpanModel(self.db_path, self.config)
            self.models['classification'] = ClassificationSpanModel(self.db_path, self.config)
            self.models['constraint'] = ConstraintSpanModel(self.db_path, self.config)
            self.models['ensemble'] = EnsembleSpanModel(self.db_path, self.config)

            # 为约束模型设置基础模型
            if 'constraint' in self.models:
                base_models = {k: v for k, v in self.models.items() if k != 'constraint'}
                self.models['constraint'].set_base_models(base_models)

            # 为集成模型设置基础模型
            if 'ensemble' in self.models:
                base_models = {k: v for k, v in self.models.items() if k != 'ensemble'}
                self.models['ensemble'].set_base_models(base_models)

            self.logger.info(f"构建了 {len(self.models)} 个跨度预测模型")
            return True

        except Exception as e:
            self.logger.error(f"构建模型失败: {e}")
            return False

    def train(self, X: np.ndarray, y: np.ndarray) -> Dict[str, Any]:
        """
        训练所有模型

        Args:
            X: 特征矩阵
            y: 目标向量（跨度值）

        Returns:
            训练结果字典
        """
        try:
            if not self.models:
                self.build_model()

            training_results = {}
            
            # 训练所有基础模型
            for model_name, model in self.models.items():
                if model_name in ['constraint', 'ensemble']:
                    continue  # 这些模型依赖其他模型，稍后训练
                
                try:
                    self.logger.info(f"训练跨度模型: {model_name}")
                    result = model.train(X, y)
                    training_results[model_name] = result
                    self.logger.info(f"模型 {model_name} 训练完成")
                except Exception as e:
                    self.logger.error(f"训练模型 {model_name} 失败: {e}")
                    training_results[model_name] = {'error': str(e)}

            # 训练约束模型
            if 'constraint' in self.models:
                try:
                    self.logger.info("训练约束优化模型")
                    result = self.models['constraint'].train(X, y)
                    training_results['constraint'] = result
                except Exception as e:
                    self.logger.error(f"训练约束模型失败: {e}")
                    training_results['constraint'] = {'error': str(e)}

            # 训练集成模型
            if 'ensemble' in self.models:
                try:
                    self.logger.info("训练集成模型")
                    result = self.models['ensemble'].train(X, y)
                    training_results['ensemble'] = result
                except Exception as e:
                    self.logger.error(f"训练集成模型失败: {e}")
                    training_results['ensemble'] = {'error': str(e)}

            self.is_trained = True
            self.training_history = training_results

            # 保存训练结果
            overall_performance = self._calculate_overall_performance(training_results)
            
            self.logger.info(f"跨度预测器训练完成: {overall_performance}")
            return overall_performance

        except Exception as e:
            self.logger.error(f"训练失败: {e}")
            raise

    def _calculate_overall_performance(self, training_results: Dict[str, Any]) -> Dict[str, Any]:
        """计算整体性能"""
        performance = {
            'trained_models': len([r for r in training_results.values() if 'error' not in r]),
            'failed_models': len([r for r in training_results.values() if 'error' in r]),
            'overall_accuracy': 0.0,
            'overall_mae': 0.0,
            'model_results': training_results
        }

        # 计算平均性能
        valid_results = [r for r in training_results.values() if 'error' not in r and isinstance(r, dict)]
        if valid_results:
            accuracies = [r.get('accuracy', 0) for r in valid_results]
            maes = [r.get('mae', 10) for r in valid_results]
            
            performance['overall_accuracy'] = np.mean(accuracies)
            performance['overall_mae'] = np.mean(maes)

        return performance

    def predict_probability(self, X: np.ndarray) -> np.ndarray:
        """
        预测跨度概率分布（适配BaseIndependentPredictor接口）

        Args:
            X: 特征矩阵

        Returns:
            概率分布数组，shape: (n_samples, 10)
        """
        if not self.is_trained:
            raise ValueError("模型尚未训练")

        try:
            # 使用分类模型预测概率分布
            if 'classification' in self.models and self.models['classification'].is_trained:
                probabilities = self.models['classification'].predict_proba(X)
                return probabilities
            else:
                # 如果分类模型不可用，使用回归模型转换为概率
                predictions = self.predict(X)
                probabilities = self._convert_regression_to_probability(predictions)
                return probabilities

        except Exception as e:
            self.logger.error(f"预测概率分布失败: {e}")
            raise

    def _convert_regression_to_probability(self, predictions: np.ndarray) -> np.ndarray:
        """将回归预测转换为概率分布"""
        probabilities = np.zeros((len(predictions), 10))
        
        for i, pred in enumerate(predictions):
            # 创建以预测值为中心的正态分布
            pred_int = int(np.round(np.clip(pred, 0, 9)))
            prob_dist = np.zeros(10)
            
            # 主要概率给预测值
            prob_dist[pred_int] = 0.6
            
            # 相邻值分配较小概率
            if pred_int > 0:
                prob_dist[pred_int - 1] = 0.2
            if pred_int < 9:
                prob_dist[pred_int + 1] = 0.2
            
            # 归一化
            prob_dist = prob_dist / np.sum(prob_dist)
            probabilities[i] = prob_dist
        
        return probabilities

    def predict(self, X: np.ndarray, model_name: Optional[str] = None) -> np.ndarray:
        """
        预测跨度

        Args:
            X: 特征矩阵
            model_name: 使用的模型名称，None表示使用当前模型

        Returns:
            预测的跨度数组
        """
        if not self.is_trained:
            raise ValueError("模型尚未训练")

        model_to_use = model_name or self.current_model

        if model_to_use not in self.models:
            raise ValueError(f"未知的模型名称: {model_to_use}")

        try:
            predictions = self.models[model_to_use].predict(X)
            return predictions
        except Exception as e:
            self.logger.error(f"预测失败: {e}")
            raise

    def predict_with_confidence(self, X: np.ndarray, model_name: Optional[str] = None) -> Tuple[np.ndarray, np.ndarray]:
        """
        预测跨度及置信度

        Args:
            X: 特征矩阵
            model_name: 使用的模型名称

        Returns:
            预测值和置信度数组
        """
        model_to_use = model_name or self.current_model

        if model_to_use not in self.models:
            raise ValueError(f"未知的模型名称: {model_to_use}")

        try:
            if hasattr(self.models[model_to_use], 'predict_with_confidence'):
                return self.models[model_to_use].predict_with_confidence(X)
            else:
                predictions = self.models[model_to_use].predict(X)
                confidences = np.full(len(predictions), 0.7)  # 默认置信度
                return predictions, confidences
        except Exception as e:
            self.logger.error(f"预测失败: {e}")
            raise

    def set_position_predictors(self, hundreds_predictor, tens_predictor, units_predictor):
        """
        设置位置预测器（用于双重约束优化）

        Args:
            hundreds_predictor: 百位预测器
            tens_predictor: 十位预测器
            units_predictor: 个位预测器
        """
        self.position_predictors = {
            'hundreds': hundreds_predictor,
            'tens': tens_predictor,
            'units': units_predictor
        }

        # 更新约束模型的位置预测器
        if 'constraint' in self.models:
            self.models['constraint'].set_base_models({
                'hundreds': hundreds_predictor,
                'tens': tens_predictor,
                'units': units_predictor
            })

        self.logger.info("设置位置预测器完成")

    def set_sum_predictor(self, sum_predictor):
        """
        设置和值预测器（用于双重约束优化）

        Args:
            sum_predictor: 和值预测器
        """
        self.sum_predictor = sum_predictor
        self.logger.info("设置和值预测器完成")

    def switch_model(self, model_name: str):
        """
        切换当前使用的模型

        Args:
            model_name: 模型名称
        """
        if model_name not in self.models:
            raise ValueError(f"未知的模型名称: {model_name}")

        self.current_model = model_name
        self.logger.info(f"切换到模型: {model_name}")

    def get_available_models(self) -> List[str]:
        """获取可用的模型列表"""
        return list(self.models.keys())

    def get_model_info(self, model_name: Optional[str] = None) -> Dict[str, Any]:
        """
        获取模型信息

        Args:
            model_name: 模型名称，None表示获取当前模型信息

        Returns:
            模型信息字典
        """
        model_to_use = model_name or self.current_model

        if model_to_use not in self.models:
            raise ValueError(f"未知的模型名称: {model_to_use}")

        base_info = {
            'position': self.position,
            'current_model': self.current_model,
            'available_models': self.get_available_models(),
            'is_trained': self.is_trained,
            'prediction_range': self.prediction_range,
            'target_type': self.target_type,
            'dual_constraints_enabled': self.dual_constraints_enabled,
            'pattern_analysis_enabled': self.pattern_analysis_enabled
        }

        # 添加特定模型信息
        if hasattr(self.models[model_to_use], 'get_model_info'):
            model_specific_info = self.models[model_to_use].get_model_info()
            base_info.update(model_specific_info)

        return base_info

    def train_all_models(self) -> Dict[str, Any]:
        """
        训练所有模型的便捷方法

        Returns:
            训练结果字典
        """
        try:
            # 加载训练数据
            X, y = self.load_training_data()

            # 训练所有模型
            return self.train(X, y)

        except Exception as e:
            self.logger.error(f"训练所有模型失败: {e}")
            raise

    def predict_next_period_with_constraints(self, current_issue: str) -> Dict[str, Any]:
        """
        使用双重约束预测下一期跨度（专属特征）

        Args:
            current_issue: 当前期号

        Returns:
            预测结果字典
        """
        if not self.is_trained:
            raise ValueError("跨度预测器尚未训练")

        try:
            start_time = time.time()

            # 获取预测特征
            features = self._get_prediction_features(current_issue)
            X = np.array(features).reshape(1, -1)

            # 基础预测
            base_predictions, base_confidences = self.predict_with_confidence(X)
            base_prediction = base_predictions[0]
            base_confidence = base_confidences[0]

            result = {
                'position': self.position,
                'issue': current_issue,
                'base_prediction': float(base_prediction),
                'base_confidence': float(base_confidence),
                'model_type': self.current_model,
                'prediction_time': 0.0
            }

            # 如果启用双重约束优化
            if self.dual_constraints_enabled and 'constraint' in self.models:
                try:
                    # 获取位置预测
                    position_predictions = self._get_position_predictions(current_issue)

                    # 获取和值预测
                    sum_prediction = self._get_sum_prediction(current_issue)

                    # 约束优化预测
                    constraint_model = self.models['constraint']
                    optimized_predictions, constraint_info = constraint_model.predict_with_constraints(
                        X, position_predictions, np.array([sum_prediction])
                    )

                    result.update({
                        'optimized_prediction': float(optimized_predictions[0]),
                        'constraint_info': constraint_info['constraint_details'][0] if constraint_info['constraint_details'] else {},
                        'dual_constraint_enabled': True
                    })

                except Exception as e:
                    self.logger.warning(f"双重约束优化失败: {e}")
                    result.update({
                        'optimized_prediction': float(base_prediction),
                        'constraint_info': {},
                        'dual_constraint_enabled': False,
                        'constraint_error': str(e)
                    })
            else:
                result.update({
                    'optimized_prediction': float(base_prediction),
                    'dual_constraint_enabled': False
                })

            # 模式分析（专属特征）
            if self.pattern_analysis_enabled:
                try:
                    pattern_analysis = self._analyze_span_patterns(current_issue)
                    result['pattern_analysis'] = pattern_analysis
                except Exception as e:
                    self.logger.warning(f"模式分析失败: {e}")
                    result['pattern_analysis'] = {}

            # 分类预测（如果可用）
            if 'classification' in self.models and self.models['classification'].is_trained:
                try:
                    probabilities = self.models['classification'].predict_proba(X)
                    top_k_indices, top_k_probs = self.models['classification'].predict_top_k(X, k=3)

                    result.update({
                        'probabilities': probabilities[0].tolist(),
                        'top_3_predictions': [
                            {'span': int(idx), 'probability': float(prob)}
                            for idx, prob in zip(top_k_indices[0], top_k_probs[0])
                        ]
                    })
                except Exception as e:
                    self.logger.warning(f"分类预测失败: {e}")

            result['prediction_time'] = time.time() - start_time

            self.logger.info(f"跨度预测完成: {current_issue}, 预测值: {result.get('optimized_prediction', base_prediction):.2f}")

            return result

        except Exception as e:
            self.logger.error(f"预测失败: {e}")
            raise

    def _get_position_predictions(self, issue: str) -> Dict[str, np.ndarray]:
        """获取位置预测结果"""
        predictions = {}

        for position, predictor in self.position_predictors.items():
            if predictor is not None:
                try:
                    # 这里应该调用位置预测器的预测方法
                    # 暂时使用默认值
                    predictions[f'{position}_pred'] = np.array([5.0])  # 默认值
                    predictions[f'{position}_prob'] = np.ones((1, 10)) / 10  # 均匀分布
                except Exception as e:
                    self.logger.warning(f"获取{position}预测失败: {e}")
                    predictions[f'{position}_pred'] = np.array([5.0])
                    predictions[f'{position}_prob'] = np.ones((1, 10)) / 10
            else:
                predictions[f'{position}_pred'] = np.array([5.0])
                predictions[f'{position}_prob'] = np.ones((1, 10)) / 10

        return predictions

    def _get_sum_prediction(self, issue: str) -> float:
        """获取和值预测结果"""
        if self.sum_predictor is not None:
            try:
                # 这里应该调用和值预测器的预测方法
                # 暂时使用默认值
                return 13.5  # 默认和值
            except Exception as e:
                self.logger.warning(f"获取和值预测失败: {e}")
                return 13.5
        else:
            return 13.5

    def _analyze_span_patterns(self, issue: str) -> Dict[str, Any]:
        """分析跨度模式（专属特征）"""
        try:
            # 获取最近的历史数据进行模式分析
            recent_data = self.data_access.load_lottery_data(limit=20)

            if recent_data.empty:
                return {}

            # 计算跨度
            recent_data['span'] = (
                recent_data[['hundreds', 'tens', 'units']].max(axis=1) -
                recent_data[['hundreds', 'tens', 'units']].min(axis=1)
            )

            # 模式统计
            pattern_stats = {
                'recent_spans': recent_data['span'].tolist()[-10:],
                'avg_span': float(recent_data['span'].mean()),
                'span_variance': float(recent_data['span'].var()),
                'most_frequent_span': int(recent_data['span'].mode().iloc[0]) if not recent_data['span'].mode().empty else 4
            }

            # 分析最近的模式
            recent_patterns = []
            for _, row in recent_data.tail(5).iterrows():
                digits = [row['hundreds'], row['tens'], row['units']]
                pattern_analysis = self.data_access.analyze_span_patterns(digits[0], digits[1], digits[2])
                recent_patterns.append(pattern_analysis)

            pattern_stats['recent_patterns'] = recent_patterns

            return pattern_stats

        except Exception as e:
            self.logger.error(f"模式分析失败: {e}")
            return {}

    def evaluate_model(self, model_name: Optional[str] = None, test_size: float = 0.2) -> Dict[str, Any]:
        """
        评估模型性能

        Args:
            model_name: 模型名称，None表示评估当前模型
            test_size: 测试集比例

        Returns:
            评估结果字典
        """
        model_to_use = model_name or self.current_model

        if model_to_use not in self.models:
            raise ValueError(f"未知的模型名称: {model_to_use}")

        try:
            # 加载数据
            X, y = self.load_training_data()

            # 分割数据
            from sklearn.model_selection import train_test_split
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=test_size, random_state=42
            )

            # 评估模型
            model = self.models[model_to_use]
            if hasattr(model, 'evaluate'):
                evaluation_result = model.evaluate(X_test, y_test)
            else:
                # 基础评估
                predictions = model.predict(X_test)
                mae = float(np.mean(np.abs(y_test - predictions)))
                accuracy = float(np.mean(np.abs(y_test - predictions) <= 1))

                evaluation_result = {
                    'mae': mae,
                    'accuracy': accuracy,
                    'test_samples': len(y_test)
                }

            evaluation_result.update({
                'model_name': model_to_use,
                'test_size': test_size,
                'evaluation_time': time.time()
            })

            return evaluation_result

        except Exception as e:
            self.logger.error(f"模型评估失败: {e}")
            raise

    def save_model(self, model_name: Optional[str] = None, filepath: Optional[str] = None) -> str:
        """
        保存模型

        Args:
            model_name: 模型名称，None表示保存当前模型
            filepath: 保存路径，None则使用默认路径

        Returns:
            实际保存路径
        """
        model_to_use = model_name or self.current_model

        if model_to_use not in self.models:
            raise ValueError(f"未知的模型名称: {model_to_use}")

        if not self.is_trained:
            raise ValueError("模型尚未训练，无法保存")

        try:
            if filepath is None:
                model_dir = Path(f'models/span_predictor/')
                model_dir.mkdir(parents=True, exist_ok=True)
                filepath = model_dir / f"{model_to_use}_span_model.pkl"

            # 保存模型
            success = self.models[model_to_use].save_model(str(filepath))

            if success:
                self.logger.info(f"模型 {model_to_use} 保存到: {filepath}")
                return str(filepath)
            else:
                raise RuntimeError(f"保存模型 {model_to_use} 失败")

        except Exception as e:
            self.logger.error(f"保存模型失败: {e}")
            raise

    def load_model(self, model_name: str, filepath: str) -> bool:
        """
        加载模型

        Args:
            model_name: 模型名称
            filepath: 模型文件路径

        Returns:
            是否加载成功
        """
        if model_name not in self.models:
            raise ValueError(f"未知的模型名称: {model_name}")

        try:
            success = self.models[model_name].load_model(filepath)

            if success:
                self.logger.info(f"模型 {model_name} 加载成功: {filepath}")

                # 检查是否所有模型都已训练
                trained_models = sum(1 for model in self.models.values() if model.is_trained)
                if trained_models > 0:
                    self.is_trained = True

                return True
            else:
                self.logger.error(f"模型 {model_name} 加载失败")
                return False

        except Exception as e:
            self.logger.error(f"加载模型失败: {e}")
            return False

    def get_model_performance_comparison(self) -> Dict[str, Any]:
        """
        获取所有模型的性能比较

        Returns:
            模型性能比较字典
        """
        try:
            # 加载测试数据
            X, y = self.load_training_data()

            # 分割数据
            from sklearn.model_selection import train_test_split
            _, X_test, _, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

            performance_comparison = {}

            for model_name, model in self.models.items():
                if model.is_trained:
                    try:
                        if hasattr(model, 'evaluate'):
                            performance = model.evaluate(X_test, y_test)
                        else:
                            predictions = model.predict(X_test)
                            mae = float(np.mean(np.abs(y_test - predictions)))
                            accuracy = float(np.mean(np.abs(y_test - predictions) <= 1))
                            performance = {'mae': mae, 'accuracy': accuracy}

                        performance_comparison[model_name] = performance
                    except Exception as e:
                        self.logger.warning(f"评估模型 {model_name} 失败: {e}")
                        performance_comparison[model_name] = {'error': str(e)}
                else:
                    performance_comparison[model_name] = {'status': 'not_trained'}

            return performance_comparison

        except Exception as e:
            self.logger.error(f"模型性能比较失败: {e}")
            return {}

    def get_best_model(self, metric: str = 'accuracy') -> str:
        """
        获取最佳模型

        Args:
            metric: 评估指标 ('accuracy', 'mae')

        Returns:
            最佳模型名称
        """
        try:
            performance_comparison = self.get_model_performance_comparison()

            best_model = None
            best_score = float('-inf') if metric == 'accuracy' else float('inf')

            for model_name, performance in performance_comparison.items():
                if 'error' in performance or 'status' in performance:
                    continue

                score = performance.get(metric)
                if score is not None:
                    if metric == 'accuracy' and score > best_score:
                        best_score = score
                        best_model = model_name
                    elif metric == 'mae' and score < best_score:
                        best_score = score
                        best_model = model_name

            return best_model or self.current_model

        except Exception as e:
            self.logger.error(f"获取最佳模型失败: {e}")
            return self.current_model

    def auto_select_best_model(self, metric: str = 'accuracy'):
        """
        自动选择最佳模型

        Args:
            metric: 评估指标
        """
        best_model = self.get_best_model(metric)
        if best_model != self.current_model:
            self.switch_model(best_model)
            self.logger.info(f"自动切换到最佳模型: {best_model} (基于{metric})")

    def predict_with_all_models(self, X: np.ndarray) -> Dict[str, np.ndarray]:
        """
        使用所有模型进行预测

        Args:
            X: 特征矩阵

        Returns:
            所有模型的预测结果字典
        """
        predictions = {}

        for model_name, model in self.models.items():
            if model.is_trained:
                try:
                    pred = model.predict(X)
                    predictions[model_name] = pred
                except Exception as e:
                    self.logger.warning(f"模型 {model_name} 预测失败: {e}")
                    predictions[model_name] = np.full(len(X), 4.5)  # 默认值

        return predictions

    def get_ensemble_prediction_details(self, X: np.ndarray) -> Dict[str, Any]:
        """
        获取集成预测的详细信息

        Args:
            X: 特征矩阵

        Returns:
            集成预测详细信息
        """
        if 'ensemble' not in self.models or not self.models['ensemble'].is_trained:
            raise ValueError("集成模型未训练")

        try:
            # 获取所有模型预测
            all_predictions = self.predict_with_all_models(X)

            # 获取集成模型的贡献信息
            ensemble_model = self.models['ensemble']
            if hasattr(ensemble_model, 'get_model_contributions'):
                contributions = ensemble_model.get_model_contributions(X)
            else:
                contributions = {}

            # 集成预测
            ensemble_predictions = ensemble_model.predict(X)

            return {
                'individual_predictions': {k: v.tolist() for k, v in all_predictions.items()},
                'ensemble_prediction': ensemble_predictions.tolist(),
                'model_contributions': contributions,
                'model_weights': ensemble_model.get_model_weights() if hasattr(ensemble_model, 'get_model_weights') else {}
            }

        except Exception as e:
            self.logger.error(f"获取集成预测详情失败: {e}")
            raise

    def retrain_model(self, model_name: str) -> Dict[str, Any]:
        """
        重新训练指定模型

        Args:
            model_name: 模型名称

        Returns:
            训练结果
        """
        if model_name not in self.models:
            raise ValueError(f"未知的模型名称: {model_name}")

        try:
            # 加载训练数据
            X, y = self.load_training_data()

            # 重新训练模型
            self.logger.info(f"重新训练模型: {model_name}")
            result = self.models[model_name].train(X, y)

            # 更新训练历史
            if 'model_results' not in self.training_history:
                self.training_history['model_results'] = {}
            self.training_history['model_results'][model_name] = result

            self.logger.info(f"模型 {model_name} 重新训练完成")
            return result

        except Exception as e:
            self.logger.error(f"重新训练模型 {model_name} 失败: {e}")
            raise

    def save_all_models(self, base_dir: Optional[str] = None) -> Dict[str, str]:
        """
        保存所有训练好的模型

        Args:
            base_dir: 基础目录

        Returns:
            保存路径字典
        """
        if base_dir is None:
            base_dir = "models/span_predictor"

        base_path = Path(base_dir)
        base_path.mkdir(parents=True, exist_ok=True)

        saved_paths = {}

        for model_name, model in self.models.items():
            if model.is_trained:
                try:
                    filepath = base_path / f"{model_name}_span_model.pkl"
                    success = model.save_model(str(filepath))

                    if success:
                        saved_paths[model_name] = str(filepath)
                        self.logger.info(f"模型 {model_name} 已保存")
                    else:
                        self.logger.warning(f"模型 {model_name} 保存失败")

                except Exception as e:
                    self.logger.error(f"保存模型 {model_name} 失败: {e}")

        return saved_paths

    def load_all_models(self, base_dir: str) -> Dict[str, bool]:
        """
        加载所有模型

        Args:
            base_dir: 基础目录

        Returns:
            加载结果字典
        """
        base_path = Path(base_dir)
        load_results = {}

        for model_name in self.models.keys():
            filepath = base_path / f"{model_name}_span_model.pkl"

            if filepath.exists():
                try:
                    success = self.models[model_name].load_model(str(filepath))
                    load_results[model_name] = success

                    if success:
                        self.logger.info(f"模型 {model_name} 加载成功")
                    else:
                        self.logger.warning(f"模型 {model_name} 加载失败")

                except Exception as e:
                    self.logger.error(f"加载模型 {model_name} 失败: {e}")
                    load_results[model_name] = False
            else:
                self.logger.warning(f"模型文件不存在: {filepath}")
                load_results[model_name] = False

        # 检查是否有模型成功加载
        if any(load_results.values()):
            self.is_trained = True

        return load_results

    def get_training_summary(self) -> Dict[str, Any]:
        """
        获取训练摘要

        Returns:
            训练摘要字典
        """
        summary = {
            'total_models': len(self.models),
            'trained_models': sum(1 for model in self.models.values() if model.is_trained),
            'current_model': self.current_model,
            'is_trained': self.is_trained,
            'training_history': self.training_history,
            'model_status': {}
        }

        for model_name, model in self.models.items():
            summary['model_status'][model_name] = {
                'is_trained': model.is_trained,
                'model_type': model.model_type
            }

        return summary

    # ==================== 双重约束优化功能（专属特征） ====================

    def predict_with_dual_constraints(self, X: np.ndarray,
                                    position_predictions: Optional[Dict[str, np.ndarray]] = None,
                                    sum_predictions: Optional[np.ndarray] = None) -> Tuple[np.ndarray, Dict[str, Any]]:
        """
        使用双重约束进行预测（专属特征）

        Args:
            X: 特征矩阵
            position_predictions: 位置预测结果字典
            sum_predictions: 和值预测结果

        Returns:
            优化后的预测结果和约束信息
        """
        if not self.dual_constraints_enabled:
            # 如果未启用双重约束，返回基础预测
            base_predictions = self.predict(X)
            return base_predictions, {'dual_constraint_enabled': False}

        if 'constraint' not in self.models or not self.models['constraint'].is_trained:
            self.logger.warning("约束模型未训练，使用基础预测")
            base_predictions = self.predict(X)
            return base_predictions, {'dual_constraint_enabled': False, 'reason': 'constraint_model_not_trained'}

        try:
            # 获取位置预测（如果未提供）
            if position_predictions is None:
                position_predictions = self._get_default_position_predictions(len(X))

            # 获取和值预测（如果未提供）
            if sum_predictions is None:
                sum_predictions = self._get_default_sum_predictions(len(X))

            # 使用约束模型进行优化
            constraint_model = self.models['constraint']
            optimized_predictions, constraint_info = constraint_model.predict_with_constraints(
                X, position_predictions, sum_predictions
            )

            return optimized_predictions, constraint_info

        except Exception as e:
            self.logger.error(f"双重约束预测失败: {e}")
            base_predictions = self.predict(X)
            return base_predictions, {'dual_constraint_enabled': False, 'error': str(e)}

    def _get_default_position_predictions(self, sample_count: int) -> Dict[str, np.ndarray]:
        """获取默认位置预测"""
        return {
            'hundreds_pred': np.full(sample_count, 5.0),
            'tens_pred': np.full(sample_count, 5.0),
            'units_pred': np.full(sample_count, 5.0),
            'hundreds_prob': np.ones((sample_count, 10)) / 10,
            'tens_prob': np.ones((sample_count, 10)) / 10,
            'units_prob': np.ones((sample_count, 10)) / 10
        }

    def _get_default_sum_predictions(self, sample_count: int) -> np.ndarray:
        """获取默认和值预测"""
        return np.full(sample_count, 13.5)

    def calculate_constraint_consistency_score(self, span_prediction: float,
                                             position_predictions: Dict[str, float],
                                             sum_prediction: float) -> Dict[str, float]:
        """
        计算约束一致性评分

        Args:
            span_prediction: 跨度预测值
            position_predictions: 位置预测字典
            sum_prediction: 和值预测值

        Returns:
            约束一致性评分字典
        """
        try:
            # 提取位置预测
            h_pred = position_predictions.get('hundreds', 5.0)
            t_pred = position_predictions.get('tens', 5.0)
            u_pred = position_predictions.get('units', 5.0)

            # 计算基于位置的跨度
            position_span = max(h_pred, t_pred, u_pred) - min(h_pred, t_pred, u_pred)

            # 位置约束一致性
            position_consistency = 1.0 - abs(span_prediction - position_span) / 9.0
            position_consistency = max(0.0, min(1.0, position_consistency))

            # 和值约束一致性（基于经验关系）
            expected_sum = h_pred + t_pred + u_pred
            sum_deviation = abs(sum_prediction - expected_sum)
            sum_consistency = 1.0 - sum_deviation / 27.0  # 最大可能偏差
            sum_consistency = max(0.0, min(1.0, sum_consistency))

            # 双重约束一致性（加权平均）
            dual_consistency = (
                self.span_config.get('dual_constraints', {}).get('position_weight', 0.4) * position_consistency +
                self.span_config.get('dual_constraints', {}).get('sum_weight', 0.4) * sum_consistency
            ) / 0.8  # 归一化

            return {
                'position_consistency': position_consistency,
                'sum_consistency': sum_consistency,
                'dual_consistency': dual_consistency,
                'position_span': position_span,
                'expected_sum': expected_sum,
                'sum_deviation': sum_deviation
            }

        except Exception as e:
            self.logger.error(f"计算约束一致性失败: {e}")
            return {
                'position_consistency': 0.5,
                'sum_consistency': 0.5,
                'dual_consistency': 0.5,
                'error': str(e)
            }

    def optimize_span_with_constraints(self, base_prediction: float,
                                     position_predictions: Dict[str, float],
                                     sum_prediction: float,
                                     optimization_weights: Optional[Dict[str, float]] = None) -> Dict[str, Any]:
        """
        基于约束优化跨度预测

        Args:
            base_prediction: 基础预测值
            position_predictions: 位置预测字典
            sum_prediction: 和值预测值
            optimization_weights: 优化权重

        Returns:
            优化结果字典
        """
        if optimization_weights is None:
            optimization_weights = {
                'base': 0.2,
                'position': 0.4,
                'sum': 0.4
            }

        try:
            # 计算基于位置的跨度
            h_pred = position_predictions.get('hundreds', 5.0)
            t_pred = position_predictions.get('tens', 5.0)
            u_pred = position_predictions.get('units', 5.0)
            position_span = max(h_pred, t_pred, u_pred) - min(h_pred, t_pred, u_pred)

            # 基于和值估算跨度（经验公式）
            sum_based_span = self._estimate_span_from_sum(sum_prediction)

            # 加权优化
            optimized_span = (
                optimization_weights['base'] * base_prediction +
                optimization_weights['position'] * position_span +
                optimization_weights['sum'] * sum_based_span
            )

            # 应用范围约束
            optimized_span = np.clip(optimized_span, 0, 9)

            # 计算约束一致性
            consistency_scores = self.calculate_constraint_consistency_score(
                optimized_span, position_predictions, sum_prediction
            )

            return {
                'base_prediction': base_prediction,
                'position_span': position_span,
                'sum_based_span': sum_based_span,
                'optimized_span': optimized_span,
                'optimization_weights': optimization_weights,
                'consistency_scores': consistency_scores
            }

        except Exception as e:
            self.logger.error(f"约束优化失败: {e}")
            return {
                'base_prediction': base_prediction,
                'optimized_span': base_prediction,
                'error': str(e)
            }

    def _estimate_span_from_sum(self, sum_value: float) -> float:
        """
        基于和值估算跨度（经验公式）

        Args:
            sum_value: 和值

        Returns:
            估算的跨度
        """
        # 经验公式：和值越大，跨度可能越大（但不是线性关系）
        # 这里使用简化的经验公式
        if sum_value <= 3:
            return 1.0  # 最小和值对应较小跨度
        elif sum_value <= 6:
            return 2.0
        elif sum_value <= 9:
            return 3.0
        elif sum_value <= 12:
            return 4.0
        elif sum_value <= 15:
            return 5.0
        elif sum_value <= 18:
            return 6.0
        elif sum_value <= 21:
            return 7.0
        elif sum_value <= 24:
            return 8.0
        else:
            return 9.0  # 最大和值对应较大跨度

    def evaluate_constraint_effectiveness(self, test_size: float = 0.2) -> Dict[str, Any]:
        """
        评估约束优化的有效性

        Args:
            test_size: 测试集比例

        Returns:
            约束优化评估结果
        """
        try:
            # 加载数据
            X, y = self.load_training_data()

            # 分割数据
            from sklearn.model_selection import train_test_split
            _, X_test, _, y_test = train_test_split(X, y, test_size=test_size, random_state=42)

            # 基础预测
            base_predictions = self.predict(X_test)
            base_mae = float(np.mean(np.abs(y_test - base_predictions)))
            base_accuracy = float(np.mean(np.abs(y_test - base_predictions) <= 1))

            # 约束优化预测
            if self.dual_constraints_enabled and 'constraint' in self.models:
                try:
                    constraint_predictions, _ = self.predict_with_dual_constraints(X_test)
                    constraint_mae = float(np.mean(np.abs(y_test - constraint_predictions)))
                    constraint_accuracy = float(np.mean(np.abs(y_test - constraint_predictions) <= 1))

                    improvement = {
                        'mae_improvement': base_mae - constraint_mae,
                        'accuracy_improvement': constraint_accuracy - base_accuracy,
                        'mae_improvement_percent': ((base_mae - constraint_mae) / base_mae * 100) if base_mae > 0 else 0,
                        'accuracy_improvement_percent': ((constraint_accuracy - base_accuracy) / base_accuracy * 100) if base_accuracy > 0 else 0
                    }
                except Exception as e:
                    self.logger.error(f"约束预测评估失败: {e}")
                    constraint_mae = base_mae
                    constraint_accuracy = base_accuracy
                    improvement = {'error': str(e)}
            else:
                constraint_mae = base_mae
                constraint_accuracy = base_accuracy
                improvement = {'reason': 'constraint_not_enabled'}

            return {
                'base_performance': {
                    'mae': base_mae,
                    'accuracy': base_accuracy
                },
                'constraint_performance': {
                    'mae': constraint_mae,
                    'accuracy': constraint_accuracy
                },
                'improvement': improvement,
                'test_samples': len(y_test)
            }

        except Exception as e:
            self.logger.error(f"约束有效性评估失败: {e}")
            return {'error': str(e)}

    def enable_dual_constraints(self, enable: bool = True):
        """
        启用/禁用双重约束优化

        Args:
            enable: 是否启用
        """
        self.dual_constraints_enabled = enable
        self.logger.info(f"双重约束优化: {'启用' if enable else '禁用'}")

    def get_constraint_configuration(self) -> Dict[str, Any]:
        """
        获取约束配置信息

        Returns:
            约束配置字典
        """
        return {
            'dual_constraints_enabled': self.dual_constraints_enabled,
            'pattern_analysis_enabled': self.pattern_analysis_enabled,
            'constraint_config': self.span_config.get('dual_constraints', {}),
            'pattern_config': self.span_config.get('pattern_analysis', {}),
            'position_predictors_set': all(p is not None for p in self.position_predictors.values()),
            'sum_predictor_set': self.sum_predictor is not None,
            'constraint_model_trained': 'constraint' in self.models and self.models['constraint'].is_trained
        }

    # ==================== 模式分析功能（专属特征） ====================

    def analyze_comprehensive_patterns(self, issue: str, analysis_depth: int = 20) -> Dict[str, Any]:
        """
        全面的跨度模式分析（专属特征）

        Args:
            issue: 期号
            analysis_depth: 分析深度（历史期数）

        Returns:
            全面的模式分析结果
        """
        try:
            # 获取历史数据
            recent_data = self.data_access.load_lottery_data(limit=analysis_depth)

            if recent_data.empty:
                return {'error': 'no_historical_data'}

            # 计算跨度
            recent_data['span'] = (
                recent_data[['hundreds', 'tens', 'units']].max(axis=1) -
                recent_data[['hundreds', 'tens', 'units']].min(axis=1)
            )

            # 基础统计分析
            basic_stats = self._analyze_basic_span_statistics(recent_data)

            # 模式识别分析
            pattern_analysis = self._analyze_span_patterns_detailed(recent_data)

            # 趋势分析
            trend_analysis = self._analyze_span_trends(recent_data)

            # 周期性分析
            cyclical_analysis = self._analyze_span_cycles(recent_data)

            # 相关性分析
            correlation_analysis = self._analyze_span_correlations(recent_data)

            return {
                'issue': issue,
                'analysis_depth': analysis_depth,
                'basic_statistics': basic_stats,
                'pattern_analysis': pattern_analysis,
                'trend_analysis': trend_analysis,
                'cyclical_analysis': cyclical_analysis,
                'correlation_analysis': correlation_analysis,
                'analysis_timestamp': time.time()
            }

        except Exception as e:
            self.logger.error(f"全面模式分析失败: {e}")
            return {'error': str(e)}

    def _analyze_basic_span_statistics(self, data: pd.DataFrame) -> Dict[str, Any]:
        """分析基础跨度统计"""
        spans = data['span'].values

        return {
            'mean': float(np.mean(spans)),
            'median': float(np.median(spans)),
            'std': float(np.std(spans)),
            'min': int(np.min(spans)),
            'max': int(np.max(spans)),
            'mode': int(np.bincount(spans.astype(int)).argmax()),
            'distribution': np.bincount(spans.astype(int), minlength=10).tolist(),
            'entropy': float(-np.sum(np.bincount(spans.astype(int)) / len(spans) *
                                   np.log(np.bincount(spans.astype(int)) / len(spans) + 1e-8)))
        }

    def _analyze_span_patterns_detailed(self, data: pd.DataFrame) -> Dict[str, Any]:
        """详细的跨度模式分析"""
        pattern_counts = {
            'ascending': 0,
            'descending': 0,
            'same_digit': 0,
            'consecutive': 0,
            'has_duplicate': 0,
            'all_even': 0,
            'all_odd': 0,
            'mixed_parity': 0
        }

        pattern_spans = {pattern: [] for pattern in pattern_counts.keys()}

        for _, row in data.iterrows():
            digits = [row['hundreds'], row['tens'], row['units']]
            span = row['span']

            # 升序模式
            if digits == sorted(digits) and len(set(digits)) == 3:
                pattern_counts['ascending'] += 1
                pattern_spans['ascending'].append(span)

            # 降序模式
            if digits == sorted(digits, reverse=True) and len(set(digits)) == 3:
                pattern_counts['descending'] += 1
                pattern_spans['descending'].append(span)

            # 相同数字模式
            if len(set(digits)) == 1:
                pattern_counts['same_digit'] += 1
                pattern_spans['same_digit'].append(span)

            # 连续数字模式
            if self._is_consecutive_digits(digits):
                pattern_counts['consecutive'] += 1
                pattern_spans['consecutive'].append(span)

            # 有重复数字
            if len(set(digits)) < 3:
                pattern_counts['has_duplicate'] += 1
                pattern_spans['has_duplicate'].append(span)

            # 全偶数
            if all(d % 2 == 0 for d in digits):
                pattern_counts['all_even'] += 1
                pattern_spans['all_even'].append(span)

            # 全奇数
            if all(d % 2 == 1 for d in digits):
                pattern_counts['all_odd'] += 1
                pattern_spans['all_odd'].append(span)

            # 奇偶混合
            if not all(d % 2 == 0 for d in digits) and not all(d % 2 == 1 for d in digits):
                pattern_counts['mixed_parity'] += 1
                pattern_spans['mixed_parity'].append(span)

        # 计算模式概率和平均跨度
        total_samples = len(data)
        pattern_analysis = {}

        for pattern, count in pattern_counts.items():
            spans_for_pattern = pattern_spans[pattern]
            pattern_analysis[pattern] = {
                'count': count,
                'probability': count / total_samples if total_samples > 0 else 0,
                'avg_span': float(np.mean(spans_for_pattern)) if spans_for_pattern else 0,
                'span_distribution': np.bincount(np.array(spans_for_pattern, dtype=int), minlength=10).tolist() if spans_for_pattern else [0] * 10
            }

        return pattern_analysis

    def _is_consecutive_digits(self, digits: List[int]) -> bool:
        """判断是否为连续数字"""
        if len(set(digits)) != 3:
            return False
        sorted_digits = sorted(digits)
        return (sorted_digits[1] - sorted_digits[0] == 1 and
                sorted_digits[2] - sorted_digits[1] == 1)

    def _analyze_span_trends(self, data: pd.DataFrame) -> Dict[str, Any]:
        """分析跨度趋势"""
        spans = data['span'].values

        if len(spans) < 3:
            return {'error': 'insufficient_data'}

        # 计算移动平均
        window_sizes = [3, 5, 7]
        moving_averages = {}

        for window in window_sizes:
            if len(spans) >= window:
                ma = np.convolve(spans, np.ones(window)/window, mode='valid')
                moving_averages[f'ma_{window}'] = ma.tolist()

        # 趋势方向
        recent_trend = 'stable'
        if len(spans) >= 5:
            recent_spans = spans[-5:]
            if np.polyfit(range(5), recent_spans, 1)[0] > 0.1:
                recent_trend = 'increasing'
            elif np.polyfit(range(5), recent_spans, 1)[0] < -0.1:
                recent_trend = 'decreasing'

        # 波动性分析
        volatility = float(np.std(np.diff(spans))) if len(spans) > 1 else 0

        return {
            'moving_averages': moving_averages,
            'recent_trend': recent_trend,
            'volatility': volatility,
            'trend_strength': abs(np.polyfit(range(len(spans)), spans, 1)[0]) if len(spans) > 1 else 0
        }

    def _analyze_span_cycles(self, data: pd.DataFrame) -> Dict[str, Any]:
        """分析跨度周期性"""
        spans = data['span'].values

        if len(spans) < 10:
            return {'error': 'insufficient_data'}

        # 简单的周期性检测
        cycle_lengths = [3, 5, 7, 10]
        cycle_analysis = {}

        for cycle_len in cycle_lengths:
            if len(spans) >= cycle_len * 2:
                # 计算自相关
                autocorr = np.corrcoef(spans[:-cycle_len], spans[cycle_len:])[0, 1]
                cycle_analysis[f'cycle_{cycle_len}'] = {
                    'autocorrelation': float(autocorr) if not np.isnan(autocorr) else 0,
                    'strength': 'strong' if abs(autocorr) > 0.5 else 'weak' if abs(autocorr) > 0.3 else 'none'
                }

        return cycle_analysis

    def _analyze_span_correlations(self, data: pd.DataFrame) -> Dict[str, Any]:
        """分析跨度相关性"""
        try:
            # 计算跨度与位置的相关性
            correlations = {}

            for position in ['hundreds', 'tens', 'units']:
                if position in data.columns:
                    corr = np.corrcoef(data['span'], data[position])[0, 1]
                    correlations[f'span_{position}_correlation'] = float(corr) if not np.isnan(corr) else 0

            # 计算跨度与和值的相关性
            if all(col in data.columns for col in ['hundreds', 'tens', 'units']):
                sum_values = data['hundreds'] + data['tens'] + data['units']
                sum_corr = np.corrcoef(data['span'], sum_values)[0, 1]
                correlations['span_sum_correlation'] = float(sum_corr) if not np.isnan(sum_corr) else 0

            return correlations

        except Exception as e:
            self.logger.error(f"相关性分析失败: {e}")
            return {'error': str(e)}

    def predict_pattern_probability(self, digits: List[int]) -> Dict[str, float]:
        """
        预测给定数字组合的模式概率

        Args:
            digits: 数字组合 [百位, 十位, 个位]

        Returns:
            模式概率字典
        """
        try:
            span = max(digits) - min(digits)

            pattern_probs = {
                'ascending': 0.0,
                'descending': 0.0,
                'same_digit': 0.0,
                'consecutive': 0.0,
                'has_duplicate': 0.0,
                'all_even': 0.0,
                'all_odd': 0.0,
                'mixed_parity': 0.0
            }

            # 升序模式
            if digits == sorted(digits) and len(set(digits)) == 3:
                pattern_probs['ascending'] = 1.0

            # 降序模式
            if digits == sorted(digits, reverse=True) and len(set(digits)) == 3:
                pattern_probs['descending'] = 1.0

            # 相同数字模式
            if len(set(digits)) == 1:
                pattern_probs['same_digit'] = 1.0

            # 连续数字模式
            if self._is_consecutive_digits(digits):
                pattern_probs['consecutive'] = 1.0

            # 有重复数字
            if len(set(digits)) < 3:
                pattern_probs['has_duplicate'] = 1.0

            # 全偶数
            if all(d % 2 == 0 for d in digits):
                pattern_probs['all_even'] = 1.0

            # 全奇数
            if all(d % 2 == 1 for d in digits):
                pattern_probs['all_odd'] = 1.0

            # 奇偶混合
            if not all(d % 2 == 0 for d in digits) and not all(d % 2 == 1 for d in digits):
                pattern_probs['mixed_parity'] = 1.0

            pattern_probs['predicted_span'] = span

            return pattern_probs

        except Exception as e:
            self.logger.error(f"模式概率预测失败: {e}")
            return {'error': str(e)}

    def get_pattern_based_span_prediction(self, digits: List[int]) -> Dict[str, Any]:
        """
        基于模式的跨度预测

        Args:
            digits: 数字组合

        Returns:
            基于模式的预测结果
        """
        try:
            # 获取模式概率
            pattern_probs = self.predict_pattern_probability(digits)

            # 获取历史模式统计
            historical_analysis = self.analyze_comprehensive_patterns("current", 50)

            if 'error' in historical_analysis:
                return historical_analysis

            pattern_analysis = historical_analysis.get('pattern_analysis', {})

            # 基于模式预测跨度
            predicted_spans = []
            weights = []

            for pattern, prob in pattern_probs.items():
                if pattern != 'predicted_span' and pattern != 'error' and prob > 0:
                    if pattern in pattern_analysis:
                        avg_span = pattern_analysis[pattern].get('avg_span', 4.5)
                        pattern_weight = pattern_analysis[pattern].get('probability', 0.1)

                        predicted_spans.append(avg_span)
                        weights.append(prob * pattern_weight)

            # 加权平均预测
            if predicted_spans and weights:
                weighted_prediction = np.average(predicted_spans, weights=weights)
            else:
                weighted_prediction = max(digits) - min(digits)  # 直接计算

            return {
                'pattern_probabilities': pattern_probs,
                'historical_patterns': pattern_analysis,
                'weighted_prediction': float(weighted_prediction),
                'direct_calculation': max(digits) - min(digits),
                'prediction_confidence': float(np.sum(weights)) if weights else 0.5
            }

        except Exception as e:
            self.logger.error(f"基于模式的跨度预测失败: {e}")
            return {'error': str(e)}

    def enable_pattern_analysis(self, enable: bool = True):
        """
        启用/禁用模式分析

        Args:
            enable: 是否启用
        """
        self.pattern_analysis_enabled = enable
        self.logger.info(f"模式分析: {'启用' if enable else '禁用'}")
