#!/usr/bin/env python3
"""
P3-百位预测器主类

统一管理百位预测的所有模型，提供简洁的预测接口。

特点：
- 统一的预测器接口
- 多模型管理和调度
- 自动模型选择
- 批量预测支持
- 性能监控集成

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import sys
from pathlib import Path
import numpy as np
import logging
import time
from typing import Dict, List, Optional, Any, Union

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 导入模型类
try:
    from src.predictors.models.xgb_hundreds_model import XGBHundredsModel
    from src.predictors.models.lgb_hundreds_model import LGBHundredsModel
    from src.predictors.models.lstm_hundreds_model import LSTMHundredsModel
    from src.predictors.models.ensemble_hundreds_model import EnsembleHundredsModel
    from src.data.hundreds_data_access import HundredsDataAccess
    from config.config_loader import get_config, setup_logging
except ImportError as e:
    print(f"警告: 无法导入必要组件: {e}")

class HundredsPredictor:
    """P3-百位预测器主类"""
    
    def __init__(self, db_path: str = "data/lottery.db"):
        """
        初始化百位预测器
        
        Args:
            db_path: 数据库路径
        """
        self.db_path = db_path
        
        # 设置日志
        self._setup_logging()
        
        # 加载配置
        self._load_config()
        
        # 初始化数据访问层
        self.data_access = HundredsDataAccess(db_path)
        
        # 初始化模型
        self.models = {}
        self.default_model = 'ensemble'
        self._init_models()
        
        self.logger.info("P3-百位预测器初始化完成")
    
    def _setup_logging(self):
        """设置日志"""
        try:
            setup_logging()
            self.logger = logging.getLogger("HundredsPredictor")
        except:
            logging.basicConfig(level=logging.INFO)
            self.logger = logging.getLogger("HundredsPredictor")
    
    def _load_config(self):
        """加载配置"""
        try:
            self.config_loader = get_config()
            self.predictor_config = self.config_loader.load_predictor_config()
            self.prediction_config = self.config_loader.get_prediction_config()
            self.monitoring_config = self.config_loader.get_monitoring_config()
        except Exception as e:
            self.logger.warning(f"配置加载失败，使用默认配置: {e}")
            self._set_default_config()
    
    def _set_default_config(self):
        """设置默认配置"""
        self.prediction_config = {
            'confidence_threshold': 0.1,
            'top_k_predictions': 3,
            'save_predictions': True,
            'batch_prediction': False
        }
        self.monitoring_config = {
            'enabled': True,
            'save_performance': True
        }
    
    def _init_models(self):
        """初始化所有模型"""
        try:
            self.models = {
                'xgb': XGBHundredsModel(self.db_path),
                'lgb': LGBHundredsModel(self.db_path),
                'lstm': LSTMHundredsModel(self.db_path),
                'ensemble': EnsembleHundredsModel(self.db_path)
            }
            self.logger.info("所有模型初始化完成")
        except Exception as e:
            self.logger.error(f"模型初始化失败: {e}")
            raise
    
    def train_model(self, model_type: str = 'ensemble', 
                   limit: Optional[int] = None) -> Dict[str, Any]:
        """
        训练指定模型
        
        Args:
            model_type: 模型类型 ('xgb', 'lgb', 'lstm', 'ensemble')
            limit: 限制训练样本数量
            
        Returns:
            训练结果字典
        """
        if model_type not in self.models:
            raise ValueError(f"不支持的模型类型: {model_type}")
        
        try:
            self.logger.info(f"开始训练 {model_type} 模型")
            
            # 加载训练数据
            model = self.models[model_type]
            X, y = model.load_training_data(limit)
            
            # 训练模型
            training_result = model.train(X, y)
            
            # 保存性能指标
            if self.monitoring_config.get('save_performance', True):
                self._save_training_performance(model_type, training_result)
            
            self.logger.info(f"{model_type} 模型训练完成")
            return training_result
            
        except Exception as e:
            self.logger.error(f"{model_type} 模型训练失败: {e}")
            raise
    
    def train_all_models(self, limit: Optional[int] = None) -> Dict[str, Any]:
        """
        训练所有模型
        
        Args:
            limit: 限制训练样本数量
            
        Returns:
            所有模型的训练结果
        """
        results = {}
        
        # 先训练基础模型
        for model_type in ['xgb', 'lgb', 'lstm']:
            try:
                result = self.train_model(model_type, limit)
                results[model_type] = result
            except Exception as e:
                self.logger.error(f"{model_type} 模型训练失败: {e}")
                results[model_type] = {'error': str(e)}
        
        # 最后训练集成模型
        try:
            result = self.train_model('ensemble', limit)
            results['ensemble'] = result
        except Exception as e:
            self.logger.error(f"集成模型训练失败: {e}")
            results['ensemble'] = {'error': str(e)}
        
        return results
    
    def predict(self, issue: str, model_type: Optional[str] = None) -> Dict[str, Any]:
        """
        预测指定期号的百位数字
        
        Args:
            issue: 期号
            model_type: 模型类型，None使用默认模型
            
        Returns:
            预测结果字典
        """
        if model_type is None:
            model_type = self.default_model
        
        if model_type not in self.models:
            raise ValueError(f"不支持的模型类型: {model_type}")
        
        try:
            model = self.models[model_type]
            
            if not model.is_trained:
                raise ValueError(f"{model_type} 模型尚未训练")
            
            # 执行预测
            prediction_result = model.predict_next_period(issue)
            
            # 保存预测结果
            if self.prediction_config.get('save_predictions', True):
                self.data_access.save_prediction_result(prediction_result)
            
            return prediction_result
            
        except Exception as e:
            self.logger.error(f"预测失败: {e}")
            raise
    
    def predict_batch(self, issues: List[str], 
                     model_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        批量预测
        
        Args:
            issues: 期号列表
            model_type: 模型类型
            
        Returns:
            预测结果列表
        """
        results = []
        
        for issue in issues:
            try:
                result = self.predict(issue, model_type)
                results.append(result)
            except Exception as e:
                self.logger.error(f"期号 {issue} 预测失败: {e}")
                results.append({
                    'issue': issue,
                    'error': str(e),
                    'predicted_digit': None,
                    'confidence': 0.0
                })
        
        return results
    
    def get_model_status(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有模型状态
        
        Returns:
            模型状态字典
        """
        status = {}
        
        for name, model in self.models.items():
            try:
                status[name] = model.get_model_info()
            except Exception as e:
                status[name] = {
                    'error': str(e),
                    'is_trained': False
                }
        
        return status

    def get_prediction_history(self, model_type: Optional[str] = None,
                             limit: int = 100) -> List[Dict[str, Any]]:
        """
        获取预测历史

        Args:
            model_type: 模型类型过滤
            limit: 限制返回数量

        Returns:
            预测历史列表
        """
        return self.data_access.get_prediction_history(model_type, limit)

    def get_accuracy_statistics(self, model_type: Optional[str] = None,
                               recent_periods: int = 100) -> Dict[str, float]:
        """
        获取准确率统计

        Args:
            model_type: 模型类型
            recent_periods: 最近期数

        Returns:
            统计结果字典
        """
        return self.data_access.get_accuracy_statistics(model_type, recent_periods)

    def evaluate_model(self, model_type: str,
                      test_periods: int = 100) -> Dict[str, Any]:
        """
        评估模型性能

        Args:
            model_type: 模型类型
            test_periods: 测试期数

        Returns:
            评估结果字典
        """
        if model_type not in self.models:
            raise ValueError(f"不支持的模型类型: {model_type}")

        try:
            model = self.models[model_type]

            if not model.is_trained:
                raise ValueError(f"{model_type} 模型尚未训练")

            # 获取测试数据
            X, y = model.load_training_data()

            # 使用最后test_periods期作为测试集
            if len(X) > test_periods:
                X_test = X[-test_periods:]
                y_test = y[-test_periods:]

                # 评估模型
                evaluation_result = model.evaluate_model(X_test, y_test)

                # 保存评估结果
                if self.monitoring_config.get('save_performance', True):
                    self._save_evaluation_performance(model_type, evaluation_result)

                return evaluation_result
            else:
                raise ValueError("测试数据不足")

        except Exception as e:
            self.logger.error(f"模型评估失败: {e}")
            raise

    def save_models(self, model_types: Optional[List[str]] = None) -> Dict[str, str]:
        """
        保存模型

        Args:
            model_types: 要保存的模型类型列表，None保存所有已训练模型

        Returns:
            保存路径字典
        """
        if model_types is None:
            model_types = [name for name, model in self.models.items() if model.is_trained]

        saved_paths = {}

        for model_type in model_types:
            if model_type in self.models and self.models[model_type].is_trained:
                try:
                    path = self.models[model_type].save_model()
                    saved_paths[model_type] = path
                    self.logger.info(f"{model_type} 模型保存成功: {path}")
                except Exception as e:
                    self.logger.error(f"{model_type} 模型保存失败: {e}")
                    saved_paths[model_type] = f"Error: {e}"

        return saved_paths

    def load_models(self, model_paths: Dict[str, str]) -> Dict[str, bool]:
        """
        加载模型

        Args:
            model_paths: 模型路径字典

        Returns:
            加载结果字典
        """
        load_results = {}

        for model_type, path in model_paths.items():
            if model_type in self.models:
                try:
                    success = self.models[model_type].load_model(path)
                    load_results[model_type] = success
                    if success:
                        self.logger.info(f"{model_type} 模型加载成功")
                    else:
                        self.logger.error(f"{model_type} 模型加载失败")
                except Exception as e:
                    self.logger.error(f"{model_type} 模型加载异常: {e}")
                    load_results[model_type] = False

        return load_results

    def _save_training_performance(self, model_type: str, training_result: Dict[str, Any]):
        """保存训练性能指标"""
        try:
            performance_data = {
                'model_type': model_type,
                'evaluation_period': 'training',
                'accuracy': training_result.get('val_accuracy', 0.0),
                'top3_accuracy': 0.0,  # 训练时通常不计算Top3
                'avg_confidence': 0.0,
                'training_time': training_result.get('training_time', 0.0),
                'prediction_time': 0.0,
                'model_size': 0
            }

            self.data_access.save_performance_metrics(performance_data)

        except Exception as e:
            self.logger.error(f"保存训练性能指标失败: {e}")

    def _save_evaluation_performance(self, model_type: str, evaluation_result: Dict[str, Any]):
        """保存评估性能指标"""
        try:
            performance_data = {
                'model_type': model_type,
                'evaluation_period': 'evaluation',
                'accuracy': evaluation_result.get('accuracy', 0.0),
                'top3_accuracy': evaluation_result.get('top3_accuracy', 0.0),
                'avg_confidence': evaluation_result.get('avg_confidence', 0.0),
                'precision_per_digit': evaluation_result.get('classification_report', {}),
                'recall_per_digit': evaluation_result.get('classification_report', {}),
                'f1_score_per_digit': evaluation_result.get('classification_report', {}),
                'confusion_matrix': evaluation_result.get('confusion_matrix', []),
                'feature_importance': evaluation_result.get('feature_importance', {}),
                'training_time': 0.0,
                'prediction_time': 0.0,
                'model_size': 0
            }

            self.data_access.save_performance_metrics(performance_data)

        except Exception as e:
            self.logger.error(f"保存评估性能指标失败: {e}")

# 便捷函数
def create_hundreds_predictor(db_path: str = "data/lottery.db") -> HundredsPredictor:
    """
    创建百位预测器实例

    Args:
        db_path: 数据库路径

    Returns:
        HundredsPredictor实例
    """
    return HundredsPredictor(db_path)

def quick_predict_hundreds(issue: str, model_type: str = 'ensemble',
                          db_path: str = "data/lottery.db") -> Dict[str, Any]:
    """
    快速预测百位数字

    Args:
        issue: 期号
        model_type: 模型类型
        db_path: 数据库路径

    Returns:
        预测结果
    """
    predictor = HundredsPredictor(db_path)
    return predictor.predict(issue, model_type)
