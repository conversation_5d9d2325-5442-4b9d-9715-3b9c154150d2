#!/usr/bin/env python3
"""
P3-百位预测器配置加载工具

提供统一的配置加载和管理功能
"""

import yaml
import os
import logging
import logging.config
from pathlib import Path
from typing import Dict, Any, Optional

class ConfigLoader:
    """配置加载器"""
    
    def __init__(self, config_dir: Optional[str] = None):
        """
        初始化配置加载器
        
        Args:
            config_dir: 配置文件目录，默认为当前目录下的config
        """
        if config_dir is None:
            self.config_dir = Path(__file__).parent
        else:
            self.config_dir = Path(config_dir)
        
        self._predictor_config = None
        self._logging_config = None
    
    def load_predictor_config(self) -> Dict[str, Any]:
        """
        加载预测器配置
        
        Returns:
            Dict: 预测器配置字典
        """
        if self._predictor_config is None:
            config_file = self.config_dir / "hundreds_predictor_config.yaml"
            
            if not config_file.exists():
                raise FileNotFoundError(f"预测器配置文件不存在: {config_file}")
            
            with open(config_file, 'r', encoding='utf-8') as f:
                self._predictor_config = yaml.safe_load(f)
        
        return self._predictor_config
    
    def load_logging_config(self) -> Dict[str, Any]:
        """
        加载日志配置
        
        Returns:
            Dict: 日志配置字典
        """
        if self._logging_config is None:
            config_file = self.config_dir / "logging_config.yaml"
            
            if not config_file.exists():
                raise FileNotFoundError(f"日志配置文件不存在: {config_file}")
            
            with open(config_file, 'r', encoding='utf-8') as f:
                self._logging_config = yaml.safe_load(f)
        
        return self._logging_config
    
    def setup_logging(self) -> None:
        """设置日志配置"""
        logging_config = self.load_logging_config()
        
        # 确保日志目录存在
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # 应用日志配置
        logging.config.dictConfig(logging_config)
    
    def get_model_config(self, model_type: str) -> Dict[str, Any]:
        """
        获取特定模型的配置
        
        Args:
            model_type: 模型类型 (xgboost, lightgbm, lstm, ensemble)
            
        Returns:
            Dict: 模型配置
        """
        config = self.load_predictor_config()
        
        if model_type not in config:
            raise ValueError(f"未找到模型配置: {model_type}")
        
        return config[model_type]
    
    def get_data_config(self) -> Dict[str, Any]:
        """获取数据配置"""
        config = self.load_predictor_config()
        return config.get('data', {})
    
    def get_training_config(self) -> Dict[str, Any]:
        """获取训练配置"""
        config = self.load_predictor_config()
        return config.get('training', {})
    
    def get_prediction_config(self) -> Dict[str, Any]:
        """获取预测配置"""
        config = self.load_predictor_config()
        return config.get('prediction', {})
    
    def get_monitoring_config(self) -> Dict[str, Any]:
        """获取监控配置"""
        config = self.load_predictor_config()
        return config.get('monitoring', {})
    
    def get_cache_config(self) -> Dict[str, Any]:
        """获取缓存配置"""
        config = self.load_predictor_config()
        return config.get('cache', {})

# 全局配置加载器实例
config_loader = ConfigLoader()

def get_config() -> ConfigLoader:
    """获取全局配置加载器实例"""
    return config_loader

def setup_logging():
    """设置日志配置（便捷函数）"""
    config_loader.setup_logging()

# 使用示例
if __name__ == "__main__":
    # 设置日志
    setup_logging()
    
    # 获取配置
    loader = get_config()
    
    # 加载各种配置
    predictor_config = loader.load_predictor_config()
    xgb_config = loader.get_model_config('xgboost')
    data_config = loader.get_data_config()
    
    print("✅ 配置加载测试完成")
    print(f"预测器名称: {predictor_config['predictor']['name']}")
    print(f"XGBoost参数数量: {len(xgb_config)}")
    print(f"数据库路径: {data_config['db_path']}")
