"""
数据源配置模块
基于3dyuce项目成功经验，配置可靠的数据源
"""

import requests
import time
import random
from typing import List, Dict, Optional
from urllib.parse import urlparse


class DataSourceConfig:
    """数据源配置类"""
    
    # 主数据源 - 基于3dyuce项目验证过的可靠数据源
    PRIMARY_SOURCES = [
        "https://data.17500.cn/3d_asc.txt",  # 3dyuce项目验证的主要数据源
        "https://www.17500.cn/chart/3d-tjb.html"  # 备用HTML数据源
    ]
    
    # 备用数据源
    BACKUP_SOURCES = [
        "https://data.17500.cn/3d_desc.txt",  # 降序数据源
        "https://www.cjcp.com.cn/kaijiang/3dmingxi_0.html"  # 第三方数据源
    ]
    
    # 请求配置
    REQUEST_CONFIG = {
        'timeout': 30,
        'max_retries': 3,
        'base_delay': 2,
        'max_delay': 10,
        'user_agents': [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        ]
    }


class DataSourceValidator:
    """数据源验证器"""
    
    def __init__(self):
        self.session = requests.Session()
        self.config = DataSourceConfig()
    
    def get_random_headers(self) -> Dict[str, str]:
        """获取随机请求头"""
        return {
            'User-Agent': random.choice(self.config.REQUEST_CONFIG['user_agents']),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
    
    def validate_url(self, url: str) -> Dict[str, any]:
        """验证单个URL的可用性"""
        result = {
            'url': url,
            'status': 'unknown',
            'response_time': 0,
            'status_code': None,
            'content_length': 0,
            'error': None
        }
        
        try:
            start_time = time.time()
            headers = self.get_random_headers()
            
            response = self.session.get(
                url, 
                headers=headers, 
                timeout=self.config.REQUEST_CONFIG['timeout']
            )
            
            result['response_time'] = time.time() - start_time
            result['status_code'] = response.status_code
            result['content_length'] = len(response.content)
            
            if response.status_code == 200:
                # 检查内容是否包含福彩3D数据特征
                content = response.text
                if self._is_valid_lottery_content(content):
                    result['status'] = 'valid'
                else:
                    result['status'] = 'invalid_content'
                    result['error'] = '内容不包含有效的福彩3D数据'
            else:
                result['status'] = 'http_error'
                result['error'] = f'HTTP错误: {response.status_code}'
                
        except requests.exceptions.Timeout:
            result['status'] = 'timeout'
            result['error'] = '请求超时'
        except requests.exceptions.ConnectionError:
            result['status'] = 'connection_error'
            result['error'] = '连接错误'
        except Exception as e:
            result['status'] = 'error'
            result['error'] = str(e)
        
        return result
    
    def _is_valid_lottery_content(self, content: str) -> bool:
        """检查内容是否包含有效的福彩3D数据"""
        # 检查是否包含期号和数字模式
        indicators = [
            '2024',  # 年份
            '2025',  # 年份
            '期',    # 期号标识
            '开奖',  # 开奖标识
        ]
        
        # 至少包含一个年份和一个标识符
        year_found = any(year in content for year in ['2024', '2025'])
        indicator_found = any(indicator in content for indicator in ['期', '开奖', 'lottery', '3d'])
        
        return year_found and indicator_found and len(content) > 1000
    
    def validate_all_sources(self) -> Dict[str, List[Dict]]:
        """验证所有数据源"""
        results = {
            'primary': [],
            'backup': [],
            'summary': {
                'total_sources': 0,
                'valid_sources': 0,
                'invalid_sources': 0,
                'best_source': None
            }
        }
        
        print("开始验证数据源...")
        
        # 验证主数据源
        for url in self.config.PRIMARY_SOURCES:
            print(f"验证主数据源: {url}")
            result = self.validate_url(url)
            results['primary'].append(result)
            time.sleep(random.uniform(1, 3))  # 随机延迟
        
        # 验证备用数据源
        for url in self.config.BACKUP_SOURCES:
            print(f"验证备用数据源: {url}")
            result = self.validate_url(url)
            results['backup'].append(result)
            time.sleep(random.uniform(1, 3))  # 随机延迟
        
        # 统计结果
        all_results = results['primary'] + results['backup']
        results['summary']['total_sources'] = len(all_results)
        results['summary']['valid_sources'] = len([r for r in all_results if r['status'] == 'valid'])
        results['summary']['invalid_sources'] = len([r for r in all_results if r['status'] != 'valid'])
        
        # 找到最佳数据源（响应时间最快的有效源）
        valid_sources = [r for r in all_results if r['status'] == 'valid']
        if valid_sources:
            best_source = min(valid_sources, key=lambda x: x['response_time'])
            results['summary']['best_source'] = best_source['url']
        
        return results


class DataSourceManager:
    """数据源管理器 - 支持故障转移"""
    
    def __init__(self):
        self.config = DataSourceConfig()
        self.validator = DataSourceValidator()
        self.current_source = None
        self.source_status = {}
    
    def get_best_source(self) -> Optional[str]:
        """获取最佳可用数据源"""
        if not self.source_status:
            # 首次运行，验证所有数据源
            validation_results = self.validator.validate_all_sources()
            self._update_source_status(validation_results)
        
        # 返回最佳数据源
        valid_sources = [url for url, status in self.source_status.items() if status['valid']]
        if valid_sources:
            # 按响应时间排序，返回最快的
            return min(valid_sources, key=lambda url: self.source_status[url]['response_time'])
        
        return None
    
    def _update_source_status(self, validation_results: Dict):
        """更新数据源状态"""
        all_results = validation_results['primary'] + validation_results['backup']
        for result in all_results:
            self.source_status[result['url']] = {
                'valid': result['status'] == 'valid',
                'response_time': result['response_time'],
                'last_check': time.time(),
                'error': result.get('error')
            }
    
    def switch_source(self) -> Optional[str]:
        """切换到下一个可用数据源"""
        available_sources = [url for url, status in self.source_status.items() 
                           if status['valid'] and url != self.current_source]
        
        if available_sources:
            self.current_source = available_sources[0]
            return self.current_source
        
        return None


# 全局数据源管理器实例
data_source_manager = DataSourceManager()


def get_active_data_source() -> Optional[str]:
    """获取当前活跃的数据源"""
    return data_source_manager.get_best_source()


def validate_data_sources() -> Dict:
    """验证所有数据源并返回结果"""
    validator = DataSourceValidator()
    return validator.validate_all_sources()


if __name__ == "__main__":
    # 测试数据源验证
    print("=== 福彩3D数据源验证测试 ===")
    results = validate_data_sources()
    
    print(f"\n验证结果摘要:")
    print(f"总数据源: {results['summary']['total_sources']}")
    print(f"有效数据源: {results['summary']['valid_sources']}")
    print(f"无效数据源: {results['summary']['invalid_sources']}")
    print(f"最佳数据源: {results['summary']['best_source']}")
    
    print(f"\n详细结果:")
    for category, sources in [('主数据源', results['primary']), ('备用数据源', results['backup'])]:
        print(f"\n{category}:")
        for source in sources:
            status_icon = "✅" if source['status'] == 'valid' else "❌"
            print(f"  {status_icon} {source['url']}")
            print(f"     状态: {source['status']}")
            print(f"     响应时间: {source['response_time']:.2f}秒")
            if source['error']:
                print(f"     错误: {source['error']}")
