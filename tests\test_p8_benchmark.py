#!/usr/bin/env python3
"""
P8智能交集融合系统性能基准测试

验证系统的执行效率和资源使用情况

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import unittest
import tempfile
import shutil
import sqlite3
import time
import psutil
import threading
from pathlib import Path
from datetime import datetime
import sys
import os
import json
import statistics

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.fusion.fusion_predictor import FusionPredictor
from src.fusion.performance_monitor import PerformanceMonitor
from src.data.fusion_data_access import FusionDataAccess

class BenchmarkResult:
    """基准测试结果"""
    
    def __init__(self, test_name: str):
        self.test_name = test_name
        self.start_time = None
        self.end_time = None
        self.execution_time = 0.0
        self.memory_usage = []
        self.cpu_usage = []
        self.success = False
        self.error_message = None
        self.metrics = {}
    
    def start(self):
        """开始测试"""
        self.start_time = time.time()
        self.memory_usage = []
        self.cpu_usage = []
    
    def end(self, success: bool = True, error_message: str = None):
        """结束测试"""
        self.end_time = time.time()
        self.execution_time = self.end_time - self.start_time
        self.success = success
        self.error_message = error_message
    
    def record_resource_usage(self):
        """记录资源使用情况"""
        try:
            process = psutil.Process()
            self.memory_usage.append(process.memory_info().rss / 1024 / 1024)  # MB
            self.cpu_usage.append(process.cpu_percent())
        except Exception:
            pass
    
    def get_summary(self) -> dict:
        """获取测试摘要"""
        return {
            'test_name': self.test_name,
            'execution_time': self.execution_time,
            'success': self.success,
            'error_message': self.error_message,
            'avg_memory_mb': statistics.mean(self.memory_usage) if self.memory_usage else 0,
            'max_memory_mb': max(self.memory_usage) if self.memory_usage else 0,
            'avg_cpu_percent': statistics.mean(self.cpu_usage) if self.cpu_usage else 0,
            'max_cpu_percent': max(self.cpu_usage) if self.cpu_usage else 0,
            'metrics': self.metrics
        }

class TestP8Benchmark(unittest.TestCase):
    """P8智能交集融合系统性能基准测试"""
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化"""
        # 创建临时目录
        cls.temp_dir = tempfile.mkdtemp()
        cls.test_db_path = os.path.join(cls.temp_dir, "benchmark_lottery.db")
        cls.config_path = os.path.join(cls.temp_dir, "benchmark_config.yaml")
        
        # 创建测试数据库
        cls._create_benchmark_database()
        
        # 创建测试配置
        cls._create_benchmark_config()
        
        # 基准测试结果
        cls.benchmark_results = []
        
        print(f"基准测试环境初始化完成: {cls.temp_dir}")
    
    @classmethod
    def tearDownClass(cls):
        """测试类清理"""
        # 输出基准测试报告
        cls._generate_benchmark_report()
        
        # 清理临时目录
        shutil.rmtree(cls.temp_dir, ignore_errors=True)
        print("基准测试环境清理完成")
    
    @classmethod
    def _create_benchmark_database(cls):
        """创建基准测试数据库"""
        with sqlite3.connect(cls.test_db_path) as conn:
            cursor = conn.cursor()
            
            # 创建所有必需的表
            tables_sql = [
                '''CREATE TABLE IF NOT EXISTS lottery_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    issue TEXT NOT NULL,
                    hundreds INTEGER NOT NULL,
                    tens INTEGER NOT NULL,
                    units INTEGER NOT NULL,
                    sum_value INTEGER NOT NULL,
                    span_value INTEGER NOT NULL,
                    draw_date TEXT NOT NULL
                )''',
                
                '''CREATE TABLE IF NOT EXISTS final_predictions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    issue TEXT NOT NULL,
                    prediction_rank INTEGER NOT NULL,
                    hundreds INTEGER NOT NULL,
                    tens INTEGER NOT NULL,
                    units INTEGER NOT NULL,
                    sum_value INTEGER NOT NULL,
                    span_value INTEGER NOT NULL,
                    combined_probability REAL NOT NULL,
                    hundreds_prob REAL NOT NULL,
                    tens_prob REAL NOT NULL,
                    units_prob REAL NOT NULL,
                    sum_prob REAL NOT NULL,
                    span_prob REAL NOT NULL,
                    sum_consistency REAL NOT NULL,
                    span_consistency REAL NOT NULL,
                    constraint_score REAL NOT NULL,
                    diversity_score REAL NOT NULL,
                    confidence_level TEXT,
                    fusion_method TEXT,
                    ranking_strategy TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(issue, prediction_rank)
                )''',
                
                '''CREATE TABLE IF NOT EXISTS fusion_weights (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    weight_type TEXT NOT NULL,
                    predictor_name TEXT NOT NULL,
                    model_name TEXT,
                    weight_value REAL NOT NULL,
                    performance_score REAL,
                    accuracy_rate REAL,
                    confidence_score REAL,
                    is_active BOOLEAN DEFAULT TRUE,
                    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(weight_type, predictor_name, model_name)
                )''',
                
                '''CREATE TABLE IF NOT EXISTS prediction_performance (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    issue TEXT NOT NULL,
                    actual_hundreds INTEGER,
                    actual_tens INTEGER,
                    actual_units INTEGER,
                    actual_sum INTEGER,
                    actual_span INTEGER,
                    predicted_rank INTEGER,
                    hit_type TEXT,
                    hundreds_accuracy BOOLEAN,
                    tens_accuracy BOOLEAN,
                    units_accuracy BOOLEAN,
                    sum_accuracy BOOLEAN,
                    span_accuracy BOOLEAN,
                    overall_score REAL,
                    probability_score REAL,
                    constraint_score REAL,
                    diversity_effectiveness REAL,
                    fusion_method TEXT,
                    ranking_strategy TEXT,
                    top_k_hit INTEGER,
                    confidence_accuracy REAL,
                    evaluated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(issue)
                )''',
                
                '''CREATE TABLE IF NOT EXISTS fusion_sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id TEXT NOT NULL,
                    issue TEXT NOT NULL,
                    fusion_method TEXT NOT NULL,
                    ranking_strategy TEXT NOT NULL,
                    input_data TEXT NOT NULL,
                    output_data TEXT NOT NULL,
                    execution_time REAL,
                    success BOOLEAN DEFAULT TRUE,
                    error_message TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(session_id)
                )''',
                
                '''CREATE TABLE IF NOT EXISTS weight_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    weights TEXT NOT NULL,
                    performance_summary TEXT,
                    adjustment_reason TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )'''
            ]
            
            for sql in tables_sql:
                cursor.execute(sql)
            
            # 插入大量测试数据用于性能测试
            test_data = []
            for i in range(1000):  # 1000条历史数据
                issue = f"2024{i+1:03d}"
                h, t, u = i % 10, (i+1) % 10, (i+2) % 10
                sum_val = h + t + u
                span_val = max(h, t, u) - min(h, t, u)
                date = f"2024-{(i//30)+1:02d}-{(i%30)+1:02d}"
                test_data.append((issue, h, t, u, sum_val, span_val, date))
            
            cursor.executemany('''
                INSERT INTO lottery_data (issue, hundreds, tens, units, sum_value, span_value, draw_date)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', test_data)
            
            # 插入权重数据
            default_weights = [
                ('position', 'hundreds', 'ensemble', 1.0, 0.7, 0.6, 0.8),
                ('position', 'tens', 'ensemble', 1.0, 0.7, 0.6, 0.8),
                ('position', 'units', 'ensemble', 1.0, 0.7, 0.6, 0.8),
                ('auxiliary', 'sum', 'ensemble', 0.8, 0.6, 0.5, 0.7),
                ('auxiliary', 'span', 'ensemble', 0.6, 0.5, 0.4, 0.6)
            ]
            
            cursor.executemany('''
                INSERT INTO fusion_weights (weight_type, predictor_name, model_name, weight_value, performance_score, accuracy_rate, confidence_score)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', default_weights)
            
            conn.commit()
    
    @classmethod
    def _create_benchmark_config(cls):
        """创建基准测试配置"""
        config_content = f'''
fusion:
  default_method: "adaptive_fusion"
  probability_weight: 0.5
  constraint_weight: 0.3
  diversity_weight: 0.2
  min_probability: 1.0e-6
  max_recommendations: 20

ranking:
  default_strategy: "adaptive"
  top_k: 20
  min_score_threshold: 0.01
  diversity_penalty: 0.1

weights:
  learning_rate: 0.1
  decay_factor: 0.95
  min_weight: 0.1
  max_weight: 2.0
  evaluation_window: 30
  min_samples: 10

constraints:
  sum_tolerance: 2.0
  span_tolerance: 1.0
  consistency_weight: 0.3
  diversity_weight: 0.2

database:
  path: "{cls.test_db_path}"
  timeout: 30

logging:
  level: "WARNING"
  file: "logs/benchmark_fusion.log"
'''
        
        with open(cls.config_path, 'w', encoding='utf-8') as f:
            f.write(config_content)
    
    def _run_benchmark(self, test_name: str, test_func, *args, **kwargs):
        """运行基准测试"""
        result = BenchmarkResult(test_name)
        result.start()
        
        # 启动资源监控线程
        monitoring = True
        def monitor_resources():
            while monitoring:
                result.record_resource_usage()
                time.sleep(0.1)
        
        monitor_thread = threading.Thread(target=monitor_resources, daemon=True)
        monitor_thread.start()
        
        try:
            # 执行测试函数
            metrics = test_func(*args, **kwargs)
            result.metrics = metrics or {}
            result.end(success=True)
            
        except Exception as e:
            result.end(success=False, error_message=str(e))
        
        finally:
            monitoring = False
            monitor_thread.join(timeout=1)
        
        self.benchmark_results.append(result)
        return result
    
    def test_01_fusion_predictor_initialization_benchmark(self):
        """基准测试：融合预测器初始化性能"""
        print("\n=== 基准测试：融合预测器初始化性能 ===")
        
        def init_test():
            predictor = FusionPredictor(
                db_path=self.test_db_path,
                config_path=self.config_path
            )
            return {'initialized': predictor.is_initialized}
        
        result = self._run_benchmark("融合预测器初始化", init_test)
        
        print(f"初始化时间: {result.execution_time:.3f}秒")
        print(f"内存使用: {result.get_summary()['max_memory_mb']:.1f}MB")
        
        # 性能要求：初始化时间应小于5秒
        self.assertLess(result.execution_time, 5.0, "初始化时间过长")
        self.assertTrue(result.success, "初始化失败")
    
    def test_02_data_access_performance_benchmark(self):
        """基准测试：数据访问性能"""
        print("\n=== 基准测试：数据访问性能 ===")
        
        def data_access_test():
            data_access = FusionDataAccess(self.test_db_path)
            
            # 测试大量数据读取
            start_time = time.time()
            weights = data_access.get_fusion_weights()
            read_time = time.time() - start_time
            
            # 测试批量写入
            test_predictions = []
            for i in range(100):  # 100个预测结果
                pred = {
                    'rank': i + 1,
                    'hundreds': i % 10,
                    'tens': (i + 1) % 10,
                    'units': (i + 2) % 10,
                    'sum_value': ((i % 10) + ((i + 1) % 10) + ((i + 2) % 10)),
                    'span_value': 2,
                    'combined_probability': 0.01,
                    'hundreds_prob': 0.1,
                    'tens_prob': 0.1,
                    'units_prob': 0.1,
                    'sum_prob': 0.1,
                    'span_prob': 0.1,
                    'sum_consistency': 0.8,
                    'span_consistency': 0.8,
                    'constraint_score': 0.8,
                    'diversity_score': 0.7,
                    'confidence_level': 'medium',
                    'fusion_method': 'benchmark_method',
                    'ranking_strategy': 'benchmark_strategy'
                }
                test_predictions.append(pred)
            
            start_time = time.time()
            success = data_access.save_final_predictions(test_predictions, 'benchmark_issue')
            write_time = time.time() - start_time
            
            return {
                'read_time': read_time,
                'write_time': write_time,
                'weights_count': len(weights),
                'predictions_saved': len(test_predictions),
                'write_success': success
            }
        
        result = self._run_benchmark("数据访问性能", data_access_test)
        
        print(f"数据读取时间: {result.metrics['read_time']:.3f}秒")
        print(f"数据写入时间: {result.metrics['write_time']:.3f}秒")
        print(f"写入{result.metrics['predictions_saved']}条预测结果")
        
        # 性能要求：读取时间应小于0.1秒，写入时间应小于1秒
        self.assertLess(result.metrics['read_time'], 0.1, "数据读取时间过长")
        self.assertLess(result.metrics['write_time'], 1.0, "数据写入时间过长")
        self.assertTrue(result.metrics['write_success'], "数据写入失败")
    
    def test_03_performance_monitoring_benchmark(self):
        """基准测试：性能监控开销"""
        print("\n=== 基准测试：性能监控开销 ===")
        
        def monitoring_test():
            monitor_config = {'alert_rules': [], 'monitoring_interval': 0.1}
            monitor = PerformanceMonitor(self.test_db_path, monitor_config)
            
            # 启动监控
            monitor.start_monitoring(interval=0.1)
            
            # 模拟工作负载
            start_time = time.time()
            for i in range(100):
                monitor.record_prediction(0.1 + i * 0.01, i % 2 == 0)
                time.sleep(0.01)  # 模拟预测时间
            
            work_time = time.time() - start_time
            
            # 获取监控数据
            summary = monitor.get_monitoring_summary()
            
            # 停止监控
            monitor.stop_monitoring()
            
            return {
                'work_time': work_time,
                'predictions_recorded': summary['stats']['total_predictions'],
                'monitoring_overhead': work_time - 1.0  # 预期工作时间约1秒
            }
        
        result = self._run_benchmark("性能监控开销", monitoring_test)
        
        print(f"工作时间: {result.metrics['work_time']:.3f}秒")
        print(f"记录预测数: {result.metrics['predictions_recorded']}")
        print(f"监控开销: {result.metrics['monitoring_overhead']:.3f}秒")
        
        # 性能要求：监控开销应小于20%
        overhead_ratio = result.metrics['monitoring_overhead'] / result.metrics['work_time']
        self.assertLess(overhead_ratio, 0.2, "性能监控开销过大")
    
    def test_04_concurrent_access_benchmark(self):
        """基准测试：并发访问性能"""
        print("\n=== 基准测试：并发访问性能 ===")
        
        def concurrent_test():
            data_access = FusionDataAccess(self.test_db_path)
            
            # 并发读取测试
            def read_worker():
                for i in range(10):
                    weights = data_access.get_fusion_weights()
                    summary = data_access.get_performance_summary(days=7)
            
            # 启动多个读取线程
            threads = []
            start_time = time.time()
            
            for i in range(5):  # 5个并发线程
                thread = threading.Thread(target=read_worker)
                threads.append(thread)
                thread.start()
            
            # 等待所有线程完成
            for thread in threads:
                thread.join()
            
            concurrent_time = time.time() - start_time
            
            return {
                'concurrent_time': concurrent_time,
                'thread_count': len(threads),
                'operations_per_thread': 10
            }
        
        result = self._run_benchmark("并发访问性能", concurrent_test)
        
        print(f"并发执行时间: {result.metrics['concurrent_time']:.3f}秒")
        print(f"线程数: {result.metrics['thread_count']}")
        print(f"每线程操作数: {result.metrics['operations_per_thread']}")
        
        # 性能要求：并发访问时间应小于5秒
        self.assertLess(result.metrics['concurrent_time'], 5.0, "并发访问时间过长")
    
    def test_05_memory_usage_benchmark(self):
        """基准测试：内存使用情况"""
        print("\n=== 基准测试：内存使用情况 ===")
        
        def memory_test():
            # 创建多个融合预测器实例
            predictors = []
            initial_memory = psutil.Process().memory_info().rss / 1024 / 1024
            
            for i in range(3):  # 创建3个实例
                predictor = FusionPredictor(
                    db_path=self.test_db_path,
                    config_path=self.config_path
                )
                predictors.append(predictor)
            
            peak_memory = psutil.Process().memory_info().rss / 1024 / 1024
            
            # 清理实例
            del predictors
            
            final_memory = psutil.Process().memory_info().rss / 1024 / 1024
            
            return {
                'initial_memory_mb': initial_memory,
                'peak_memory_mb': peak_memory,
                'final_memory_mb': final_memory,
                'memory_per_instance_mb': (peak_memory - initial_memory) / 3,
                'memory_leak_mb': final_memory - initial_memory
            }
        
        result = self._run_benchmark("内存使用情况", memory_test)
        
        print(f"初始内存: {result.metrics['initial_memory_mb']:.1f}MB")
        print(f"峰值内存: {result.metrics['peak_memory_mb']:.1f}MB")
        print(f"最终内存: {result.metrics['final_memory_mb']:.1f}MB")
        print(f"每实例内存: {result.metrics['memory_per_instance_mb']:.1f}MB")
        print(f"内存泄漏: {result.metrics['memory_leak_mb']:.1f}MB")
        
        # 性能要求：每个实例内存使用应小于100MB，内存泄漏应小于10MB
        self.assertLess(result.metrics['memory_per_instance_mb'], 100.0, "单实例内存使用过大")
        self.assertLess(result.metrics['memory_leak_mb'], 10.0, "存在内存泄漏")
    
    @classmethod
    def _generate_benchmark_report(cls):
        """生成基准测试报告"""
        print(f"\n{'='*60}")
        print("P8智能交集融合系统性能基准测试报告")
        print(f"{'='*60}")
        print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"测试环境: Python {sys.version}")
        
        try:
            print(f"系统信息: {psutil.cpu_count()}核CPU, {psutil.virtual_memory().total/1024/1024/1024:.1f}GB内存")
        except Exception:
            print("系统信息: 无法获取")
        
        print(f"\n{'='*60}")
        print("测试结果汇总:")
        print(f"{'='*60}")
        
        total_tests = len(cls.benchmark_results)
        successful_tests = sum(1 for r in cls.benchmark_results if r.success)
        
        print(f"总测试数: {total_tests}")
        print(f"成功测试: {successful_tests}")
        print(f"失败测试: {total_tests - successful_tests}")
        print(f"成功率: {successful_tests/total_tests:.1%}")
        
        print(f"\n{'='*60}")
        print("详细测试结果:")
        print(f"{'='*60}")
        
        for result in cls.benchmark_results:
            summary = result.get_summary()
            status = "✓" if summary['success'] else "✗"
            
            print(f"\n{status} {summary['test_name']}")
            print(f"   执行时间: {summary['execution_time']:.3f}秒")
            print(f"   平均内存: {summary['avg_memory_mb']:.1f}MB")
            print(f"   峰值内存: {summary['max_memory_mb']:.1f}MB")
            print(f"   平均CPU: {summary['avg_cpu_percent']:.1f}%")
            
            if summary['metrics']:
                print(f"   关键指标:")
                for key, value in summary['metrics'].items():
                    if isinstance(value, float):
                        print(f"     {key}: {value:.3f}")
                    else:
                        print(f"     {key}: {value}")
            
            if not summary['success']:
                print(f"   错误信息: {summary['error_message']}")
        
        # 保存报告到文件
        report_file = os.path.join(cls.temp_dir, "benchmark_report.json")
        with open(report_file, 'w', encoding='utf-8') as f:
            report_data = {
                'test_time': datetime.now().isoformat(),
                'total_tests': total_tests,
                'successful_tests': successful_tests,
                'success_rate': successful_tests/total_tests,
                'results': [r.get_summary() for r in cls.benchmark_results]
            }
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n基准测试报告已保存到: {report_file}")

def run_benchmark_tests():
    """运行基准测试"""
    print("开始P8智能交集融合系统性能基准测试...")
    
    # 创建测试套件
    test_suite = unittest.TestLoader().loadTestsFromTestCase(TestP8Benchmark)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    return result.wasSuccessful()

if __name__ == '__main__':
    success = run_benchmark_tests()
    exit(0 if success else 1)
