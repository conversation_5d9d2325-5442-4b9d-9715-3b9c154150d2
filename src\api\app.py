"""
P2高级特征工程系统 - Flask应用集成

集成高级特征API到Flask应用中，提供完整的API服务。

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

from flask import Flask, jsonify
from flask_cors import CORS
import os
import sys
import logging

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
sys.path.insert(0, project_root)

try:
    from src.api.v2.advanced_features import advanced_features_bp, init_advanced_features_api
except ImportError as e:
    print(f"导入API模块失败: {e}")
    advanced_features_bp = None

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def create_app(config=None):
    """
    创建Flask应用
    
    Args:
        config: 配置字典
        
    Returns:
        Flask: Flask应用实例
    """
    app = Flask(__name__)
    
    # 默认配置
    app.config.update({
        'SECRET_KEY': 'your-secret-key-here',
        'JSON_AS_ASCII': False,  # 支持中文JSON
        'JSONIFY_PRETTYPRINT_REGULAR': True,  # 美化JSON输出
        'DATABASE_PATH': 'data/lottery.db'
    })
    
    # 应用自定义配置
    if config:
        app.config.update(config)
    
    # 启用CORS
    CORS(app, resources={
        r"/api/*": {
            "origins": "*",
            "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
            "allow_headers": ["Content-Type", "Authorization"]
        }
    })
    
    # 注册蓝图
    if advanced_features_bp:
        app.register_blueprint(advanced_features_bp)
        logger.info("高级特征API蓝图注册成功")
    else:
        logger.warning("高级特征API蓝图注册失败")
    
    # 根路径
    @app.route('/')
    def index():
        return jsonify({
            'name': 'P2高级特征工程系统API',
            'version': 'v2.0',
            'status': 'running',
            'endpoints': {
                'api_info': '/api/v2/features/info',
                'health_check': '/api/v2/features/health',
                'advanced_features': '/api/v2/features/advanced/<feature_type>/<issue>',
                'batch_features': '/api/v2/features/batch',
                'feature_importance': '/api/v2/features/importance/<feature_type>',
                'cache_stats': '/api/v2/features/cache/stats',
                'available_types': '/api/v2/features/available_types'
            }
        })
    
    # 健康检查
    @app.route('/health')
    def health():
        return jsonify({
            'status': 'healthy',
            'service': 'P2 Advanced Features API',
            'version': 'v2.0'
        })
    
    # API文档
    @app.route('/docs')
    def docs():
        return jsonify({
            'api_documentation': {
                'base_url': '/api/v2/features',
                'endpoints': [
                    {
                        'path': '/advanced/<feature_type>/<issue>',
                        'method': 'GET',
                        'description': '获取指定期号的高级特征',
                        'parameters': {
                            'feature_type': 'hundreds|tens|units|sum|span|common|all',
                            'issue': '期号，格式如2025001'
                        },
                        'example': '/api/v2/features/advanced/hundreds/2025001'
                    },
                    {
                        'path': '/batch',
                        'method': 'POST',
                        'description': '批量获取多个期号的特征',
                        'body': {
                            'issues': ['2025001', '2025002'],
                            'feature_type': 'hundreds',
                            'include_cache_stats': True
                        }
                    },
                    {
                        'path': '/importance/<feature_type>',
                        'method': 'POST',
                        'description': '分析特征重要性',
                        'body': {
                            'issues': ['2025001', '2025002', '...'],
                            'target_variable': 'hundreds',
                            'analysis_config': {
                                'top_k_features': 20,
                                'model_type': 'auto'
                            }
                        }
                    },
                    {
                        'path': '/cache/stats',
                        'method': 'GET',
                        'description': '获取缓存统计信息'
                    },
                    {
                        'path': '/cache/clear',
                        'method': 'POST',
                        'description': '清理缓存',
                        'body': {
                            'cache_type': 'all|memory|db'
                        }
                    },
                    {
                        'path': '/available_types',
                        'method': 'GET',
                        'description': '获取可用的特征类型'
                    },
                    {
                        'path': '/health',
                        'method': 'GET',
                        'description': '健康检查'
                    },
                    {
                        'path': '/export',
                        'method': 'POST',
                        'description': '导出特征数据',
                        'body': {
                            'issues': ['2025001', '2025002'],
                            'feature_types': ['hundreds', 'tens'],
                            'format': 'json|csv',
                            'include_metadata': True
                        }
                    }
                ]
            }
        })
    
    # 错误处理
    @app.errorhandler(404)
    def not_found(error):
        return jsonify({
            'status': 'error',
            'message': '请求的资源不存在',
            'error_code': 404
        }), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        logger.error(f"服务器内部错误: {error}")
        return jsonify({
            'status': 'error',
            'message': '服务器内部错误',
            'error_code': 500
        }), 500
    
    # 初始化API
    try:
        db_path = app.config.get('DATABASE_PATH', 'data/lottery.db')
        init_advanced_features_api(db_path)
        logger.info(f"API初始化完成，数据库路径: {db_path}")
    except Exception as e:
        logger.error(f"API初始化失败: {e}")
    
    return app


def main():
    """主函数"""
    # 创建应用
    app = create_app()
    
    # 运行应用
    host = os.getenv('HOST', '127.0.0.1')
    port = int(os.getenv('PORT', 5000))
    debug = os.getenv('DEBUG', 'False').lower() == 'true'
    
    logger.info(f"启动P2高级特征工程API服务")
    logger.info(f"服务地址: http://{host}:{port}")
    logger.info(f"API文档: http://{host}:{port}/docs")
    logger.info(f"健康检查: http://{host}:{port}/health")
    
    app.run(host=host, port=port, debug=debug)


if __name__ == '__main__':
    main()
