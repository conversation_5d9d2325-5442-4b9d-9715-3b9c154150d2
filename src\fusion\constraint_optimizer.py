#!/usr/bin/env python3
"""
约束优化器

应用和值跨度约束和一致性检验
为P8智能交集融合系统提供约束优化功能

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Set
import logging
from datetime import datetime
from scipy.optimize import minimize, differential_evolution
import itertools

class ConstraintOptimizer:
    """约束优化器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化约束优化器
        
        Args:
            config: 配置参数
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 约束参数
        self.constraint_params = {
            'sum_tolerance': config.get('sum_tolerance', 2.0),      # 和值容差
            'span_tolerance': config.get('span_tolerance', 1.0),    # 跨度容差
            'min_probability': config.get('min_probability', 1e-6), # 最小概率阈值
            'max_recommendations': config.get('max_recommendations', 20), # 最大推荐数量
            'consistency_weight': config.get('consistency_weight', 0.3),  # 一致性权重
            'diversity_weight': config.get('diversity_weight', 0.2)       # 多样性权重
        }
        
        # 约束规则
        self.constraint_rules = {
            'sum_range': (0, 27),      # 和值范围
            'span_range': (0, 9),      # 跨度范围
            'digit_range': (0, 9),     # 数字范围
            'forbidden_patterns': [],   # 禁止模式
            'required_patterns': []     # 必需模式
        }
        
        self.logger.info("约束优化器初始化完成")
    
    def optimize_combinations(self, combinations: Dict[str, float], 
                            constraint_info: Dict[str, Any]) -> Dict[str, float]:
        """
        优化组合，应用约束条件
        
        Args:
            combinations: 原始组合概率
            constraint_info: 约束信息
            
        Returns:
            优化后的组合概率
        """
        try:
            # 应用硬约束
            valid_combinations = self._apply_hard_constraints(combinations, constraint_info)
            
            # 应用软约束
            optimized_combinations = self._apply_soft_constraints(valid_combinations, constraint_info)
            
            # 一致性优化
            consistent_combinations = self._apply_consistency_constraints(optimized_combinations, constraint_info)
            
            # 多样性优化
            diverse_combinations = self._apply_diversity_constraints(consistent_combinations)
            
            self.logger.info(f"约束优化完成: {len(combinations)} -> {len(diverse_combinations)}")
            return diverse_combinations
            
        except Exception as e:
            self.logger.error(f"约束优化失败: {e}")
            return combinations
    
    def _apply_hard_constraints(self, combinations: Dict[str, float], 
                               constraint_info: Dict[str, Any]) -> Dict[str, float]:
        """应用硬约束（必须满足的约束）"""
        valid_combinations = {}
        
        for combo, prob in combinations.items():
            if self._validate_hard_constraints(combo, constraint_info):
                valid_combinations[combo] = prob
        
        # 如果没有有效组合，放宽约束
        if not valid_combinations:
            self.logger.warning("硬约束过严，放宽约束条件")
            return self._apply_relaxed_constraints(combinations, constraint_info)
        
        return valid_combinations
    
    def _validate_hard_constraints(self, combination: str, 
                                  constraint_info: Dict[str, Any]) -> bool:
        """验证硬约束"""
        if len(combination) != 3:
            return False
        
        try:
            h, t, u = int(combination[0]), int(combination[1]), int(combination[2])
            
            # 数字范围约束
            if not all(0 <= digit <= 9 for digit in [h, t, u]):
                return False
            
            # 和值约束
            sum_value = h + t + u
            sum_constraints = constraint_info.get('sum_constraints', {})
            if 'valid_range' in sum_constraints:
                sum_min, sum_max = sum_constraints['valid_range']
                if not (sum_min <= sum_value <= sum_max):
                    return False
            
            # 跨度约束
            span_value = max(h, t, u) - min(h, t, u)
            span_constraints = constraint_info.get('span_constraints', {})
            if 'valid_range' in span_constraints:
                span_min, span_max = span_constraints['valid_range']
                if not (span_min <= span_value <= span_max):
                    return False
            
            # 禁止模式
            if self._check_forbidden_patterns(combination):
                return False
            
            return True
            
        except (ValueError, IndexError):
            return False
    
    def _apply_soft_constraints(self, combinations: Dict[str, float], 
                               constraint_info: Dict[str, Any]) -> Dict[str, float]:
        """应用软约束（偏好约束）"""
        optimized_combinations = {}
        
        for combo, prob in combinations.items():
            # 计算软约束分数
            soft_score = self._calculate_soft_constraint_score(combo, constraint_info)
            
            # 调整概率
            adjusted_prob = prob * soft_score
            optimized_combinations[combo] = adjusted_prob
        
        # 重新归一化
        return self._normalize_probabilities(optimized_combinations)
    
    def _calculate_soft_constraint_score(self, combination: str, 
                                       constraint_info: Dict[str, Any]) -> float:
        """计算软约束分数"""
        try:
            h, t, u = int(combination[0]), int(combination[1]), int(combination[2])
            score = 1.0
            
            # 和值偏好分数
            sum_value = h + t + u
            sum_score = self._calculate_sum_preference_score(sum_value, constraint_info)
            score *= sum_score
            
            # 跨度偏好分数
            span_value = max(h, t, u) - min(h, t, u)
            span_score = self._calculate_span_preference_score(span_value, constraint_info)
            score *= span_score
            
            # 模式偏好分数
            pattern_score = self._calculate_pattern_preference_score(combination)
            score *= pattern_score
            
            return max(score, 0.01)  # 确保最小分数
            
        except (ValueError, IndexError):
            return 0.01
    
    def _calculate_sum_preference_score(self, sum_value: int, 
                                      constraint_info: Dict[str, Any]) -> float:
        """计算和值偏好分数"""
        sum_constraints = constraint_info.get('sum_constraints', {})
        
        if 'sum_predictions' in sum_constraints:
            predicted_sums = sum_constraints['sum_predictions']
            if predicted_sums:
                # 计算与预测和值的距离
                min_distance = min(abs(sum_value - pred) for pred in predicted_sums)
                tolerance = self.constraint_params['sum_tolerance']
                
                if min_distance <= tolerance:
                    return 1.0 - (min_distance / tolerance) * 0.5
                else:
                    return 0.1
        
        return 1.0
    
    def _calculate_span_preference_score(self, span_value: int, 
                                       constraint_info: Dict[str, Any]) -> float:
        """计算跨度偏好分数"""
        span_constraints = constraint_info.get('span_constraints', {})
        
        if 'predicted_span' in span_constraints:
            predicted_span = span_constraints['predicted_span']
            distance = abs(span_value - predicted_span)
            tolerance = self.constraint_params['span_tolerance']
            
            if distance <= tolerance:
                return 1.0 - (distance / tolerance) * 0.5
            else:
                return 0.1
        
        return 1.0
    
    def _calculate_pattern_preference_score(self, combination: str) -> float:
        """计算模式偏好分数"""
        h, t, u = int(combination[0]), int(combination[1]), int(combination[2])
        score = 1.0
        
        # 避免全相同数字
        if h == t == u:
            score *= 0.3
        
        # 避免连续数字（如123, 234等）
        if abs(h - t) == 1 and abs(t - u) == 1:
            score *= 0.7
        
        # 偏好有一定差异的组合
        unique_digits = len(set([h, t, u]))
        if unique_digits == 3:
            score *= 1.2
        elif unique_digits == 2:
            score *= 1.0
        else:  # unique_digits == 1
            score *= 0.5
        
        return score
    
    def _apply_consistency_constraints(self, combinations: Dict[str, float], 
                                     constraint_info: Dict[str, Any]) -> Dict[str, float]:
        """应用一致性约束"""
        consistent_combinations = {}
        
        for combo, prob in combinations.items():
            # 计算一致性分数
            consistency_score = self._calculate_consistency_score(combo, constraint_info)
            
            # 应用一致性权重
            weight = self.constraint_params['consistency_weight']
            adjusted_prob = prob * (1 + weight * (consistency_score - 1))
            
            consistent_combinations[combo] = max(adjusted_prob, 0.001)
        
        return self._normalize_probabilities(consistent_combinations)
    
    def _calculate_consistency_score(self, combination: str, 
                                   constraint_info: Dict[str, Any]) -> float:
        """计算一致性分数"""
        try:
            h, t, u = int(combination[0]), int(combination[1]), int(combination[2])
            sum_value = h + t + u
            span_value = max(h, t, u) - min(h, t, u)
            
            consistency_score = 1.0
            
            # 位置预测一致性
            position_constraints = constraint_info.get('position_constraints', {})
            for pos, digit in zip(['hundreds', 'tens', 'units'], [h, t, u]):
                if pos in position_constraints and 'probabilities' in position_constraints[pos]:
                    probs = position_constraints[pos]['probabilities']
                    if len(probs) > digit:
                        consistency_score *= (1 + probs[digit])
            
            # 和值预测一致性
            sum_constraints = constraint_info.get('sum_constraints', {})
            if 'sum_probabilities' in sum_constraints:
                sum_probs = sum_constraints['sum_probabilities']
                if len(sum_probs) > sum_value:
                    consistency_score *= (1 + sum_probs[sum_value])
            
            # 跨度预测一致性
            span_constraints = constraint_info.get('span_constraints', {})
            if 'probabilities' in span_constraints:
                span_probs = span_constraints['probabilities']
                if len(span_probs) > span_value:
                    consistency_score *= (1 + span_probs[span_value])
            
            return min(consistency_score, 3.0)  # 限制最大分数
            
        except (ValueError, IndexError):
            return 1.0
    
    def _apply_diversity_constraints(self, combinations: Dict[str, float]) -> Dict[str, float]:
        """应用多样性约束"""
        # 按概率排序
        sorted_combos = sorted(combinations.items(), key=lambda x: x[1], reverse=True)
        
        # 选择多样化的组合
        diverse_combinations = {}
        selected_patterns = set()
        
        for combo, prob in sorted_combos:
            # 计算模式特征
            pattern = self._extract_pattern_features(combo)
            
            # 检查是否与已选择的模式过于相似
            if not self._is_too_similar(pattern, selected_patterns):
                diverse_combinations[combo] = prob
                selected_patterns.add(pattern)
                
                # 限制推荐数量
                if len(diverse_combinations) >= self.constraint_params['max_recommendations']:
                    break
        
        return self._normalize_probabilities(diverse_combinations)
    
    def _extract_pattern_features(self, combination: str) -> Tuple:
        """提取模式特征"""
        h, t, u = int(combination[0]), int(combination[1]), int(combination[2])
        
        return (
            h + t + u,  # 和值
            max(h, t, u) - min(h, t, u),  # 跨度
            len(set([h, t, u])),  # 不同数字个数
            tuple(sorted([h, t, u]))  # 排序后的数字
        )
    
    def _is_too_similar(self, pattern: Tuple, selected_patterns: Set[Tuple]) -> bool:
        """检查模式是否过于相似"""
        for selected in selected_patterns:
            # 检查和值差异
            if abs(pattern[0] - selected[0]) <= 1:
                # 检查跨度差异
                if abs(pattern[1] - selected[1]) <= 1:
                    # 检查数字组合相似性
                    if pattern[3] == selected[3]:
                        return True
        return False
    
    def _check_forbidden_patterns(self, combination: str) -> bool:
        """检查禁止模式"""
        forbidden_patterns = self.constraint_rules['forbidden_patterns']
        
        for pattern in forbidden_patterns:
            if pattern in combination:
                return True
        
        return False
    
    def _apply_relaxed_constraints(self, combinations: Dict[str, float], 
                                  constraint_info: Dict[str, Any]) -> Dict[str, float]:
        """应用放宽的约束"""
        # 放宽约束参数
        relaxed_params = self.constraint_params.copy()
        relaxed_params['sum_tolerance'] *= 2
        relaxed_params['span_tolerance'] *= 2
        
        valid_combinations = {}
        
        for combo, prob in combinations.items():
            if self._validate_relaxed_constraints(combo, constraint_info, relaxed_params):
                valid_combinations[combo] = prob
        
        return valid_combinations if valid_combinations else combinations
    
    def _validate_relaxed_constraints(self, combination: str, 
                                    constraint_info: Dict[str, Any],
                                    relaxed_params: Dict[str, Any]) -> bool:
        """验证放宽的约束"""
        # 简化的约束验证
        try:
            h, t, u = int(combination[0]), int(combination[1]), int(combination[2])
            
            # 基本范围检查
            if not all(0 <= digit <= 9 for digit in [h, t, u]):
                return False
            
            # 放宽的和值检查
            sum_value = h + t + u
            if not (0 <= sum_value <= 27):
                return False
            
            return True
            
        except (ValueError, IndexError):
            return False
    
    def _normalize_probabilities(self, combinations: Dict[str, float]) -> Dict[str, float]:
        """归一化概率"""
        total_prob = sum(combinations.values())
        
        if total_prob > 0:
            return {k: v / total_prob for k, v in combinations.items()}
        else:
            # 如果总概率为0，返回均匀分布
            uniform_prob = 1.0 / len(combinations) if combinations else 1.0
            return {k: uniform_prob for k in combinations.keys()}
    
    def get_constraint_summary(self, combinations: Dict[str, float], 
                              constraint_info: Dict[str, Any]) -> Dict[str, Any]:
        """获取约束应用摘要"""
        summary = {
            'total_combinations': len(combinations),
            'constraint_satisfaction': {},
            'optimization_metrics': {}
        }
        
        # 计算约束满足度
        satisfied_count = 0
        for combo in combinations.keys():
            if self._validate_hard_constraints(combo, constraint_info):
                satisfied_count += 1
        
        summary['constraint_satisfaction']['hard_constraints'] = satisfied_count / len(combinations)
        
        # 计算优化指标
        probs = list(combinations.values())
        summary['optimization_metrics']['entropy'] = -sum(p * np.log2(p) for p in probs if p > 0)
        summary['optimization_metrics']['max_probability'] = max(probs)
        summary['optimization_metrics']['min_probability'] = min(probs)
        
        return summary
