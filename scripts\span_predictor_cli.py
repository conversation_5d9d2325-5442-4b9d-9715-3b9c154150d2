#!/usr/bin/env python3
"""
P7跨度预测器命令行工具

集成所有功能，提供统一的命令行接口
支持训练、预测、评估和管理功能

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import sys
import os
import argparse
import logging
import subprocess
from pathlib import Path
from typing import List, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def setup_logging(log_level: str = "INFO"):
    """设置日志配置"""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format="%(asctime)s - %(levelname)s - %(message)s"
    )

def create_main_parser():
    """创建主解析器"""
    parser = argparse.ArgumentParser(
        description="P7跨度预测器命令行工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
子命令:
  train      训练跨度预测模型
  predict    执行跨度预测
  evaluate   评估模型性能
  info       显示预测器信息
  manage     管理模型和数据

使用示例:
  # 训练所有模型
  python span_predictor_cli.py train --db-path data/lottery.db
  
  # 预测下一期
  python span_predictor_cli.py predict --db-path data/lottery.db --issue 2025001
  
  # 评估模型性能
  python span_predictor_cli.py evaluate --db-path data/lottery.db
  
  # 显示预测器信息
  python span_predictor_cli.py info --db-path data/lottery.db
        """
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="日志级别 (默认: INFO)"
    )
    
    # 创建子解析器
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # 训练子命令
    create_train_parser(subparsers)
    
    # 预测子命令
    create_predict_parser(subparsers)
    
    # 评估子命令
    create_evaluate_parser(subparsers)
    
    # 信息子命令
    create_info_parser(subparsers)
    
    # 管理子命令
    create_manage_parser(subparsers)
    
    return parser

def create_train_parser(subparsers):
    """创建训练子解析器"""
    train_parser = subparsers.add_parser(
        "train",
        help="训练跨度预测模型",
        description="训练P7跨度预测器的所有模型"
    )
    
    train_parser.add_argument("--db-path", type=str, required=True, help="数据库文件路径")
    train_parser.add_argument("--config-path", type=str, help="配置文件路径")
    train_parser.add_argument("--models", nargs="+", 
                             choices=["xgb", "lgb", "lstm", "classification", "constraint", "ensemble"],
                             help="要训练的模型")
    train_parser.add_argument("--enable-constraints", action="store_true", help="启用双重约束")
    train_parser.add_argument("--enable-patterns", action="store_true", help="启用模式分析")
    train_parser.add_argument("--save-models", action="store_true", help="保存训练好的模型")
    train_parser.add_argument("--model-dir", type=str, default="models/span_predictor", help="模型保存目录")
    train_parser.add_argument("--data-limit", type=int, help="训练数据限制")

def create_predict_parser(subparsers):
    """创建预测子解析器"""
    predict_parser = subparsers.add_parser(
        "predict",
        help="执行跨度预测",
        description="使用训练好的模型进行跨度预测"
    )
    
    predict_parser.add_argument("--db-path", type=str, required=True, help="数据库文件路径")
    predict_parser.add_argument("--config-path", type=str, help="配置文件路径")
    
    # 预测模式
    group = predict_parser.add_mutually_exclusive_group(required=True)
    group.add_argument("--issue", type=str, help="单期预测的期号")
    group.add_argument("--batch", action="store_true", help="批量预测模式")
    
    predict_parser.add_argument("--start-issue", type=str, help="批量预测起始期号")
    predict_parser.add_argument("--count", type=int, default=10, help="批量预测期数")
    predict_parser.add_argument("--model", choices=["xgb", "lgb", "lstm", "classification", "constraint", "ensemble"],
                               default="ensemble", help="使用的模型")
    predict_parser.add_argument("--model-dir", type=str, default="models/span_predictor", help="模型目录")
    predict_parser.add_argument("--enable-constraints", action="store_true", help="启用双重约束")
    predict_parser.add_argument("--enable-patterns", action="store_true", help="启用模式分析")
    predict_parser.add_argument("--all-models", action="store_true", help="使用所有模型预测")
    predict_parser.add_argument("--output", type=str, help="输出文件路径")
    predict_parser.add_argument("--format", choices=["table", "json", "csv"], default="table", help="输出格式")
    predict_parser.add_argument("--verbose", action="store_true", help="详细输出")

def create_evaluate_parser(subparsers):
    """创建评估子解析器"""
    evaluate_parser = subparsers.add_parser(
        "evaluate",
        help="评估模型性能",
        description="评估P7跨度预测器的性能"
    )
    
    evaluate_parser.add_argument("--db-path", type=str, required=True, help="数据库文件路径")
    evaluate_parser.add_argument("--config-path", type=str, help="配置文件路径")
    evaluate_parser.add_argument("--models", nargs="+",
                                choices=["xgb", "lgb", "lstm", "classification", "constraint", "ensemble"],
                                help="要评估的模型")
    evaluate_parser.add_argument("--model-dir", type=str, default="models/span_predictor", help="模型目录")
    evaluate_parser.add_argument("--test-size", type=float, default=0.2, help="测试集比例")
    evaluate_parser.add_argument("--evaluate-constraints", action="store_true", help="评估约束有效性")
    evaluate_parser.add_argument("--evaluate-patterns", action="store_true", help="评估模式分析")
    evaluate_parser.add_argument("--cross-validation", action="store_true", help="执行交叉验证")
    evaluate_parser.add_argument("--cv-folds", type=int, default=5, help="交叉验证折数")
    evaluate_parser.add_argument("--output", type=str, help="输出文件路径")
    evaluate_parser.add_argument("--detailed", action="store_true", help="详细报告")

def create_info_parser(subparsers):
    """创建信息子解析器"""
    info_parser = subparsers.add_parser(
        "info",
        help="显示预测器信息",
        description="显示P7跨度预测器的详细信息"
    )
    
    info_parser.add_argument("--db-path", type=str, required=True, help="数据库文件路径")
    info_parser.add_argument("--config-path", type=str, help="配置文件路径")
    info_parser.add_argument("--model-dir", type=str, default="models/span_predictor", help="模型目录")
    info_parser.add_argument("--show-data", action="store_true", help="显示数据统计")
    info_parser.add_argument("--show-models", action="store_true", help="显示模型状态")
    info_parser.add_argument("--show-config", action="store_true", help="显示配置信息")

def create_manage_parser(subparsers):
    """创建管理子解析器"""
    manage_parser = subparsers.add_parser(
        "manage",
        help="管理模型和数据",
        description="管理P7跨度预测器的模型和数据"
    )
    
    manage_parser.add_argument("--db-path", type=str, required=True, help="数据库文件路径")
    manage_parser.add_argument("--model-dir", type=str, default="models/span_predictor", help="模型目录")
    
    # 管理操作
    manage_group = manage_parser.add_mutually_exclusive_group(required=True)
    manage_group.add_argument("--list-models", action="store_true", help="列出所有模型")
    manage_group.add_argument("--backup-models", type=str, help="备份模型到指定目录")
    manage_group.add_argument("--restore-models", type=str, help="从指定目录恢复模型")
    manage_group.add_argument("--clean-old-data", type=int, help="清理指定天数前的旧数据")
    manage_group.add_argument("--export-data", type=str, help="导出数据到CSV文件")
    manage_group.add_argument("--import-data", type=str, help="从CSV文件导入数据")

def run_script(script_name: str, args: List[str]) -> int:
    """运行指定的脚本"""
    script_path = Path(__file__).parent / script_name
    
    if not script_path.exists():
        print(f"错误: 脚本文件不存在: {script_path}")
        return 1
    
    cmd = [sys.executable, str(script_path)] + args
    
    try:
        result = subprocess.run(cmd, check=False)
        return result.returncode
    except Exception as e:
        print(f"执行脚本失败: {e}")
        return 1

def handle_train_command(args):
    """处理训练命令"""
    script_args = ["--db-path", args.db_path]
    
    if args.config_path:
        script_args.extend(["--config-path", args.config_path])
    
    if args.models:
        script_args.extend(["--models"] + args.models)
    
    if args.enable_constraints:
        script_args.append("--enable-constraints")
    
    if args.enable_patterns:
        script_args.append("--enable-patterns")
    
    if args.save_models:
        script_args.append("--save-models")
    
    if args.model_dir:
        script_args.extend(["--model-dir", args.model_dir])
    
    if args.data_limit:
        script_args.extend(["--data-limit", str(args.data_limit)])
    
    return run_script("train_span_predictor.py", script_args)

def handle_predict_command(args):
    """处理预测命令"""
    script_args = ["--db-path", args.db_path]
    
    if args.config_path:
        script_args.extend(["--config-path", args.config_path])
    
    if args.issue:
        script_args.extend(["--issue", args.issue])
    elif args.batch:
        script_args.append("--batch")
        if args.start_issue:
            script_args.extend(["--start-issue", args.start_issue])
        if args.count:
            script_args.extend(["--count", str(args.count)])
    
    script_args.extend(["--model", args.model])
    script_args.extend(["--model-dir", args.model_dir])
    
    if args.enable_constraints:
        script_args.append("--enable-constraints")
    
    if args.enable_patterns:
        script_args.append("--enable-patterns")
    
    if args.all_models:
        script_args.append("--all-models")
    
    if args.output:
        script_args.extend(["--output", args.output])
    
    script_args.extend(["--format", args.format])
    
    if args.verbose:
        script_args.append("--verbose")
    
    return run_script("predict_span.py", script_args)

def handle_evaluate_command(args):
    """处理评估命令"""
    script_args = ["--db-path", args.db_path]
    
    if args.config_path:
        script_args.extend(["--config-path", args.config_path])
    
    if args.models:
        script_args.extend(["--models"] + args.models)
    
    script_args.extend(["--model-dir", args.model_dir])
    script_args.extend(["--test-size", str(args.test_size)])
    
    if args.evaluate_constraints:
        script_args.append("--evaluate-constraints")
    
    if args.evaluate_patterns:
        script_args.append("--evaluate-patterns")
    
    if args.cross_validation:
        script_args.append("--cross-validation")
        script_args.extend(["--cv-folds", str(args.cv_folds)])
    
    if args.output:
        script_args.extend(["--output", args.output])
    
    if args.detailed:
        script_args.append("--detailed")
    
    return run_script("evaluate_span_predictor.py", script_args)

def handle_info_command(args):
    """处理信息命令"""
    try:
        from src.predictors.span_predictor import SpanPredictor
        
        print("P7跨度预测器信息")
        print("=" * 50)
        
        # 初始化预测器
        predictor = SpanPredictor(args.db_path, args.config_path)
        
        # 显示基本信息
        print(f"数据库路径: {args.db_path}")
        print(f"配置文件: {args.config_path or '默认配置'}")
        print(f"模型目录: {args.model_dir}")
        
        # 显示数据统计
        if args.show_data:
            print("\n数据统计:")
            print("-" * 30)
            try:
                stats = predictor.data_access.get_data_statistics()
                print(f"总预测记录: {stats.get('total_predictions', 0)}")
                print(f"模型数量: {stats.get('model_count', 0)}")
                print(f"期号数量: {stats.get('issue_count', 0)}")
                
                span_dist = stats.get('span_distribution', {})
                if span_dist:
                    print("跨度分布:")
                    for span, count in span_dist.items():
                        print(f"  跨度{span}: {count}次")
            except Exception as e:
                print(f"获取数据统计失败: {e}")
        
        # 显示模型状态
        if args.show_models:
            print("\n模型状态:")
            print("-" * 30)
            
            predictor.build_model()
            
            for model_name, model in predictor.models.items():
                model_file = Path(args.model_dir) / f"{model_name}_span_model.pkl"
                file_exists = model_file.exists()
                
                print(f"{model_name}:")
                print(f"  文件存在: {'是' if file_exists else '否'}")
                print(f"  已训练: {'是' if model.is_trained else '否'}")
                if file_exists:
                    print(f"  文件路径: {model_file}")
        
        # 显示配置信息
        if args.show_config:
            print("\n配置信息:")
            print("-" * 30)
            config = predictor.get_constraint_configuration()
            print(f"双重约束: {'启用' if config.get('dual_constraints_enabled') else '禁用'}")
            print(f"模式分析: {'启用' if config.get('pattern_analysis_enabled') else '禁用'}")
            print(f"位置预测器: {'已设置' if config.get('position_predictors_set') else '未设置'}")
            print(f"和值预测器: {'已设置' if config.get('sum_predictor_set') else '未设置'}")
            print(f"约束模型: {'已训练' if config.get('constraint_model_trained') else '未训练'}")
        
        return 0
        
    except Exception as e:
        print(f"获取信息失败: {e}")
        return 1

def handle_manage_command(args):
    """处理管理命令"""
    try:
        if args.list_models:
            print("模型列表:")
            print("-" * 30)
            
            model_dir = Path(args.model_dir)
            if not model_dir.exists():
                print("模型目录不存在")
                return 1
            
            model_files = list(model_dir.glob("*_span_model.pkl"))
            if not model_files:
                print("没有找到模型文件")
                return 0
            
            for model_file in model_files:
                model_name = model_file.stem.replace("_span_model", "")
                file_size = model_file.stat().st_size / 1024 / 1024  # MB
                print(f"  {model_name}: {model_file} ({file_size:.2f} MB)")
        
        elif args.backup_models:
            import shutil
            
            backup_dir = Path(args.backup_models)
            backup_dir.mkdir(parents=True, exist_ok=True)
            
            model_dir = Path(args.model_dir)
            if not model_dir.exists():
                print("模型目录不存在")
                return 1
            
            model_files = list(model_dir.glob("*_span_model.pkl"))
            
            print(f"备份模型到: {backup_dir}")
            for model_file in model_files:
                dest_file = backup_dir / model_file.name
                shutil.copy2(model_file, dest_file)
                print(f"  备份: {model_file.name}")
            
            print(f"备份完成，共 {len(model_files)} 个文件")
        
        elif args.restore_models:
            import shutil
            
            restore_dir = Path(args.restore_models)
            if not restore_dir.exists():
                print("恢复目录不存在")
                return 1
            
            model_dir = Path(args.model_dir)
            model_dir.mkdir(parents=True, exist_ok=True)
            
            backup_files = list(restore_dir.glob("*_span_model.pkl"))
            
            print(f"从 {restore_dir} 恢复模型:")
            for backup_file in backup_files:
                dest_file = model_dir / backup_file.name
                shutil.copy2(backup_file, dest_file)
                print(f"  恢复: {backup_file.name}")
            
            print(f"恢复完成，共 {len(backup_files)} 个文件")
        
        elif args.clean_old_data:
            from src.predictors.span_predictor import SpanPredictor
            
            predictor = SpanPredictor(args.db_path)
            success = predictor.data_access.cleanup_old_data(args.clean_old_data)
            
            if success:
                print(f"清理 {args.clean_old_data} 天前的旧数据完成")
            else:
                print("清理旧数据失败")
                return 1
        
        elif args.export_data:
            from src.predictors.span_predictor import SpanPredictor
            
            predictor = SpanPredictor(args.db_path)
            data = predictor.data_access.load_lottery_data()
            
            if not data.empty:
                data.to_csv(args.export_data, index=False, encoding='utf-8')
                print(f"数据已导出到: {args.export_data} ({len(data)} 条记录)")
            else:
                print("没有数据可导出")
        
        elif args.import_data:
            print("数据导入功能需要根据具体需求实现")
            return 1
        
        return 0
        
    except Exception as e:
        print(f"管理操作失败: {e}")
        return 1

def main():
    """主函数"""
    parser = create_main_parser()
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.log_level)
    
    # 检查是否提供了子命令
    if not args.command:
        parser.print_help()
        return 1
    
    # 根据子命令执行相应操作
    try:
        if args.command == "train":
            return handle_train_command(args)
        elif args.command == "predict":
            return handle_predict_command(args)
        elif args.command == "evaluate":
            return handle_evaluate_command(args)
        elif args.command == "info":
            return handle_info_command(args)
        elif args.command == "manage":
            return handle_manage_command(args)
        else:
            print(f"未知命令: {args.command}")
            return 1
            
    except KeyboardInterrupt:
        print("\n\n⚠️  操作被用户中断")
        return 1
    except Exception as e:
        print(f"\n❌ 操作失败: {str(e)}")
        logging.error(f"CLI执行失败: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
