"""
数据库初始化脚本
创建数据库表、索引、约束和触发器
"""

import os
import sys
import sqlite3
from pathlib import Path
from datetime import datetime

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.database.models import DatabaseManager, DatabaseSchema


class DatabaseInitializer:
    """数据库初始化器"""
    
    def __init__(self, db_path: str = "data/lottery.db"):
        self.db_path = db_path
        self.db_manager = DatabaseManager(db_path)
        self.schema = DatabaseSchema()
    
    def create_data_directory(self) -> bool:
        """创建数据目录"""
        try:
            data_dir = Path(self.db_path).parent
            data_dir.mkdir(parents=True, exist_ok=True)
            print(f"✅ 数据目录创建成功: {data_dir}")
            return True
        except Exception as e:
            print(f"❌ 数据目录创建失败: {e}")
            return False
    
    def check_database_exists(self) -> bool:
        """检查数据库是否已存在"""
        return Path(self.db_path).exists()
    
    def backup_existing_database(self) -> bool:
        """备份现有数据库"""
        if not self.check_database_exists():
            return True
        
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"{self.db_path}.backup_{timestamp}"
            
            # 复制数据库文件
            import shutil
            shutil.copy2(self.db_path, backup_path)
            print(f"✅ 数据库备份成功: {backup_path}")
            return True
        except Exception as e:
            print(f"❌ 数据库备份失败: {e}")
            return False
    
    def initialize_tables(self) -> bool:
        """初始化数据库表"""
        try:
            with self.db_manager.get_connection() as conn:
                print("创建数据表...")
                
                # 创建主数据表
                conn.execute(self.schema.LOTTERY_DATA_TABLE)
                print("✅ lottery_data 表创建成功")
                
                # 创建采集日志表
                conn.execute(self.schema.COLLECTION_LOGS_TABLE)
                print("✅ collection_logs 表创建成功")
                
                # 创建数据验证表
                conn.execute(self.schema.DATA_VALIDATION_TABLE)
                print("✅ data_validation 表创建成功")
                
                conn.commit()
                return True
        except Exception as e:
            print(f"❌ 数据表创建失败: {e}")
            return False
    
    def create_indexes(self) -> bool:
        """创建索引"""
        try:
            with self.db_manager.get_connection() as conn:
                print("创建索引...")
                
                for i, index_sql in enumerate(self.schema.INDEXES, 1):
                    conn.execute(index_sql)
                    print(f"✅ 索引 {i} 创建成功")
                
                conn.commit()
                return True
        except Exception as e:
            print(f"❌ 索引创建失败: {e}")
            return False
    
    def create_triggers(self) -> bool:
        """创建触发器"""
        try:
            with self.db_manager.get_connection() as conn:
                print("创建触发器...")
                
                for i, trigger_sql in enumerate(self.schema.TRIGGERS, 1):
                    conn.execute(trigger_sql)
                    print(f"✅ 触发器 {i} 创建成功")
                
                conn.commit()
                return True
        except Exception as e:
            print(f"❌ 触发器创建失败: {e}")
            return False
    
    def verify_database_structure(self) -> bool:
        """验证数据库结构"""
        try:
            with self.db_manager.get_connection() as conn:
                print("验证数据库结构...")
                
                # 检查表是否存在
                tables = ['lottery_data', 'collection_logs', 'data_validation']
                for table in tables:
                    cursor = conn.execute(
                        "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
                        (table,)
                    )
                    if cursor.fetchone():
                        print(f"✅ 表 {table} 存在")
                    else:
                        print(f"❌ 表 {table} 不存在")
                        return False
                
                # 检查主表字段
                cursor = conn.execute("PRAGMA table_info(lottery_data)")
                columns = [row[1] for row in cursor.fetchall()]
                expected_columns = [
                    'id', 'issue', 'draw_date', 'hundreds', 'tens', 'units',
                    'trial_hundreds', 'trial_tens', 'trial_units',
                    'machine_number', 'sales_amount', 'prize_info',
                    'sum_value', 'span', 'number_type',
                    'created_at', 'updated_at'
                ]
                
                for col in expected_columns:
                    if col in columns:
                        print(f"✅ 字段 {col} 存在")
                    else:
                        print(f"❌ 字段 {col} 不存在")
                        return False
                
                print(f"✅ 数据库结构验证通过，共 {len(columns)} 个字段")
                return True
        except Exception as e:
            print(f"❌ 数据库结构验证失败: {e}")
            return False
    
    def insert_test_data(self) -> bool:
        """插入测试数据"""
        try:
            from src.database.models import LotteryData, CollectionLog
            
            # 创建测试数据
            test_data = LotteryData(
                issue="2024001",
                draw_date="2024-01-01",
                hundreds=1,
                tens=2,
                units=3,
                trial_hundreds=4,
                trial_tens=5,
                trial_units=6,
                machine_number="M001",
                sales_amount=1000000.0,
                prize_info="一等奖1注"
            )
            
            # 插入测试数据
            if self.db_manager.insert_lottery_data(test_data):
                print("✅ 测试数据插入成功")
                
                # 插入测试日志
                test_log = CollectionLog(
                    source_url="https://test.example.com",
                    records_collected=1,
                    success=True,
                    response_time=0.5
                )
                
                if self.db_manager.insert_collection_log(test_log):
                    print("✅ 测试日志插入成功")
                    return True
            
            return False
        except Exception as e:
            print(f"❌ 测试数据插入失败: {e}")
            return False
    
    def get_database_info(self) -> dict:
        """获取数据库信息"""
        try:
            with self.db_manager.get_connection() as conn:
                info = {}
                
                # 获取数据库文件大小
                if Path(self.db_path).exists():
                    info['file_size'] = Path(self.db_path).stat().st_size
                
                # 获取表信息
                cursor = conn.execute(
                    "SELECT name FROM sqlite_master WHERE type='table'"
                )
                info['tables'] = [row[0] for row in cursor.fetchall()]
                
                # 获取数据统计
                info['data_count'] = self.db_manager.get_data_count()
                info['latest_issue'] = self.db_manager.get_latest_issue()
                
                return info
        except Exception as e:
            print(f"❌ 获取数据库信息失败: {e}")
            return {}
    
    def run_full_initialization(self, backup_existing: bool = True) -> bool:
        """运行完整的数据库初始化"""
        print("=== 福彩3D数据库初始化 ===")
        print(f"数据库路径: {self.db_path}")
        
        # 1. 创建数据目录
        if not self.create_data_directory():
            return False
        
        # 2. 备份现有数据库
        if backup_existing and not self.backup_existing_database():
            return False
        
        # 3. 初始化表结构
        if not self.initialize_tables():
            return False
        
        # 4. 创建索引
        if not self.create_indexes():
            return False
        
        # 5. 创建触发器
        if not self.create_triggers():
            return False
        
        # 6. 验证数据库结构
        if not self.verify_database_structure():
            return False
        
        # 7. 插入测试数据
        if not self.insert_test_data():
            return False
        
        # 8. 显示数据库信息
        info = self.get_database_info()
        print(f"\n=== 数据库初始化完成 ===")
        print(f"数据库文件大小: {info.get('file_size', 0)} 字节")
        print(f"数据表数量: {len(info.get('tables', []))}")
        print(f"数据记录数: {info.get('data_count', 0)}")
        print(f"最新期号: {info.get('latest_issue', 'N/A')}")
        
        return True


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="福彩3D数据库初始化工具")
    parser.add_argument("--db-path", default="data/lottery.db", help="数据库文件路径")
    parser.add_argument("--no-backup", action="store_true", help="不备份现有数据库")
    parser.add_argument("--force", action="store_true", help="强制重新初始化")
    
    args = parser.parse_args()
    
    initializer = DatabaseInitializer(args.db_path)
    
    # 检查是否需要强制重新初始化
    if args.force and initializer.check_database_exists():
        print("⚠️  强制重新初始化模式")
        os.remove(args.db_path)
    
    # 运行初始化
    success = initializer.run_full_initialization(backup_existing=not args.no_backup)
    
    if success:
        print("🎉 数据库初始化成功！")
        return 0
    else:
        print("💥 数据库初始化失败！")
        return 1


if __name__ == "__main__":
    exit(main())
