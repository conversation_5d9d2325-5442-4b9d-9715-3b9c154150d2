#!/usr/bin/env python3
"""
系统稳定性验证脚本

连续运行24小时，验证系统稳定性
监控系统资源使用、错误率、响应时间等指标

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import os
import sys
import time
import json
import logging
import threading
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

class StabilityTester:
    """系统稳定性测试器"""
    
    def __init__(self, test_duration_hours: int = 24):
        """
        初始化稳定性测试器
        
        Args:
            test_duration_hours: 测试持续时间(小时)
        """
        self.project_root = Path(__file__).parent.parent
        self.reports_dir = self.project_root / "reports"
        self.logs_dir = self.project_root / "logs"
        
        # 确保目录存在
        self.reports_dir.mkdir(exist_ok=True)
        self.logs_dir.mkdir(exist_ok=True)
        
        # 测试配置
        self.test_duration_hours = test_duration_hours
        self.check_interval_seconds = 300  # 5分钟检查一次
        self.test_active = False
        
        # 设置日志
        self._setup_logging()
        self.logger = logging.getLogger(__name__)
        
        # 测试数据
        self.test_data = {
            'test_info': {
                'start_time': None,
                'end_time': None,
                'duration_hours': test_duration_hours,
                'check_interval_seconds': self.check_interval_seconds
            },
            'stability_metrics': [],
            'error_log': [],
            'summary': {}
        }
    
    def _setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler(self.logs_dir / 'stability_test.log')
            ]
        )
    
    def check_system_health(self) -> Dict[str, Any]:
        """检查系统健康状态"""
        health_check = {
            'timestamp': datetime.now().isoformat(),
            'status': 'healthy',
            'checks': {},
            'issues': []
        }
        
        try:
            import psutil
            
            # CPU检查
            cpu_percent = psutil.cpu_percent(interval=1)
            health_check['checks']['cpu'] = {
                'percent': cpu_percent,
                'status': 'ok' if cpu_percent < 90 else 'warning'
            }
            
            if cpu_percent >= 90:
                health_check['issues'].append(f"CPU使用率过高: {cpu_percent:.1f}%")
            
            # 内存检查
            memory = psutil.virtual_memory()
            health_check['checks']['memory'] = {
                'percent': memory.percent,
                'available_gb': memory.available / (1024**3),
                'status': 'ok' if memory.percent < 90 else 'warning'
            }
            
            if memory.percent >= 90:
                health_check['issues'].append(f"内存使用率过高: {memory.percent:.1f}%")
            
            # 磁盘检查
            disk = psutil.disk_usage('/')
            health_check['checks']['disk'] = {
                'percent': disk.percent,
                'free_gb': disk.free / (1024**3),
                'status': 'ok' if disk.percent < 95 else 'warning'
            }
            
            if disk.percent >= 95:
                health_check['issues'].append(f"磁盘使用率过高: {disk.percent:.1f}%")
            
            # 进程检查
            process_count = len(psutil.pids())
            health_check['checks']['processes'] = {
                'count': process_count,
                'status': 'ok' if process_count < 1000 else 'warning'
            }
            
            # 总体状态
            if health_check['issues']:
                health_check['status'] = 'warning' if len(health_check['issues']) <= 2 else 'critical'
            
        except Exception as e:
            health_check['status'] = 'error'
            health_check['error'] = str(e)
            self.logger.error(f"系统健康检查失败: {e}")
        
        return health_check
    
    def check_database_connectivity(self) -> Dict[str, Any]:
        """检查数据库连接性"""
        db_check = {
            'timestamp': datetime.now().isoformat(),
            'status': 'ok',
            'response_time': 0,
            'error': None
        }
        
        try:
            import sqlite3
            db_path = self.project_root / "data" / "lottery.db"
            
            start_time = time.time()
            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM lottery_data")
                count = cursor.fetchone()[0]
                
                db_check['response_time'] = time.time() - start_time
                db_check['record_count'] = count
                
                if db_check['response_time'] > 5.0:
                    db_check['status'] = 'slow'
                
        except Exception as e:
            db_check['status'] = 'error'
            db_check['error'] = str(e)
            self.logger.error(f"数据库连接检查失败: {e}")
        
        return db_check
    
    def check_log_files(self) -> Dict[str, Any]:
        """检查日志文件状态"""
        log_check = {
            'timestamp': datetime.now().isoformat(),
            'status': 'ok',
            'files': [],
            'issues': []
        }
        
        try:
            log_files = list(self.logs_dir.glob("*.log"))
            
            for log_file in log_files:
                file_size_mb = log_file.stat().st_size / (1024**2)
                
                file_info = {
                    'name': log_file.name,
                    'size_mb': round(file_size_mb, 2),
                    'status': 'ok'
                }
                
                # 检查文件大小
                if file_size_mb > 100:  # 超过100MB
                    file_info['status'] = 'large'
                    log_check['issues'].append(f"{log_file.name} 文件过大: {file_size_mb:.1f}MB")
                
                log_check['files'].append(file_info)
            
            if log_check['issues']:
                log_check['status'] = 'warning'
                
        except Exception as e:
            log_check['status'] = 'error'
            log_check['error'] = str(e)
            self.logger.error(f"日志文件检查失败: {e}")
        
        return log_check
    
    def perform_stability_check(self) -> Dict[str, Any]:
        """执行稳定性检查"""
        stability_check = {
            'timestamp': datetime.now().isoformat(),
            'overall_status': 'stable',
            'checks': {}
        }
        
        # 系统健康检查
        health = self.check_system_health()
        stability_check['checks']['system_health'] = health
        
        # 数据库连接检查
        db_check = self.check_database_connectivity()
        stability_check['checks']['database'] = db_check
        
        # 日志文件检查
        log_check = self.check_log_files()
        stability_check['checks']['log_files'] = log_check
        
        # 确定总体状态
        issues = []
        if health['status'] != 'healthy':
            issues.extend(health.get('issues', []))
        
        if db_check['status'] == 'error':
            issues.append("数据库连接失败")
        elif db_check['status'] == 'slow':
            issues.append("数据库响应缓慢")
        
        if log_check['status'] != 'ok':
            issues.extend(log_check.get('issues', []))
        
        if issues:
            stability_check['overall_status'] = 'unstable' if len(issues) > 3 else 'warning'
            stability_check['issues'] = issues
        
        return stability_check
    
    def run_stability_test(self):
        """运行稳定性测试"""
        self.test_active = True
        self.test_data['test_info']['start_time'] = datetime.now().isoformat()
        
        self.logger.info(f"开始稳定性测试，持续时间: {self.test_duration_hours} 小时")
        
        end_time = datetime.now() + timedelta(hours=self.test_duration_hours)
        check_count = 0
        
        try:
            while self.test_active and datetime.now() < end_time:
                # 执行稳定性检查
                stability_result = self.perform_stability_check()
                self.test_data['stability_metrics'].append(stability_result)
                
                check_count += 1
                
                # 记录问题
                if stability_result['overall_status'] != 'stable':
                    error_entry = {
                        'timestamp': stability_result['timestamp'],
                        'status': stability_result['overall_status'],
                        'issues': stability_result.get('issues', [])
                    }
                    self.test_data['error_log'].append(error_entry)
                
                # 输出进度
                if check_count % 12 == 0:  # 每小时输出一次
                    elapsed_hours = (datetime.now() - datetime.fromisoformat(self.test_data['test_info']['start_time'])).total_seconds() / 3600
                    self.logger.info(f"稳定性测试进行中... 已运行 {elapsed_hours:.1f} 小时，检查次数: {check_count}")
                
                # 等待下次检查
                time.sleep(self.check_interval_seconds)
                
        except KeyboardInterrupt:
            self.logger.info("稳定性测试被用户中断")
        except Exception as e:
            self.logger.error(f"稳定性测试过程中发生错误: {e}")
        finally:
            self.test_active = False
            self.test_data['test_info']['end_time'] = datetime.now().isoformat()
            
            # 生成测试摘要
            self._generate_summary()
            
            # 保存测试结果
            self._save_test_results()
    
    def _generate_summary(self):
        """生成测试摘要"""
        try:
            metrics = self.test_data['stability_metrics']
            errors = self.test_data['error_log']
            
            if not metrics:
                return
            
            # 计算统计信息
            total_checks = len(metrics)
            stable_checks = sum(1 for m in metrics if m['overall_status'] == 'stable')
            warning_checks = sum(1 for m in metrics if m['overall_status'] == 'warning')
            unstable_checks = sum(1 for m in metrics if m['overall_status'] == 'unstable')
            
            # 计算实际运行时间
            start_time = datetime.fromisoformat(self.test_data['test_info']['start_time'])
            end_time = datetime.fromisoformat(self.test_data['test_info']['end_time'])
            actual_duration_hours = (end_time - start_time).total_seconds() / 3600
            
            summary = {
                'test_duration': {
                    'planned_hours': self.test_duration_hours,
                    'actual_hours': actual_duration_hours,
                    'completion_rate': actual_duration_hours / self.test_duration_hours
                },
                'stability_statistics': {
                    'total_checks': total_checks,
                    'stable_checks': stable_checks,
                    'warning_checks': warning_checks,
                    'unstable_checks': unstable_checks,
                    'stability_rate': stable_checks / total_checks if total_checks > 0 else 0
                },
                'error_statistics': {
                    'total_errors': len(errors),
                    'error_rate': len(errors) / total_checks if total_checks > 0 else 0
                },
                'overall_assessment': self._assess_stability(stable_checks / total_checks if total_checks > 0 else 0)
            }
            
            self.test_data['summary'] = summary
            
        except Exception as e:
            self.logger.error(f"生成测试摘要失败: {e}")
    
    def _assess_stability(self, stability_rate: float) -> str:
        """评估系统稳定性"""
        if stability_rate >= 0.95:
            return "excellent"
        elif stability_rate >= 0.90:
            return "good"
        elif stability_rate >= 0.80:
            return "acceptable"
        elif stability_rate >= 0.70:
            return "poor"
        else:
            return "critical"
    
    def _save_test_results(self):
        """保存测试结果"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = self.reports_dir / f"stability_test_{timestamp}.json"
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.test_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"稳定性测试结果已保存到: {filename}")
            
        except Exception as e:
            self.logger.error(f"保存测试结果失败: {e}")
    
    def stop_test(self):
        """停止测试"""
        self.test_active = False
        self.logger.info("稳定性测试已停止")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="系统稳定性测试工具")
    parser.add_argument('--hours', type=int, default=24, 
                       help='测试持续时间(小时)')
    parser.add_argument('--quick', action='store_true',
                       help='快速测试模式(10分钟)')
    
    args = parser.parse_args()
    
    # 快速测试模式
    if args.quick:
        test_hours = 0.17  # 10分钟
        print("🚀 开始快速稳定性测试 (10分钟)...")
    else:
        test_hours = args.hours
        print(f"🚀 开始稳定性测试 (持续 {test_hours} 小时)...")
    
    print("=" * 50)
    print("注意: 测试期间请保持系统运行")
    print("按 Ctrl+C 可以提前结束测试")
    print("=" * 50)
    
    tester = StabilityTester(test_hours)
    
    try:
        tester.run_stability_test()
        
        # 输出测试结果
        summary = tester.test_data.get('summary', {})
        if summary:
            print("\n📊 稳定性测试结果:")
            print(f"实际运行时间: {summary['test_duration']['actual_hours']:.2f} 小时")
            print(f"稳定性检查次数: {summary['stability_statistics']['total_checks']}")
            print(f"稳定率: {summary['stability_statistics']['stability_rate']:.1%}")
            print(f"总体评估: {summary['overall_assessment']}")
            
            if summary['overall_assessment'] in ['excellent', 'good']:
                print("\n✅ 系统稳定性测试通过！")
            else:
                print("\n⚠️ 系统稳定性需要改进")
        
        return True
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        return True
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
