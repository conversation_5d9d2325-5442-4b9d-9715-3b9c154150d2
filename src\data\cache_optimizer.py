"""
P2高级特征工程系统 - 智能缓存优化器

实现LRU内存缓存和数据库缓存的智能缓存系统，
支持多层缓存策略、缓存预热、批量操作等高级功能。

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import sqlite3
import threading
import time
import pickle
import hashlib
from collections import OrderedDict
from typing import Dict, Any, Optional, List, Tuple, Union
from dataclasses import dataclass
from datetime import datetime, timedelta
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class CacheConfig:
    """缓存配置类"""
    memory_size: int = 1000  # 内存缓存大小
    db_cache_enabled: bool = True  # 是否启用数据库缓存
    db_cache_path: str = "cache/feature_cache.db"  # 数据库缓存路径
    cache_ttl: int = 3600  # 缓存生存时间（秒）
    auto_cleanup: bool = True  # 是否自动清理过期缓存
    preload_enabled: bool = False  # 是否启用预加载
    batch_size: int = 100  # 批量操作大小


@dataclass
class CacheStats:
    """缓存统计信息"""
    total_requests: int = 0
    memory_hits: int = 0
    db_hits: int = 0
    misses: int = 0
    memory_size: int = 0
    db_size: int = 0
    hit_rate: float = 0.0
    memory_hit_rate: float = 0.0
    db_hit_rate: float = 0.0


class CacheOptimizer:
    """
    智能缓存优化器
    
    提供多层缓存策略：
    1. L1: 内存LRU缓存（最快）
    2. L2: SQLite数据库缓存（持久化）
    
    特性：
    - LRU内存缓存管理
    - 数据库持久化缓存
    - 缓存统计和监控
    - 自动过期清理
    - 批量操作支持
    - 缓存预热功能
    """
    
    def __init__(self, config: Optional[CacheConfig] = None):
        """
        初始化缓存优化器
        
        Args:
            config: 缓存配置，如果为None则使用默认配置
        """
        self.config = config or CacheConfig()
        
        # 内存缓存（LRU）
        self._memory_cache: OrderedDict[str, Dict[str, Any]] = OrderedDict()
        self._cache_lock = threading.RLock()
        
        # 数据库缓存
        self._db_connection: Optional[sqlite3.Connection] = None
        self._db_lock = threading.RLock()
        
        # 统计信息
        self._stats = CacheStats()
        
        # 初始化数据库缓存
        if self.config.db_cache_enabled:
            self._init_db_cache()
        
        logger.info(f"CacheOptimizer initialized with config: {self.config}")
    
    def _init_db_cache(self):
        """初始化数据库缓存"""
        try:
            import os
            os.makedirs(os.path.dirname(self.config.db_cache_path), exist_ok=True)
            
            self._db_connection = sqlite3.connect(
                self.config.db_cache_path, 
                check_same_thread=False,
                timeout=30.0
            )
            
            # 创建缓存表
            cursor = self._db_connection.cursor()
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS feature_cache (
                    cache_key TEXT PRIMARY KEY,
                    cache_value BLOB NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    accessed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    ttl INTEGER DEFAULT 3600
                )
            """)
            
            # 创建索引
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_cache_accessed 
                ON feature_cache(accessed_at)
            """)
            
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_cache_created 
                ON feature_cache(created_at)
            """)
            
            self._db_connection.commit()
            cursor.close()
            
            logger.info(f"Database cache initialized at: {self.config.db_cache_path}")
            
        except Exception as e:
            logger.error(f"Failed to initialize database cache: {e}")
            self.config.db_cache_enabled = False
    
    def get_cached_features(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """
        获取缓存的特征数据
        
        Args:
            cache_key: 缓存键
            
        Returns:
            Optional[Dict[str, Any]]: 缓存的特征数据，如果不存在则返回None
        """
        with self._cache_lock:
            self._stats.total_requests += 1
            
            # 1. 检查内存缓存（L1）
            if cache_key in self._memory_cache:
                # LRU: 移动到末尾
                cached_data = self._memory_cache.pop(cache_key)
                self._memory_cache[cache_key] = cached_data
                
                # 检查是否过期
                if self._is_cache_valid(cached_data):
                    self._stats.memory_hits += 1
                    logger.debug(f"Memory cache hit for key: {cache_key}")
                    return cached_data['features']
                else:
                    # 过期，从内存中删除
                    del self._memory_cache[cache_key]
            
            # 2. 检查数据库缓存（L2）
            if self.config.db_cache_enabled:
                db_data = self._get_from_db_cache(cache_key)
                if db_data:
                    self._stats.db_hits += 1
                    
                    # 将数据加载到内存缓存
                    self._save_to_memory_cache(cache_key, db_data)
                    
                    logger.debug(f"Database cache hit for key: {cache_key}")
                    return db_data
            
            # 3. 缓存未命中
            self._stats.misses += 1
            logger.debug(f"Cache miss for key: {cache_key}")
            return None
    
    def cache_features(self, cache_key: str, features: Dict[str, Any]) -> None:
        """
        缓存特征数据
        
        Args:
            cache_key: 缓存键
            features: 要缓存的特征数据
        """
        if not features:
            return
        
        # 创建缓存数据结构
        cache_data = {
            'features': features,
            'created_at': time.time(),
            'accessed_at': time.time(),
            'ttl': self.config.cache_ttl
        }
        
        # 保存到内存缓存
        self._save_to_memory_cache(cache_key, cache_data)
        
        # 保存到数据库缓存
        if self.config.db_cache_enabled:
            self._save_to_db_cache(cache_key, features)
        
        logger.debug(f"Cached features for key: {cache_key}")
    
    def _save_to_memory_cache(self, cache_key: str, cache_data: Dict[str, Any]):
        """保存到内存缓存"""
        with self._cache_lock:
            # LRU管理：如果缓存已满，删除最旧的条目
            if len(self._memory_cache) >= self.config.memory_size:
                oldest_key = next(iter(self._memory_cache))
                del self._memory_cache[oldest_key]
                logger.debug(f"Evicted oldest cache entry: {oldest_key}")
            
            self._memory_cache[cache_key] = cache_data
            self._stats.memory_size = len(self._memory_cache)
    
    def _save_to_db_cache(self, cache_key: str, features: Dict[str, Any]):
        """保存到数据库缓存"""
        if not self._db_connection:
            return
        
        try:
            with self._db_lock:
                cursor = self._db_connection.cursor()
                
                # 序列化特征数据
                serialized_features = pickle.dumps(features)
                
                # 插入或更新缓存
                cursor.execute("""
                    INSERT OR REPLACE INTO feature_cache 
                    (cache_key, cache_value, created_at, accessed_at, ttl)
                    VALUES (?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, ?)
                """, (cache_key, serialized_features, self.config.cache_ttl))
                
                self._db_connection.commit()
                cursor.close()
                
        except Exception as e:
            logger.error(f"Failed to save to database cache: {e}")
    
    def _get_from_db_cache(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """从数据库缓存获取数据"""
        if not self._db_connection:
            return None
        
        try:
            with self._db_lock:
                cursor = self._db_connection.cursor()
                
                # 查询缓存数据
                cursor.execute("""
                    SELECT cache_value, created_at, ttl 
                    FROM feature_cache 
                    WHERE cache_key = ?
                """, (cache_key,))
                
                result = cursor.fetchone()
                cursor.close()
                
                if result:
                    cache_value, created_at, ttl = result
                    
                    # 检查是否过期
                    created_time = datetime.fromisoformat(created_at)
                    if datetime.now() - created_time > timedelta(seconds=ttl):
                        # 过期，删除缓存
                        self._remove_from_db_cache(cache_key)
                        return None
                    
                    # 更新访问时间
                    self._update_db_access_time(cache_key)
                    
                    # 反序列化并返回
                    features = pickle.loads(cache_value)
                    return features
                
                return None
                
        except Exception as e:
            logger.error(f"Failed to get from database cache: {e}")
            return None

    def _is_cache_valid(self, cache_data: Dict[str, Any]) -> bool:
        """检查缓存是否有效（未过期）"""
        if not cache_data:
            return False

        created_at = cache_data.get('created_at', 0)
        ttl = cache_data.get('ttl', self.config.cache_ttl)

        return (time.time() - created_at) < ttl

    def _update_db_access_time(self, cache_key: str):
        """更新数据库缓存的访问时间"""
        if not self._db_connection:
            return

        try:
            with self._db_lock:
                cursor = self._db_connection.cursor()
                cursor.execute("""
                    UPDATE feature_cache
                    SET accessed_at = CURRENT_TIMESTAMP
                    WHERE cache_key = ?
                """, (cache_key,))
                self._db_connection.commit()
                cursor.close()
        except Exception as e:
            logger.error(f"Failed to update access time: {e}")

    def _remove_from_db_cache(self, cache_key: str):
        """从数据库缓存中删除条目"""
        if not self._db_connection:
            return

        try:
            with self._db_lock:
                cursor = self._db_connection.cursor()
                cursor.execute("DELETE FROM feature_cache WHERE cache_key = ?", (cache_key,))
                self._db_connection.commit()
                cursor.close()
        except Exception as e:
            logger.error(f"Failed to remove from database cache: {e}")

    def clear_cache(self, cache_type: str = 'all'):
        """
        清理缓存

        Args:
            cache_type: 缓存类型 ('memory', 'db', 'all')
        """
        if cache_type in ('memory', 'all'):
            with self._cache_lock:
                self._memory_cache.clear()
                self._stats.memory_size = 0
                logger.info("Memory cache cleared")

        if cache_type in ('db', 'all') and self.config.db_cache_enabled:
            try:
                with self._db_lock:
                    cursor = self._db_connection.cursor()
                    cursor.execute("DELETE FROM feature_cache")
                    self._db_connection.commit()
                    cursor.close()
                    logger.info("Database cache cleared")
            except Exception as e:
                logger.error(f"Failed to clear database cache: {e}")

    def cleanup_expired_cache(self):
        """清理过期的缓存条目"""
        # 清理内存缓存中的过期条目
        with self._cache_lock:
            expired_keys = []
            for key, cache_data in self._memory_cache.items():
                if not self._is_cache_valid(cache_data):
                    expired_keys.append(key)

            for key in expired_keys:
                del self._memory_cache[key]

            if expired_keys:
                logger.info(f"Cleaned up {len(expired_keys)} expired memory cache entries")

        # 清理数据库缓存中的过期条目
        if self.config.db_cache_enabled and self._db_connection:
            try:
                with self._db_lock:
                    cursor = self._db_connection.cursor()
                    cursor.execute("""
                        DELETE FROM feature_cache
                        WHERE datetime(created_at, '+' || ttl || ' seconds') < datetime('now')
                    """)
                    deleted_count = cursor.rowcount
                    self._db_connection.commit()
                    cursor.close()

                    if deleted_count > 0:
                        logger.info(f"Cleaned up {deleted_count} expired database cache entries")
            except Exception as e:
                logger.error(f"Failed to cleanup expired database cache: {e}")

    def get_cache_stats(self) -> CacheStats:
        """
        获取缓存统计信息

        Returns:
            CacheStats: 缓存统计信息
        """
        with self._cache_lock:
            # 更新统计信息
            self._stats.memory_size = len(self._memory_cache)

            if self._stats.total_requests > 0:
                self._stats.hit_rate = (self._stats.memory_hits + self._stats.db_hits) / self._stats.total_requests
                self._stats.memory_hit_rate = self._stats.memory_hits / self._stats.total_requests
                self._stats.db_hit_rate = self._stats.db_hits / self._stats.total_requests

            # 获取数据库缓存大小
            if self.config.db_cache_enabled and self._db_connection:
                try:
                    with self._db_lock:
                        cursor = self._db_connection.cursor()
                        cursor.execute("SELECT COUNT(*) FROM feature_cache")
                        self._stats.db_size = cursor.fetchone()[0]
                        cursor.close()
                except Exception as e:
                    logger.error(f"Failed to get database cache size: {e}")

            return self._stats

    def get_cache_info(self) -> Dict[str, Any]:
        """
        获取详细的缓存信息

        Returns:
            Dict[str, Any]: 缓存信息字典
        """
        stats = self.get_cache_stats()

        return {
            'config': {
                'memory_size_limit': self.config.memory_size,
                'db_cache_enabled': self.config.db_cache_enabled,
                'cache_ttl': self.config.cache_ttl,
                'auto_cleanup': self.config.auto_cleanup
            },
            'stats': {
                'total_requests': stats.total_requests,
                'memory_hits': stats.memory_hits,
                'db_hits': stats.db_hits,
                'misses': stats.misses,
                'hit_rate': f"{stats.hit_rate:.2%}",
                'memory_hit_rate': f"{stats.memory_hit_rate:.2%}",
                'db_hit_rate': f"{stats.db_hit_rate:.2%}"
            },
            'size': {
                'memory_cache_size': stats.memory_size,
                'db_cache_size': stats.db_size,
                'memory_usage_rate': f"{stats.memory_size / self.config.memory_size:.2%}" if self.config.memory_size > 0 else "0%"
            }
        }

    def batch_cache_features(self, cache_data: Dict[str, Dict[str, Any]]):
        """
        批量缓存特征数据

        Args:
            cache_data: 缓存数据字典，格式为 {cache_key: features}
        """
        if not cache_data:
            return

        # 批量保存到内存缓存
        with self._cache_lock:
            for cache_key, features in cache_data.items():
                cache_entry = {
                    'features': features,
                    'created_at': time.time(),
                    'accessed_at': time.time(),
                    'ttl': self.config.cache_ttl
                }

                # LRU管理
                if len(self._memory_cache) >= self.config.memory_size:
                    oldest_key = next(iter(self._memory_cache))
                    del self._memory_cache[oldest_key]

                self._memory_cache[cache_key] = cache_entry

            self._stats.memory_size = len(self._memory_cache)

        # 批量保存到数据库缓存
        if self.config.db_cache_enabled and self._db_connection:
            try:
                with self._db_lock:
                    cursor = self._db_connection.cursor()

                    batch_data = []
                    for cache_key, features in cache_data.items():
                        serialized_features = pickle.dumps(features)
                        batch_data.append((cache_key, serialized_features, self.config.cache_ttl))

                    cursor.executemany("""
                        INSERT OR REPLACE INTO feature_cache
                        (cache_key, cache_value, created_at, accessed_at, ttl)
                        VALUES (?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, ?)
                    """, batch_data)

                    self._db_connection.commit()
                    cursor.close()

                    logger.info(f"Batch cached {len(cache_data)} feature entries")

            except Exception as e:
                logger.error(f"Failed to batch save to database cache: {e}")

    def batch_get_cached_features(self, cache_keys: List[str]) -> Dict[str, Optional[Dict[str, Any]]]:
        """
        批量获取缓存的特征数据

        Args:
            cache_keys: 缓存键列表

        Returns:
            Dict[str, Optional[Dict[str, Any]]]: 缓存结果字典
        """
        results = {}

        for cache_key in cache_keys:
            results[cache_key] = self.get_cached_features(cache_key)

        return results

    def preload_cache(self, cache_keys: List[str], feature_generator_func):
        """
        预加载缓存

        Args:
            cache_keys: 要预加载的缓存键列表
            feature_generator_func: 特征生成函数，接受cache_key参数
        """
        if not self.config.preload_enabled:
            logger.info("Cache preload is disabled")
            return

        logger.info(f"Starting cache preload for {len(cache_keys)} keys")

        preload_data = {}
        for cache_key in cache_keys:
            # 检查是否已经缓存
            if self.get_cached_features(cache_key) is None:
                try:
                    features = feature_generator_func(cache_key)
                    if features:
                        preload_data[cache_key] = features
                except Exception as e:
                    logger.error(f"Failed to generate features for preload key {cache_key}: {e}")

        if preload_data:
            self.batch_cache_features(preload_data)
            logger.info(f"Preloaded {len(preload_data)} cache entries")

    def optimize_cache(self):
        """
        优化缓存性能

        执行以下优化操作：
        1. 清理过期缓存
        2. 整理数据库
        3. 更新统计信息
        """
        logger.info("Starting cache optimization")

        # 清理过期缓存
        if self.config.auto_cleanup:
            self.cleanup_expired_cache()

        # 数据库优化
        if self.config.db_cache_enabled and self._db_connection:
            try:
                with self._db_lock:
                    cursor = self._db_connection.cursor()
                    cursor.execute("VACUUM")
                    cursor.execute("ANALYZE")
                    self._db_connection.commit()
                    cursor.close()
                    logger.info("Database cache optimized")
            except Exception as e:
                logger.error(f"Failed to optimize database cache: {e}")

        logger.info("Cache optimization completed")

    def export_cache_data(self, export_path: str, format: str = 'json'):
        """
        导出缓存数据

        Args:
            export_path: 导出文件路径
            format: 导出格式 ('json', 'pickle')
        """
        try:
            cache_data = {}

            # 导出内存缓存
            with self._cache_lock:
                for key, cache_entry in self._memory_cache.items():
                    cache_data[key] = cache_entry['features']

            # 导出数据库缓存
            if self.config.db_cache_enabled and self._db_connection:
                with self._db_lock:
                    cursor = self._db_connection.cursor()
                    cursor.execute("SELECT cache_key, cache_value FROM feature_cache")

                    for cache_key, cache_value in cursor.fetchall():
                        if cache_key not in cache_data:  # 避免重复
                            features = pickle.loads(cache_value)
                            cache_data[cache_key] = features

                    cursor.close()

            # 保存数据
            if format == 'json':
                import json
                with open(export_path, 'w', encoding='utf-8') as f:
                    json.dump(cache_data, f, indent=2, ensure_ascii=False)
            elif format == 'pickle':
                with open(export_path, 'wb') as f:
                    pickle.dump(cache_data, f)
            else:
                raise ValueError(f"Unsupported export format: {format}")

            logger.info(f"Cache data exported to {export_path} ({len(cache_data)} entries)")

        except Exception as e:
            logger.error(f"Failed to export cache data: {e}")

    def close(self):
        """关闭缓存优化器，释放资源"""
        if self._db_connection:
            try:
                self._db_connection.close()
                logger.info("Database connection closed")
            except Exception as e:
                logger.error(f"Failed to close database connection: {e}")

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()
        return False  # 不抑制异常


# 工具函数
def create_cache_key(issue: str, feature_type: str, **kwargs) -> str:
    """
    创建标准化的缓存键

    Args:
        issue: 期号
        feature_type: 特征类型
        **kwargs: 其他参数

    Returns:
        str: 缓存键
    """
    key_parts = [issue, feature_type]

    # 添加其他参数
    for key, value in sorted(kwargs.items()):
        key_parts.append(f"{key}={value}")

    key_string = "_".join(key_parts)

    # 如果键太长，使用哈希
    if len(key_string) > 200:
        key_hash = hashlib.md5(key_string.encode()).hexdigest()
        return f"{issue}_{feature_type}_{key_hash}"

    return key_string


def get_default_cache_config() -> CacheConfig:
    """获取默认缓存配置"""
    return CacheConfig(
        memory_size=1000,
        db_cache_enabled=True,
        db_cache_path="cache/feature_cache.db",
        cache_ttl=3600,
        auto_cleanup=True,
        preload_enabled=False,
        batch_size=100
    )
