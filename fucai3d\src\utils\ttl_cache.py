#!/usr/bin/env python3
"""
TTL (Time To Live) 缓存工具类
用于P9系统API响应缓存优化
"""

import time
import threading
from typing import Any, Dict, Optional, Callable
from functools import wraps
import hashlib
import json


class TTLCache:
    """带有TTL（生存时间）的缓存类"""
    
    def __init__(self, default_ttl: int = 60):
        """
        初始化TTL缓存
        
        Args:
            default_ttl: 默认缓存时间（秒）
        """
        self.default_ttl = default_ttl
        self._cache: Dict[str, Dict[str, Any]] = {}
        self._lock = threading.RLock()
    
    def _generate_key(self, *args, **kwargs) -> str:
        """生成缓存键"""
        # 将参数转换为字符串并生成哈希
        key_data = {
            'args': args,
            'kwargs': kwargs
        }
        key_str = json.dumps(key_data, sort_keys=True, default=str)
        return hashlib.md5(key_str.encode()).hexdigest()
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        with self._lock:
            if key not in self._cache:
                return None
            
            cache_entry = self._cache[key]
            current_time = time.time()
            
            # 检查是否过期
            if current_time > cache_entry['expires_at']:
                del self._cache[key]
                return None
            
            # 更新访问时间
            cache_entry['last_accessed'] = current_time
            return cache_entry['value']
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """设置缓存值"""
        if ttl is None:
            ttl = self.default_ttl
        
        current_time = time.time()
        
        with self._lock:
            self._cache[key] = {
                'value': value,
                'created_at': current_time,
                'last_accessed': current_time,
                'expires_at': current_time + ttl,
                'ttl': ttl
            }
    
    def delete(self, key: str) -> bool:
        """删除缓存项"""
        with self._lock:
            if key in self._cache:
                del self._cache[key]
                return True
            return False
    
    def clear(self) -> None:
        """清空所有缓存"""
        with self._lock:
            self._cache.clear()
    
    def cleanup_expired(self) -> int:
        """清理过期的缓存项，返回清理的数量"""
        current_time = time.time()
        expired_keys = []
        
        with self._lock:
            for key, cache_entry in self._cache.items():
                if current_time > cache_entry['expires_at']:
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self._cache[key]
        
        return len(expired_keys)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self._lock:
            current_time = time.time()
            total_items = len(self._cache)
            expired_items = 0
            
            for cache_entry in self._cache.values():
                if current_time > cache_entry['expires_at']:
                    expired_items += 1
            
            return {
                'total_items': total_items,
                'active_items': total_items - expired_items,
                'expired_items': expired_items,
                'cache_keys': list(self._cache.keys())
            }


def ttl_cache(ttl: int = 60, cache_instance: Optional[TTLCache] = None):
    """
    TTL缓存装饰器
    
    Args:
        ttl: 缓存时间（秒）
        cache_instance: 缓存实例，如果为None则创建新实例
    """
    if cache_instance is None:
        cache_instance = TTLCache(default_ttl=ttl)
    
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = f"{func.__name__}_{cache_instance._generate_key(*args, **kwargs)}"
            
            # 尝试从缓存获取
            cached_result = cache_instance.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # 执行函数并缓存结果
            result = await func(*args, **kwargs)
            cache_instance.set(cache_key, result, ttl)
            return result
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = f"{func.__name__}_{cache_instance._generate_key(*args, **kwargs)}"
            
            # 尝试从缓存获取
            cached_result = cache_instance.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            cache_instance.set(cache_key, result, ttl)
            return result
        
        # 根据函数是否为协程选择包装器
        import asyncio
        if asyncio.iscoroutinefunction(func):
            wrapper = async_wrapper
        else:
            wrapper = sync_wrapper
        
        # 添加缓存管理方法
        wrapper.cache = cache_instance
        wrapper.clear_cache = lambda: cache_instance.clear()
        wrapper.get_cache_stats = lambda: cache_instance.get_stats()
        
        return wrapper
    
    return decorator


# 全局缓存实例
api_cache = TTLCache(default_ttl=60)
performance_cache = TTLCache(default_ttl=30)  # 性能数据缓存时间更短
system_status_cache = TTLCache(default_ttl=60)  # 系统状态缓存


def get_api_cache() -> TTLCache:
    """获取API缓存实例"""
    return api_cache


def get_performance_cache() -> TTLCache:
    """获取性能数据缓存实例"""
    return performance_cache


def get_system_status_cache() -> TTLCache:
    """获取系统状态缓存实例"""
    return system_status_cache


def cleanup_all_caches() -> Dict[str, int]:
    """清理所有缓存的过期项"""
    return {
        'api_cache': api_cache.cleanup_expired(),
        'performance_cache': performance_cache.cleanup_expired(),
        'system_status_cache': system_status_cache.cleanup_expired()
    }


def get_all_cache_stats() -> Dict[str, Dict[str, Any]]:
    """获取所有缓存的统计信息"""
    return {
        'api_cache': api_cache.get_stats(),
        'performance_cache': performance_cache.get_stats(),
        'system_status_cache': system_status_cache.get_stats()
    }
