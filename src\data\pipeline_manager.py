"""
P2特征工程Pipeline管理器
集成Feature-engine库，提供标准化的特征工程Pipeline
支持不同预测器的专用Pipeline和ML训练Pipeline
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Union, Tuple
from sklearn.pipeline import Pipeline
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.compose import ColumnTransformer

# Feature-engine库导入
from feature_engine.creation import MathFeatures
from feature_engine.discretisation import EqualFrequencyDiscretiser, EqualWidthDiscretiser
from feature_engine.encoding import OneHotEncoder, OrdinalEncoder
from feature_engine.imputation import MeanMedianImputer, CategoricalImputer
from feature_engine.outliers import Winsorizer
from feature_engine.selection import DropFeatures, SelectByShuffling
from feature_engine.transformation import LogTransformer, PowerTransformer


class FeaturePipelineManager:
    """特征工程Pipeline管理器"""
    
    def __init__(self):
        """初始化Pipeline管理器"""
        self.predictor_configs = {
            'hundreds': self._get_hundreds_config(),
            'tens': self._get_tens_config(),
            'units': self._get_units_config(),
            'sum': self._get_sum_config(),
            'span': self._get_span_config()
        }
        
        # 通用特征列定义
        self.numeric_features = [
            'hundreds', 'tens', 'units', 'sum_value', 'span',
            'hundreds_frequency', 'tens_frequency', 'units_frequency',
            'hundreds_missing_count', 'tens_missing_count', 'units_missing_count'
        ]
        
        self.categorical_features = [
            'number_type', 'hundreds_cold_hot_status', 'tens_cold_hot_status',
            'units_cold_hot_status', 'sum_distribution_zone', 'span_distribution_zone'
        ]
    
    def create_predictor_pipeline(self, predictor_type: str, 
                                include_feature_selection: bool = True) -> Pipeline:
        """
        创建预测器专用Pipeline
        
        Args:
            predictor_type: 预测器类型 ('hundreds', 'tens', 'units', 'sum', 'span')
            include_feature_selection: 是否包含特征选择
            
        Returns:
            Pipeline: 配置好的sklearn Pipeline
        """
        if predictor_type not in self.predictor_configs:
            raise ValueError(f"不支持的预测器类型: {predictor_type}")
        
        config = self.predictor_configs[predictor_type]
        steps = []
        
        # 1. 数据清洗和缺失值处理
        steps.append(('imputer_numeric', MeanMedianImputer(
            imputation_method='median',
            variables=config['numeric_vars']
        )))
        
        steps.append(('imputer_categorical', CategoricalImputer(
            imputation_method='frequent',
            variables=config['categorical_vars']
        )))
        
        # 2. 异常值处理
        steps.append(('outlier_handler', Winsorizer(
            capping_method='iqr',
            tail='both',
            fold=1.5,
            variables=config['numeric_vars']
        )))
        
        # 3. 特征创建
        if config.get('create_math_features', True):
            steps.append(('math_features', MathFeatures(
                variables=config['numeric_vars'][:3],  # 取前3个数值特征
                func=['sum', 'prod', 'mean', 'std']
            )))
        
        # 4. 特征变换
        if config.get('apply_log_transform', False):
            log_vars = [var for var in config['numeric_vars'] if var.endswith('_frequency')]
            if log_vars:
                steps.append(('log_transformer', LogTransformer(variables=log_vars)))
        
        # 5. 特征编码
        if config['categorical_vars']:
            steps.append(('categorical_encoder', OneHotEncoder(
                variables=config['categorical_vars'],
                drop_last=True
            )))
        
        # 6. 特征离散化（可选）
        if config.get('discretize_features', False):
            steps.append(('discretizer', EqualFrequencyDiscretiser(
                variables=config['numeric_vars'],
                q=5  # 分为5个等频区间
            )))
        
        # 7. 特征选择（可选）
        if include_feature_selection and config.get('feature_selection', True):
            steps.append(('feature_selector', SelectByShuffling(
                estimator=config.get('selector_estimator'),
                scoring='accuracy',
                cv=3
            )))
        
        # 8. 标准化
        steps.append(('scaler', StandardScaler()))
        
        return Pipeline(steps)
    
    def create_ml_pipeline(self, feature_types: List[str], 
                          target_type: str = 'classification') -> Pipeline:
        """
        创建ML训练专用Pipeline
        
        Args:
            feature_types: 特征类型列表
            target_type: 目标类型 ('classification' 或 'regression')
            
        Returns:
            Pipeline: ML训练Pipeline
        """
        steps = []
        
        # 1. 综合数据清洗
        all_numeric_vars = []
        all_categorical_vars = []
        
        for feature_type in feature_types:
            if feature_type in self.predictor_configs:
                config = self.predictor_configs[feature_type]
                all_numeric_vars.extend(config['numeric_vars'])
                all_categorical_vars.extend(config['categorical_vars'])
        
        # 去重
        all_numeric_vars = list(set(all_numeric_vars))
        all_categorical_vars = list(set(all_categorical_vars))
        
        # 2. 缺失值处理
        steps.append(('imputer_numeric', MeanMedianImputer(
            imputation_method='median',
            variables=all_numeric_vars
        )))
        
        steps.append(('imputer_categorical', CategoricalImputer(
            imputation_method='frequent',
            variables=all_categorical_vars
        )))
        
        # 3. 异常值处理
        steps.append(('outlier_handler', Winsorizer(
            capping_method='iqr',
            tail='both',
            fold=2.0,  # ML训练时更宽松的异常值处理
            variables=all_numeric_vars
        )))
        
        # 4. 特征工程
        if len(all_numeric_vars) >= 3:
            steps.append(('math_features', MathFeatures(
                variables=all_numeric_vars[:5],  # 取前5个特征进行数学运算
                func=['sum', 'prod', 'mean', 'std', 'max', 'min']
            )))
        
        # 5. 特征编码
        if all_categorical_vars:
            if target_type == 'classification':
                steps.append(('categorical_encoder', OneHotEncoder(
                    variables=all_categorical_vars,
                    drop_last=True
                )))
            else:
                steps.append(('categorical_encoder', OrdinalEncoder(
                    variables=all_categorical_vars
                )))
        
        # 6. 特征变换
        frequency_vars = [var for var in all_numeric_vars if 'frequency' in var]
        if frequency_vars:
            steps.append(('power_transformer', PowerTransformer(
                variables=frequency_vars,
                exp=0.5  # 平方根变换
            )))
        
        # 7. 标准化
        steps.append(('scaler', StandardScaler()))
        
        return Pipeline(steps)
    
    def get_sklearn_pipeline(self, steps: List[Tuple[str, Any]]) -> Pipeline:
        """
        创建自定义sklearn Pipeline
        
        Args:
            steps: Pipeline步骤列表
            
        Returns:
            Pipeline: sklearn Pipeline对象
        """
        return Pipeline(steps)
    
    def _get_hundreds_config(self) -> Dict[str, Any]:
        """获取百位预测器配置"""
        return {
            'numeric_vars': [
                'hundreds', 'sum_value', 'span', 'hundreds_frequency',
                'hundreds_missing_count', 'tens', 'units'
            ],
            'categorical_vars': [
                'number_type', 'hundreds_cold_hot_status'
            ],
            'create_math_features': True,
            'apply_log_transform': True,
            'discretize_features': False,
            'feature_selection': True,
            'selector_estimator': None  # 将在后续设置
        }
    
    def _get_tens_config(self) -> Dict[str, Any]:
        """获取十位预测器配置"""
        return {
            'numeric_vars': [
                'tens', 'sum_value', 'span', 'tens_frequency',
                'tens_missing_count', 'hundreds', 'units'
            ],
            'categorical_vars': [
                'number_type', 'tens_cold_hot_status'
            ],
            'create_math_features': True,
            'apply_log_transform': True,
            'discretize_features': False,
            'feature_selection': True,
            'selector_estimator': None
        }
    
    def _get_units_config(self) -> Dict[str, Any]:
        """获取个位预测器配置"""
        return {
            'numeric_vars': [
                'units', 'sum_value', 'span', 'units_frequency',
                'units_missing_count', 'hundreds', 'tens'
            ],
            'categorical_vars': [
                'number_type', 'units_cold_hot_status'
            ],
            'create_math_features': True,
            'apply_log_transform': True,
            'discretize_features': False,
            'feature_selection': True,
            'selector_estimator': None
        }
    
    def _get_sum_config(self) -> Dict[str, Any]:
        """获取和值预测器配置"""
        return {
            'numeric_vars': [
                'sum_value', 'hundreds', 'tens', 'units', 'span'
            ],
            'categorical_vars': [
                'number_type', 'sum_distribution_zone'
            ],
            'create_math_features': False,  # 和值本身就是数学特征
            'apply_log_transform': False,
            'discretize_features': True,
            'feature_selection': True,
            'selector_estimator': None
        }
    
    def _get_span_config(self) -> Dict[str, Any]:
        """获取跨度预测器配置"""
        return {
            'numeric_vars': [
                'span', 'hundreds', 'tens', 'units', 'sum_value'
            ],
            'categorical_vars': [
                'number_type', 'span_distribution_zone'
            ],
            'create_math_features': False,  # 跨度本身就是数学特征
            'apply_log_transform': False,
            'discretize_features': True,
            'feature_selection': True,
            'selector_estimator': None
        }
    
    def validate_pipeline(self, pipeline: Pipeline, sample_data: pd.DataFrame) -> Dict[str, Any]:
        """
        验证Pipeline配置
        
        Args:
            pipeline: 要验证的Pipeline
            sample_data: 样本数据
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        try:
            # 尝试拟合Pipeline
            pipeline.fit(sample_data)
            
            # 尝试变换数据
            transformed_data = pipeline.transform(sample_data)
            
            return {
                'status': 'success',
                'input_shape': sample_data.shape,
                'output_shape': transformed_data.shape,
                'pipeline_steps': len(pipeline.steps),
                'step_names': [step[0] for step in pipeline.steps]
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'error_message': str(e),
                'input_shape': sample_data.shape,
                'pipeline_steps': len(pipeline.steps)
            }
