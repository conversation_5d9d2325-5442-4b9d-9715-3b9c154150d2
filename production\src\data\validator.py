"""
数据验证器模块
实现完整的数据验证系统，确保数据质量和一致性
"""

import re
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class ValidationResult:
    """验证结果"""
    is_valid: bool
    error_type: str = ""
    error_message: str = ""


@dataclass
class QualityReport:
    """质量报告"""
    total_records: int
    valid_records: int
    virtual_data_count: int
    duplicate_count: int
    quality_score: float
    validation_passed: bool
    virtual_data_found: List[Dict]
    recommendations: List[str]
    field_name: str = ""
    expected_value: Any = None
    actual_value: Any = None


class DataFormatValidator:
    """数据格式验证器"""
    
    def __init__(self):
        self.field_rules = self._init_field_rules()
    
    def _init_field_rules(self) -> Dict[str, Dict]:
        """初始化字段验证规则"""
        return {
            'issue': {
                'type': str,
                'pattern': r'^\d{7}$',
                'required': True,
                'description': '期号必须是7位数字'
            },
            'draw_date': {
                'type': str,
                'pattern': r'^\d{4}-\d{2}-\d{2}$',
                'required': True,
                'description': '开奖日期必须是YYYY-MM-DD格式'
            },
            'hundreds': {
                'type': int,
                'min_value': 0,
                'max_value': 9,
                'required': True,
                'description': '百位数字必须是0-9'
            },
            'tens': {
                'type': int,
                'min_value': 0,
                'max_value': 9,
                'required': True,
                'description': '十位数字必须是0-9'
            },
            'units': {
                'type': int,
                'min_value': 0,
                'max_value': 9,
                'required': True,
                'description': '个位数字必须是0-9'
            },
            'trial_hundreds': {
                'type': int,
                'min_value': 0,
                'max_value': 9,
                'required': False,
                'description': '试机百位数字必须是0-9'
            },
            'trial_tens': {
                'type': int,
                'min_value': 0,
                'max_value': 9,
                'required': False,
                'description': '试机十位数字必须是0-9'
            },
            'trial_units': {
                'type': int,
                'min_value': 0,
                'max_value': 9,
                'required': False,
                'description': '试机个位数字必须是0-9'
            },
            'machine_number': {
                'type': str,
                'pattern': r'^[A-Z]?\d+$',
                'required': False,
                'description': '机器号格式不正确'
            },
            'sales_amount': {
                'type': float,
                'min_value': 0,
                'required': False,
                'description': '销售额必须大于等于0'
            },
            'sum_value': {
                'type': int,
                'min_value': 0,
                'max_value': 27,
                'required': False,
                'description': '和值必须是0-27'
            },
            'span': {
                'type': int,
                'min_value': 0,
                'max_value': 9,
                'required': False,
                'description': '跨度必须是0-9'
            },
            'number_type': {
                'type': str,
                'allowed_values': ['豹子', '对子', '组六'],
                'required': False,
                'description': '号码类型必须是豹子、对子或组六'
            }
        }
    
    def validate_field(self, field_name: str, value: Any) -> ValidationResult:
        """验证单个字段"""
        if field_name not in self.field_rules:
            return ValidationResult(
                is_valid=False,
                error_type="unknown_field",
                error_message=f"未知字段: {field_name}",
                field_name=field_name
            )
        
        rule = self.field_rules[field_name]
        
        # 检查必填字段
        if rule.get('required', False) and (value is None or value == ""):
            return ValidationResult(
                is_valid=False,
                error_type="required_field_missing",
                error_message=f"必填字段缺失: {field_name}",
                field_name=field_name
            )
        
        # 如果字段为空且非必填，跳过验证
        if value is None or value == "":
            return ValidationResult(is_valid=True)
        
        # 类型检查
        expected_type = rule.get('type')
        if expected_type and not isinstance(value, expected_type):
            try:
                # 尝试类型转换
                if expected_type == int:
                    value = int(value)
                elif expected_type == float:
                    value = float(value)
                elif expected_type == str:
                    value = str(value)
            except (ValueError, TypeError):
                return ValidationResult(
                    is_valid=False,
                    error_type="type_error",
                    error_message=f"字段 {field_name} 类型错误，期望 {expected_type.__name__}",
                    field_name=field_name,
                    expected_value=expected_type.__name__,
                    actual_value=type(value).__name__
                )
        
        # 模式匹配检查
        pattern = rule.get('pattern')
        if pattern and isinstance(value, str):
            if not re.match(pattern, value):
                return ValidationResult(
                    is_valid=False,
                    error_type="pattern_mismatch",
                    error_message=f"字段 {field_name} 格式不正确: {rule.get('description', '')}",
                    field_name=field_name,
                    actual_value=value
                )
        
        # 数值范围检查
        if isinstance(value, (int, float)):
            min_val = rule.get('min_value')
            max_val = rule.get('max_value')
            
            if min_val is not None and value < min_val:
                return ValidationResult(
                    is_valid=False,
                    error_type="value_too_small",
                    error_message=f"字段 {field_name} 值过小，最小值: {min_val}",
                    field_name=field_name,
                    expected_value=f">= {min_val}",
                    actual_value=value
                )
            
            if max_val is not None and value > max_val:
                return ValidationResult(
                    is_valid=False,
                    error_type="value_too_large",
                    error_message=f"字段 {field_name} 值过大，最大值: {max_val}",
                    field_name=field_name,
                    expected_value=f"<= {max_val}",
                    actual_value=value
                )
        
        # 允许值检查
        allowed_values = rule.get('allowed_values')
        if allowed_values and value not in allowed_values:
            return ValidationResult(
                is_valid=False,
                error_type="invalid_value",
                error_message=f"字段 {field_name} 值不在允许范围内: {allowed_values}",
                field_name=field_name,
                expected_value=allowed_values,
                actual_value=value
            )
        
        return ValidationResult(is_valid=True)
    
    def validate_record(self, record: Dict[str, Any]) -> List[ValidationResult]:
        """验证整条记录"""
        results = []
        
        for field_name, value in record.items():
            result = self.validate_field(field_name, value)
            if not result.is_valid:
                results.append(result)
        
        # 检查必填字段是否都存在
        for field_name, rule in self.field_rules.items():
            if rule.get('required', False) and field_name not in record:
                results.append(ValidationResult(
                    is_valid=False,
                    error_type="required_field_missing",
                    error_message=f"必填字段缺失: {field_name}",
                    field_name=field_name
                ))
        
        return results


class DataLogicValidator:
    """数据逻辑验证器"""
    
    def __init__(self):
        self.historical_data = {}  # 用于存储历史数据进行对比
    
    def validate_number_consistency(self, record: Dict[str, Any]) -> List[ValidationResult]:
        """验证号码一致性"""
        results = []
        
        # 检查开奖号码是否存在
        if not all(field in record for field in ['hundreds', 'tens', 'units']):
            results.append(ValidationResult(
                is_valid=False,
                error_type="missing_lottery_numbers",
                error_message="开奖号码不完整"
            ))
            return results
        
        numbers = [record['hundreds'], record['tens'], record['units']]
        
        # 验证和值计算
        if 'sum_value' in record:
            expected_sum = sum(numbers)
            actual_sum = record['sum_value']
            if expected_sum != actual_sum:
                results.append(ValidationResult(
                    is_valid=False,
                    error_type="sum_value_mismatch",
                    error_message="和值计算错误",
                    field_name="sum_value",
                    expected_value=expected_sum,
                    actual_value=actual_sum
                ))
        
        # 验证跨度计算
        if 'span' in record:
            expected_span = max(numbers) - min(numbers)
            actual_span = record['span']
            if expected_span != actual_span:
                results.append(ValidationResult(
                    is_valid=False,
                    error_type="span_mismatch",
                    error_message="跨度计算错误",
                    field_name="span",
                    expected_value=expected_span,
                    actual_value=actual_span
                ))
        
        # 验证号码类型
        if 'number_type' in record:
            expected_type = self._calculate_number_type(numbers)
            actual_type = record['number_type']
            if expected_type != actual_type:
                results.append(ValidationResult(
                    is_valid=False,
                    error_type="number_type_mismatch",
                    error_message="号码类型判断错误",
                    field_name="number_type",
                    expected_value=expected_type,
                    actual_value=actual_type
                ))
        
        return results
    
    def _calculate_number_type(self, numbers: List[int]) -> str:
        """计算号码类型"""
        unique_count = len(set(numbers))
        if unique_count == 1:
            return "豹子"
        elif unique_count == 2:
            return "对子"
        else:
            return "组六"
    
    def validate_date_sequence(self, record: Dict[str, Any]) -> List[ValidationResult]:
        """验证日期序列合理性"""
        results = []
        
        if 'draw_date' not in record or 'issue' not in record:
            return results
        
        try:
            draw_date = datetime.strptime(record['draw_date'], '%Y-%m-%d')
            issue = record['issue']
            
            # 检查日期是否在合理范围内
            min_date = datetime(2002, 1, 1)  # 福彩3D开始时间
            max_date = datetime.now() + timedelta(days=1)  # 允许明天的数据
            
            if draw_date < min_date:
                results.append(ValidationResult(
                    is_valid=False,
                    error_type="date_too_early",
                    error_message=f"开奖日期过早: {record['draw_date']}",
                    field_name="draw_date",
                    actual_value=record['draw_date']
                ))
            
            if draw_date > max_date:
                results.append(ValidationResult(
                    is_valid=False,
                    error_type="date_too_late",
                    error_message=f"开奖日期过晚: {record['draw_date']}",
                    field_name="draw_date",
                    actual_value=record['draw_date']
                ))
            
            # 检查期号与日期的对应关系
            year = draw_date.year
            expected_year_prefix = str(year)
            if not issue.startswith(expected_year_prefix):
                results.append(ValidationResult(
                    is_valid=False,
                    error_type="issue_date_mismatch",
                    error_message=f"期号与日期年份不匹配: {issue} vs {record['draw_date']}",
                    field_name="issue",
                    actual_value=issue
                ))
        
        except ValueError as e:
            results.append(ValidationResult(
                is_valid=False,
                error_type="date_format_error",
                error_message=f"日期格式错误: {str(e)}",
                field_name="draw_date",
                actual_value=record.get('draw_date')
            ))
        
        return results
    
    def validate_trial_numbers(self, record: Dict[str, Any]) -> List[ValidationResult]:
        """验证试机号码合理性"""
        results = []
        
        trial_fields = ['trial_hundreds', 'trial_tens', 'trial_units']
        trial_values = [record.get(field) for field in trial_fields]
        
        # 如果有试机号码，检查是否完整
        if any(val is not None for val in trial_values):
            if not all(val is not None for val in trial_values):
                results.append(ValidationResult(
                    is_valid=False,
                    error_type="incomplete_trial_numbers",
                    error_message="试机号码不完整",
                    field_name="trial_numbers"
                ))
        
        return results
    
    def validate_sales_amount(self, record: Dict[str, Any]) -> List[ValidationResult]:
        """验证销售额合理性"""
        results = []
        
        if 'sales_amount' in record and record['sales_amount'] is not None:
            sales = record['sales_amount']
            
            # 检查销售额是否在合理范围内
            min_sales = 100000  # 最小销售额10万
            max_sales = 50000000  # 最大销售额5000万
            
            if sales < min_sales:
                results.append(ValidationResult(
                    is_valid=False,
                    error_type="sales_too_low",
                    error_message=f"销售额过低: {sales}",
                    field_name="sales_amount",
                    actual_value=sales
                ))
            
            if sales > max_sales:
                results.append(ValidationResult(
                    is_valid=False,
                    error_type="sales_too_high",
                    error_message=f"销售额过高: {sales}",
                    field_name="sales_amount",
                    actual_value=sales
                ))
        
        return results
    
    def validate_record_logic(self, record: Dict[str, Any]) -> List[ValidationResult]:
        """验证记录逻辑一致性"""
        results = []
        
        # 验证号码一致性
        results.extend(self.validate_number_consistency(record))
        
        # 验证日期序列
        results.extend(self.validate_date_sequence(record))
        
        # 验证试机号码
        results.extend(self.validate_trial_numbers(record))
        
        # 验证销售额
        results.extend(self.validate_sales_amount(record))

        # 验证虚拟数据
        results.extend(self.validate_virtual_data(record))

        return results

    def validate_virtual_data(self, record: Dict[str, Any]) -> List[ValidationResult]:
        """检测虚拟数据"""
        results = []

        # 虚拟数据特征模式
        virtual_patterns = [
            r'^2024001$', r'^2024002$', r'^2024003$',  # 明显的虚拟期号
            r'^1999\d{3}$',  # 1999年的期号（福彩3D 2002年才开始）
            r'^2001\d{3}$',  # 2001年的期号
            r'^0000\d{3}$',  # 以0000开头的期号
            r'^9999\d{3}$',  # 以9999开头的期号
        ]

        # 虚拟数据关键词
        virtual_keywords = [
            'test', 'demo', 'sample', 'example', 'mock',
            '测试', '示例', '样例', '演示', '虚拟'
        ]

        issue = str(record.get('issue', ''))

        # 检查虚拟期号模式
        for pattern in virtual_patterns:
            if re.match(pattern, issue):
                results.append(ValidationResult(
                    is_valid=False,
                    error_type="virtual_data",
                    error_message=f"检测到虚拟数据模式: {pattern}",
                    field_name="issue",
                    actual_value=issue
                ))
                return results

        # 检查是否包含虚拟数据关键词
        record_str = str(record).lower()
        for keyword in virtual_keywords:
            if keyword in record_str:
                results.append(ValidationResult(
                    is_valid=False,
                    error_type="virtual_data",
                    error_message=f"检测到虚拟数据关键词: {keyword}",
                    field_name="record",
                    actual_value=record_str[:100]
                ))
                return results

        # 检查期号是否在合理范围内
        try:
            issue_num = int(issue)
            current_year = datetime.now().year

            # 期号年份部分
            year_part = issue_num // 1000

            # 如果年份不在合理范围内，可能是虚拟数据
            if year_part < 2002 or year_part > current_year + 1:
                results.append(ValidationResult(
                    is_valid=False,
                    error_type="virtual_data",
                    error_message=f"期号年份超出合理范围: {year_part}",
                    field_name="issue",
                    actual_value=issue
                ))
                return results

        except (ValueError, TypeError):
            results.append(ValidationResult(
                is_valid=False,
                error_type="virtual_data",
                error_message="期号格式无效，可能是虚拟数据",
                field_name="issue",
                actual_value=issue
            ))
            return results

        return results


class ComprehensiveDataValidator:
    """综合数据验证器"""
    
    def __init__(self):
        self.format_validator = DataFormatValidator()
        self.logic_validator = DataLogicValidator()
        self.validation_stats = {
            'total_records': 0,
            'valid_records': 0,
            'invalid_records': 0,
            'error_types': {}
        }
    
    def validate_single_record(self, record: Dict[str, Any]) -> Tuple[bool, List[ValidationResult]]:
        """验证单条记录"""
        all_results = []
        
        # 格式验证
        format_results = self.format_validator.validate_record(record)
        all_results.extend(format_results)
        
        # 逻辑验证
        logic_results = self.logic_validator.validate_record_logic(record)
        all_results.extend(logic_results)
        
        # 统计错误类型
        for result in all_results:
            if not result.is_valid:
                error_type = result.error_type
                self.validation_stats['error_types'][error_type] = \
                    self.validation_stats['error_types'].get(error_type, 0) + 1
        
        is_valid = len(all_results) == 0
        return is_valid, all_results
    
    def validate_batch_records(self, records: List[Dict[str, Any]]) -> Dict[str, Any]:
        """批量验证记录"""
        validation_report = {
            'total_records': len(records),
            'valid_records': 0,
            'invalid_records': 0,
            'validation_details': [],
            'error_summary': {},
            'validation_rate': 0.0
        }
        
        for i, record in enumerate(records):
            self.validation_stats['total_records'] += 1
            
            is_valid, errors = self.validate_single_record(record)
            
            if is_valid:
                validation_report['valid_records'] += 1
                self.validation_stats['valid_records'] += 1
            else:
                validation_report['invalid_records'] += 1
                self.validation_stats['invalid_records'] += 1
                
                # 记录验证详情
                validation_report['validation_details'].append({
                    'record_index': i,
                    'issue': record.get('issue', 'unknown'),
                    'errors': [
                        {
                            'error_type': err.error_type,
                            'error_message': err.error_message,
                            'field_name': err.field_name
                        } for err in errors
                    ]
                })
        
        # 计算验证通过率
        if validation_report['total_records'] > 0:
            validation_report['validation_rate'] = \
                validation_report['valid_records'] / validation_report['total_records']
        
        # 错误汇总
        validation_report['error_summary'] = self.validation_stats['error_types'].copy()
        
        return validation_report
    
    def get_validation_stats(self) -> Dict[str, Any]:
        """获取验证统计信息"""
        return self.validation_stats.copy()


if __name__ == "__main__":
    # 测试数据验证器
    print("=== 福彩3D数据验证器测试 ===")
    
    validator = ComprehensiveDataValidator()
    
    # 测试数据
    test_records = [
        {
            'issue': '2024001',
            'draw_date': '2024-01-01',
            'hundreds': 1,
            'tens': 2,
            'units': 3,
            'sum_value': 6,
            'span': 2,
            'number_type': '组六'
        },
        {
            'issue': '2024002',
            'draw_date': '2024-01-02',
            'hundreds': 5,
            'tens': 5,
            'units': 5,
            'sum_value': 15,
            'span': 0,
            'number_type': '豹子'
        },
        {
            'issue': 'invalid',  # 无效期号
            'draw_date': '2024-01-03',
            'hundreds': 10,  # 无效数字
            'tens': 1,
            'units': 2
        }
    ]
    
    # 批量验证
    report = validator.validate_batch_records(test_records)
    
    print(f"验证结果:")
    print(f"  总记录数: {report['total_records']}")
    print(f"  有效记录数: {report['valid_records']}")
    print(f"  无效记录数: {report['invalid_records']}")

    # 测试虚拟数据检测
    virtual_test_records = [
        {
            'issue': '2024001',  # 虚拟期号
            'draw_date': '2024-01-01',
            'hundreds': 1,
            'tens': 2,
            'units': 3
        },
        {
            'issue': '2025204',  # 真实期号
            'draw_date': '2025-07-23',
            'hundreds': 4,
            'tens': 9,
            'units': 7
        }
    ]

    print(f"\n虚拟数据检测测试:")
    for i, record in enumerate(virtual_test_records):
        validation_results = validator.validate_record_logic(record)
        virtual_detected = any(r.error_type == "virtual_data" for r in validation_results)
        print(f"  记录{i+1}: {'虚拟数据' if virtual_detected else '真实数据'} - 期号: {record['issue']}")


def check_virtual_data_patterns(record: Dict) -> bool:
        """检测虚拟数据模式"""
        # 虚拟数据特征模式
        virtual_patterns = [
            r'^2024001$', r'^2024002$', r'^2024003$',  # 明显的虚拟期号
            r'^1999\d{3}$',  # 1999年的期号（福彩3D 2002年才开始）
            r'^2001\d{3}$',  # 2001年的期号
            r'^0000\d{3}$',  # 以0000开头的期号
            r'^9999\d{3}$',  # 以9999开头的期号
        ]

        # 虚拟数据关键词
        virtual_keywords = [
            'test', 'demo', 'sample', 'example', 'mock',
            '测试', '示例', '样例', '演示', '虚拟'
        ]

        period = str(record.get('issue', ''))

        # 检查虚拟期号模式
        for pattern in virtual_patterns:
            if re.match(pattern, period):
                return True

        # 检查是否包含虚拟数据关键词
        record_str = str(record).lower()
        for keyword in virtual_keywords:
            if keyword in record_str:
                return True

        # 检查期号是否在合理范围内
        try:
            period_num = int(period)
            current_year = datetime.now().year

            # 期号年份部分
            year_part = period_num // 1000

            # 如果年份不在合理范围内，可能是虚拟数据
            if year_part < 2002 or year_part > current_year + 1:
                return True

        except (ValueError, TypeError):
            return True

        return False

    def validate_data_quality(self, records: List[Dict]) -> QualityReport:
        """验证数据质量，重点检查虚拟数据"""
        total_records = len(records)
        valid_records = 0
        virtual_data_count = 0
        virtual_data_found = []
        duplicate_count = 0
        seen_periods = set()

        for i, record in enumerate(records):
            # 基本格式验证
            if self.validate_record(record).is_valid:
                valid_records += 1

            # 虚拟数据检测
            if self.check_virtual_data_patterns(record):
                virtual_data_count += 1
                virtual_data_found.append({
                    'index': i,
                    'period': record.get('issue', 'unknown'),
                    'reason': 'Virtual data pattern detected'
                })

            # 重复检测
            period = record.get('issue')
            if period in seen_periods:
                duplicate_count += 1
            else:
                seen_periods.add(period)

        # 计算质量分数
        quality_score = 0.0
        if total_records > 0:
            valid_ratio = valid_records / total_records
            virtual_penalty = virtual_data_count / total_records * 0.5
            duplicate_penalty = duplicate_count / total_records * 0.2
            quality_score = max(0.0, valid_ratio - virtual_penalty - duplicate_penalty)

        # 生成建议
        recommendations = []
        if virtual_data_count == 0:
            recommendations.append("✅ 未发现虚拟数据，数据质量良好")
        else:
            recommendations.append(f"❌ 发现 {virtual_data_count} 条虚拟数据，需要立即清除")
            recommendations.append("🔧 建议重新采集真实数据")

        if duplicate_count > 0:
            recommendations.append(f"⚠️ 发现 {duplicate_count} 条重复数据，建议去重")

        validation_passed = virtual_data_count == 0 and quality_score >= 0.95

        return QualityReport(
            total_records=total_records,
            valid_records=valid_records,
            virtual_data_count=virtual_data_count,
            duplicate_count=duplicate_count,
            quality_score=quality_score,
            validation_passed=validation_passed,
            virtual_data_found=virtual_data_found,
            recommendations=recommendations
        )
