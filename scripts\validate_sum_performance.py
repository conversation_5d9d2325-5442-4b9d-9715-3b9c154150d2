#!/usr/bin/env python3
"""
P6和值预测器性能验证脚本

验证模型性能指标（MAE、RMSE、准确率）、约束一致性、与P3-P5协同工作
提供详细的性能报告和基准测试

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import sys
import os
import argparse
import logging
import json
import numpy as np
import pandas as pd
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.predictors.sum_predictor import SumPredictor

def setup_logging(log_level: str = "INFO"):
    """设置日志配置"""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[logging.StreamHandler(sys.stdout)]
    )

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="P6和值预测器性能验证脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument(
        "--db-path", "-d",
        type=str,
        default="data/lottery.db",
        help="数据库文件路径"
    )
    
    parser.add_argument(
        "--config", "-c",
        type=str,
        default="config/sum_predictor_config.yaml",
        help="配置文件路径"
    )
    
    parser.add_argument(
        "--model-path",
        type=str,
        default=None,
        help="预训练模型路径"
    )
    
    parser.add_argument(
        "--test-size",
        type=float,
        default=0.2,
        help="测试集比例"
    )
    
    parser.add_argument(
        "--performance-thresholds",
        type=str,
        default='{"mae": 1.5, "rmse": 2.0, "accuracy_1": 0.6, "accuracy_2": 0.85}',
        help="性能阈值JSON字符串"
    )
    
    parser.add_argument(
        "--validate-constraints",
        action="store_true",
        help="验证约束一致性"
    )
    
    parser.add_argument(
        "--benchmark-mode",
        action="store_true",
        help="基准测试模式"
    )
    
    parser.add_argument(
        "--output-report",
        type=str,
        default="reports/sum_performance_validation.json",
        help="输出报告路径"
    )
    
    parser.add_argument(
        "--log-level",
        type=str,
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="日志级别"
    )
    
    return parser.parse_args()

def validate_model_performance(predictor: SumPredictor, X_test: np.ndarray, y_test: np.ndarray,
                              thresholds: Dict[str, float]) -> Dict[str, Any]:
    """验证模型性能"""
    logger = logging.getLogger("validate_performance")
    
    validation_results = {}
    
    # 测试所有可用模型
    for model_name in predictor.get_available_models():
        logger.info(f"验证模型: {model_name}")
        
        try:
            # 切换到当前模型
            predictor.switch_model(model_name)
            
            # 如果模型未训练，进行快速训练
            if not predictor.models[model_name].is_trained:
                logger.info(f"模型 {model_name} 未训练，进行快速训练...")
                if len(X_test) > 20:
                    X_train_small = X_test[:20]
                    y_train_small = y_test[:20]
                    predictor.models[model_name].train(X_train_small, y_train_small)
            
            # 评估性能
            performance = predictor.evaluate(X_test, y_test, model_name)
            
            # 验证性能是否达标
            validation_status = {}
            for metric, threshold in thresholds.items():
                if metric in performance:
                    actual_value = performance[metric]
                    if metric in ['mae', 'rmse']:
                        # 越小越好
                        passed = actual_value <= threshold
                    else:
                        # 越大越好
                        passed = actual_value >= threshold
                    
                    validation_status[metric] = {
                        'actual': actual_value,
                        'threshold': threshold,
                        'passed': passed
                    }
            
            # 计算总体通过率
            passed_count = sum(1 for v in validation_status.values() if v['passed'])
            total_count = len(validation_status)
            overall_pass_rate = passed_count / total_count if total_count > 0 else 0
            
            validation_results[model_name] = {
                'performance': performance,
                'validation_status': validation_status,
                'overall_pass_rate': overall_pass_rate,
                'status': 'PASS' if overall_pass_rate >= 0.7 else 'FAIL'
            }
            
            logger.info(f"模型 {model_name} 验证完成，通过率: {overall_pass_rate:.2%}")
            
        except Exception as e:
            logger.error(f"模型 {model_name} 验证失败: {e}")
            validation_results[model_name] = {
                'error': str(e),
                'status': 'ERROR'
            }
    
    return validation_results

def validate_constraint_consistency(predictor: SumPredictor, X_test: np.ndarray, y_test: np.ndarray) -> Dict[str, Any]:
    """验证约束一致性"""
    logger = logging.getLogger("validate_constraints")
    
    try:
        logger.info("验证约束一致性...")
        
        # 生成模拟位置预测
        position_predictions = {
            'hundreds_pred': np.random.randint(0, 10, len(X_test)),
            'tens_pred': np.random.randint(0, 10, len(X_test)),
            'units_pred': np.random.randint(0, 10, len(X_test)),
            'hundreds_prob': np.random.dirichlet(np.ones(10), len(X_test)),
            'tens_prob': np.random.dirichlet(np.ones(10), len(X_test)),
            'units_prob': np.random.dirichlet(np.ones(10), len(X_test))
        }
        
        # 评估约束一致性
        constraint_results = predictor.evaluate_constraint_consistency(
            X_test, y_test, position_predictions
        )
        
        # 验证约束一致性指标
        constraint_thresholds = {
            'avg_constraint_score': 0.7,
            'constraint_consistency_rate': 0.6,
            'improvement_over_base': 0.0  # 至少不能变差
        }
        
        validation_status = {}
        for metric, threshold in constraint_thresholds.items():
            if metric in constraint_results:
                actual_value = constraint_results[metric]
                passed = actual_value >= threshold
                
                validation_status[metric] = {
                    'actual': actual_value,
                    'threshold': threshold,
                    'passed': passed
                }
        
        # 计算约束验证通过率
        passed_count = sum(1 for v in validation_status.values() if v['passed'])
        total_count = len(validation_status)
        constraint_pass_rate = passed_count / total_count if total_count > 0 else 0
        
        return {
            'constraint_results': constraint_results,
            'validation_status': validation_status,
            'constraint_pass_rate': constraint_pass_rate,
            'status': 'PASS' if constraint_pass_rate >= 0.7 else 'FAIL'
        }
        
    except Exception as e:
        logger.error(f"约束一致性验证失败: {e}")
        return {
            'error': str(e),
            'status': 'ERROR'
        }

def run_benchmark_tests(predictor: SumPredictor, X_test: np.ndarray, y_test: np.ndarray) -> Dict[str, Any]:
    """运行基准测试"""
    logger = logging.getLogger("benchmark")
    
    benchmark_results = {}
    
    # 测试预测速度
    logger.info("测试预测速度...")
    for model_name in ['xgb', 'lgb', 'ensemble']:
        if model_name in predictor.models:
            try:
                predictor.switch_model(model_name)
                
                # 如果模型未训练，跳过
                if not predictor.models[model_name].is_trained:
                    continue
                
                # 测试单次预测时间
                import time
                start_time = time.time()
                
                test_sample = X_test[:1]
                for _ in range(10):  # 预测10次取平均
                    predictor.predict(test_sample)
                
                avg_prediction_time = (time.time() - start_time) / 10
                
                # 测试批量预测时间
                start_time = time.time()
                predictor.predict(X_test[:10])
                batch_prediction_time = time.time() - start_time
                
                benchmark_results[model_name] = {
                    'avg_prediction_time': avg_prediction_time,
                    'batch_prediction_time': batch_prediction_time,
                    'predictions_per_second': 1.0 / avg_prediction_time if avg_prediction_time > 0 else 0
                }
                
                logger.info(f"{model_name} 预测速度: {1.0/avg_prediction_time:.1f} 次/秒")
                
            except Exception as e:
                logger.warning(f"模型 {model_name} 基准测试失败: {e}")
                benchmark_results[model_name] = {'error': str(e)}
    
    # 测试内存使用
    logger.info("测试内存使用...")
    try:
        import psutil
        process = psutil.Process()
        memory_info = process.memory_info()
        
        benchmark_results['memory_usage'] = {
            'rss_mb': memory_info.rss / 1024 / 1024,
            'vms_mb': memory_info.vms / 1024 / 1024
        }
        
    except ImportError:
        logger.warning("psutil未安装，跳过内存测试")
    
    return benchmark_results

def generate_validation_report(performance_results: Dict, constraint_results: Dict,
                             benchmark_results: Dict, args) -> Dict[str, Any]:
    """生成验证报告"""
    
    # 计算总体通过率
    model_pass_count = sum(1 for result in performance_results.values() 
                          if result.get('status') == 'PASS')
    total_models = len([r for r in performance_results.values() if 'status' in r])
    
    overall_pass_rate = model_pass_count / total_models if total_models > 0 else 0
    
    # 生成报告
    report = {
        'validation_summary': {
            'timestamp': datetime.now().isoformat(),
            'total_models_tested': total_models,
            'models_passed': model_pass_count,
            'overall_pass_rate': overall_pass_rate,
            'validation_status': 'PASS' if overall_pass_rate >= 0.7 else 'FAIL'
        },
        'performance_validation': performance_results,
        'constraint_validation': constraint_results,
        'benchmark_results': benchmark_results,
        'test_configuration': {
            'db_path': args.db_path,
            'config_path': args.config,
            'test_size': args.test_size,
            'performance_thresholds': json.loads(args.performance_thresholds),
            'validate_constraints': args.validate_constraints,
            'benchmark_mode': args.benchmark_mode
        }
    }
    
    return report

def print_validation_summary(report: Dict[str, Any]):
    """打印验证摘要"""
    print("\n" + "="*80)
    print("P6和值预测器性能验证报告")
    print("="*80)
    
    summary = report['validation_summary']
    print(f"验证时间: {summary['timestamp']}")
    print(f"测试模型数: {summary['total_models_tested']}")
    print(f"通过模型数: {summary['models_passed']}")
    print(f"总体通过率: {summary['overall_pass_rate']:.2%}")
    print(f"验证状态: {summary['validation_status']}")
    
    # 性能验证详情
    print("\n" + "-"*50)
    print("模型性能验证详情")
    print("-"*50)
    
    for model_name, result in report['performance_validation'].items():
        if 'status' in result:
            print(f"\n{model_name.upper()} 模型: {result['status']}")
            if 'overall_pass_rate' in result:
                print(f"  通过率: {result['overall_pass_rate']:.2%}")
            
            if 'validation_status' in result:
                for metric, status in result['validation_status'].items():
                    symbol = "✓" if status['passed'] else "✗"
                    print(f"  {symbol} {metric}: {status['actual']:.4f} (阈值: {status['threshold']})")
    
    # 约束验证详情
    if 'constraint_validation' in report and report['constraint_validation']:
        constraint_result = report['constraint_validation']
        print(f"\n约束一致性验证: {constraint_result.get('status', 'N/A')}")
        if 'constraint_pass_rate' in constraint_result:
            print(f"约束通过率: {constraint_result['constraint_pass_rate']:.2%}")
    
    # 基准测试结果
    if 'benchmark_results' in report and report['benchmark_results']:
        print(f"\n基准测试结果:")
        for model_name, bench_result in report['benchmark_results'].items():
            if 'predictions_per_second' in bench_result:
                print(f"  {model_name}: {bench_result['predictions_per_second']:.1f} 预测/秒")

def main():
    """主函数"""
    args = parse_arguments()
    
    # 设置日志
    setup_logging(args.log_level)
    logger = logging.getLogger("main")
    
    try:
        logger.info("开始P6和值预测器性能验证")
        
        # 检查数据库文件
        if not Path(args.db_path).exists():
            logger.error(f"数据库文件不存在: {args.db_path}")
            return 1
        
        # 解析性能阈值
        try:
            thresholds = json.loads(args.performance_thresholds)
        except json.JSONDecodeError:
            logger.error("性能阈值JSON格式错误")
            return 1
        
        # 初始化预测器
        logger.info("初始化和值预测器")
        predictor = SumPredictor(args.db_path, args.config)
        
        # 加载模型
        if args.model_path:
            if not predictor.load_model(args.model_path):
                logger.error(f"无法加载模型: {args.model_path}")
                return 1
        else:
            predictor.build_model()
        
        # 准备测试数据
        logger.info("准备测试数据")
        X, y = predictor.models['xgb'].load_data()
        
        if len(X) < 20:
            logger.error("测试数据不足")
            return 1
        
        # 分割测试数据
        test_size = int(len(X) * args.test_size)
        X_test = X[-test_size:]
        y_test = y[-test_size:]
        
        logger.info(f"使用 {len(X_test)} 个样本进行验证")
        
        # 1. 验证模型性能
        logger.info("验证模型性能...")
        performance_results = validate_model_performance(predictor, X_test, y_test, thresholds)
        
        # 2. 验证约束一致性
        constraint_results = {}
        if args.validate_constraints:
            logger.info("验证约束一致性...")
            constraint_results = validate_constraint_consistency(predictor, X_test, y_test)
        
        # 3. 运行基准测试
        benchmark_results = {}
        if args.benchmark_mode:
            logger.info("运行基准测试...")
            benchmark_results = run_benchmark_tests(predictor, X_test, y_test)
        
        # 4. 生成验证报告
        logger.info("生成验证报告...")
        report = generate_validation_report(
            performance_results, constraint_results, benchmark_results, args
        )
        
        # 5. 保存报告
        output_path = Path(args.output_report)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)
        
        logger.info(f"验证报告已保存到: {output_path}")
        
        # 6. 打印摘要
        print_validation_summary(report)
        
        # 7. 返回状态码
        overall_status = report['validation_summary']['validation_status']
        return 0 if overall_status == 'PASS' else 1
        
    except Exception as e:
        logger.error(f"性能验证过程发生错误: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
