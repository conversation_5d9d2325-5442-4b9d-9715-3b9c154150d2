"""
个位专用特征生成器
为个位预测器提供50+维专用特征，包括：
- 频次特征：出现频率、遗漏次数、连续性分析
- 冷热度特征：冷热状态、温度变化、周期性分析
- 趋势特征：上升下降趋势、变化幅度、稳定性
- 统计特征：均值、方差、偏度、峰度
- 尾数特征：奇偶性、大小数、升降序列
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional
from collections import Counter, deque


def generate_units_features(df: pd.DataFrame, window_sizes: List[int] = [5, 10, 20, 50]) -> pd.DataFrame:
    """
    生成个位专用特征
    
    Args:
        df: 包含历史数据的DataFrame，必须包含'units'列
        window_sizes: 滑动窗口大小列表
        
    Returns:
        pd.DataFrame: 包含个位专用特征的DataFrame
    """
    if 'units' not in df.columns:
        raise ValueError("DataFrame必须包含'units'列")
    
    result_df = df.copy()
    units_series = df['units']
    
    # 1. 基础频次特征
    result_df.update(_generate_units_frequency_features(units_series, window_sizes))
    
    # 2. 遗漏分析特征
    result_df.update(_generate_units_missing_features(units_series, window_sizes))
    
    # 3. 冷热度特征
    result_df.update(_generate_units_hot_cold_features(units_series, window_sizes))
    
    # 4. 趋势分析特征
    result_df.update(_generate_units_trend_features(units_series, window_sizes))
    
    # 5. 统计分布特征
    result_df.update(_generate_units_statistical_features(units_series, window_sizes))
    
    # 6. 尾数特性特征
    result_df.update(_generate_units_tail_features(units_series, window_sizes))
    
    # 7. 升降序列特征
    result_df.update(_generate_units_sequence_features(units_series, window_sizes))
    
    # 8. 关联性特征（如果存在百位和十位数据）
    if 'hundreds' in df.columns and 'tens' in df.columns:
        result_df.update(_generate_units_correlation_features(df[['hundreds', 'tens', 'units']], window_sizes))
    
    return result_df


def _generate_units_frequency_features(series: pd.Series, window_sizes: List[int]) -> pd.DataFrame:
    """生成个位频次特征"""
    features = pd.DataFrame(index=series.index)
    
    for window in window_sizes:
        # 滑动窗口频次统计
        freq_counts = series.rolling(window=window, min_periods=1).apply(
            lambda x: Counter(x)[x.iloc[-1]] if len(x) > 0 else 0
        )
        features[f'units_freq_{window}'] = freq_counts
        
        # 频次排名
        features[f'units_freq_rank_{window}'] = freq_counts.rolling(window=window).rank(pct=True)
        
        # 频次变化率
        features[f'units_freq_change_{window}'] = freq_counts.pct_change()
        
        # 重复出现模式
        features[f'units_repeat_pattern_{window}'] = _calculate_repeat_pattern(series, window)
    
    # 全局频次特征
    global_counts = series.value_counts()
    features['units_global_freq'] = series.map(global_counts)
    features['units_global_freq_rank'] = features['units_global_freq'].rank(pct=True)
    
    return features


def _generate_units_missing_features(series: pd.Series, window_sizes: List[int]) -> pd.DataFrame:
    """生成个位遗漏分析特征"""
    features = pd.DataFrame(index=series.index)
    
    # 计算每个数字的遗漏次数
    for digit in range(10):
        missing_counts = []
        current_missing = 0
        
        for value in series:
            if value == digit:
                missing_counts.append(current_missing)
                current_missing = 0
            else:
                current_missing += 1
                missing_counts.append(current_missing)
        
        features[f'units_missing_{digit}'] = missing_counts
    
    # 当前数字的遗漏特征
    current_missing = []
    for i, value in enumerate(series):
        if i == 0:
            current_missing.append(0)
        else:
            missing = 0
            for j in range(i-1, -1, -1):
                if series.iloc[j] == value:
                    break
                missing += 1
            current_missing.append(missing)
    
    features['units_current_missing'] = current_missing
    
    # 遗漏统计特征
    for window in window_sizes:
        features[f'units_avg_missing_{window}'] = features['units_current_missing'].rolling(window).mean()
        features[f'units_max_missing_{window}'] = features['units_current_missing'].rolling(window).max()
        features[f'units_missing_variance_{window}'] = features['units_current_missing'].rolling(window).var()
        
        # 遗漏周期性
        features[f'units_missing_cycle_{window}'] = _calculate_missing_cycle(series, window)
    
    return features


def _generate_units_hot_cold_features(series: pd.Series, window_sizes: List[int]) -> pd.DataFrame:
    """生成个位冷热度特征"""
    features = pd.DataFrame(index=series.index)
    
    for window in window_sizes:
        # 计算窗口内的出现频次
        window_freq = series.rolling(window=window, min_periods=1).apply(
            lambda x: Counter(x)[x.iloc[-1]] if len(x) > 0 else 0
        )
        
        # 计算期望频次
        expected_freq = window / 10
        
        # 冷热度评分
        features[f'units_hot_cold_score_{window}'] = (window_freq - expected_freq) / expected_freq
        
        # 冷热度分类
        def classify_units_hot_cold(score):
            if score > 0.6:
                return 'very_hot'
            elif score > 0.2:
                return 'hot'
            elif score < -0.6:
                return 'very_cold'
            elif score < -0.2:
                return 'cold'
            else:
                return 'normal'
        
        features[f'units_hot_cold_{window}'] = features[f'units_hot_cold_score_{window}'].apply(classify_units_hot_cold)
        
        # 温度持续性
        features[f'units_temp_persistence_{window}'] = _calculate_temperature_persistence(
            features[f'units_hot_cold_score_{window}'], window
        )
    
    return features


def _generate_units_trend_features(series: pd.Series, window_sizes: List[int]) -> pd.DataFrame:
    """生成个位趋势分析特征"""
    features = pd.DataFrame(index=series.index)
    
    for window in window_sizes:
        # 数值趋势
        features[f'units_trend_{window}'] = series.rolling(window=window).apply(
            lambda x: np.polyfit(range(len(x)), x, 1)[0] if len(x) > 1 else 0
        )
        
        # 变化幅度
        features[f'units_volatility_{window}'] = series.rolling(window=window).std()
        
        # 上升下降趋势
        features[f'units_increasing_{window}'] = (series > series.shift(1)).rolling(window=window).sum()
        features[f'units_decreasing_{window}'] = (series < series.shift(1)).rolling(window=window).sum()
        features[f'units_stable_{window}'] = (series == series.shift(1)).rolling(window=window).sum()
        
        # 趋势转折点
        features[f'units_turning_points_{window}'] = _calculate_turning_points(series, window)
        
        # 趋势强度
        features[f'units_trend_strength_{window}'] = np.abs(features[f'units_trend_{window}'])
    
    return features


def _generate_units_statistical_features(series: pd.Series, window_sizes: List[int]) -> pd.DataFrame:
    """生成个位统计分布特征"""
    features = pd.DataFrame(index=series.index)
    
    for window in window_sizes:
        # 基础统计量
        features[f'units_mean_{window}'] = series.rolling(window=window).mean()
        features[f'units_std_{window}'] = series.rolling(window=window).std()
        features[f'units_range_{window}'] = series.rolling(window=window).max() - series.rolling(window=window).min()
        
        # 分位数特征
        features[f'units_q25_{window}'] = series.rolling(window=window).quantile(0.25)
        features[f'units_q75_{window}'] = series.rolling(window=window).quantile(0.75)
        features[f'units_iqr_{window}'] = features[f'units_q75_{window}'] - features[f'units_q25_{window}']
        
        # 分布形状
        features[f'units_skew_{window}'] = series.rolling(window=window).skew()
        features[f'units_kurt_{window}'] = series.rolling(window=window).kurt()
        
        # 变异系数
        features[f'units_cv_{window}'] = features[f'units_std_{window}'] / (features[f'units_mean_{window}'] + 1e-8)
    
    return features


def _generate_units_tail_features(series: pd.Series, window_sizes: List[int]) -> pd.DataFrame:
    """生成个位尾数特性特征"""
    features = pd.DataFrame(index=series.index)
    
    # 奇偶性特征
    features['units_is_odd'] = (series % 2).astype(int)
    features['units_is_even'] = ((series + 1) % 2).astype(int)
    
    # 大小数特征
    features['units_is_big'] = (series >= 5).astype(int)
    features['units_is_small'] = (series < 5).astype(int)
    
    # 质数特征
    primes = {2, 3, 5, 7}
    features['units_is_prime'] = series.apply(lambda x: 1 if x in primes else 0)
    
    for window in window_sizes:
        # 奇偶性统计
        features[f'units_odd_ratio_{window}'] = features['units_is_odd'].rolling(window=window).mean()
        features[f'units_even_ratio_{window}'] = features['units_is_even'].rolling(window=window).mean()
        
        # 大小数统计
        features[f'units_big_ratio_{window}'] = features['units_is_big'].rolling(window=window).mean()
        features[f'units_small_ratio_{window}'] = features['units_is_small'].rolling(window=window).mean()
        
        # 质数统计
        features[f'units_prime_ratio_{window}'] = features['units_is_prime'].rolling(window=window).mean()
        
        # 尾数分布均匀性
        features[f'units_distribution_entropy_{window}'] = _calculate_distribution_entropy(series, window)
    
    return features


def _generate_units_sequence_features(series: pd.Series, window_sizes: List[int]) -> pd.DataFrame:
    """生成个位升降序列特征"""
    features = pd.DataFrame(index=series.index)
    
    # 升降序列基础特征
    features['units_is_ascending'] = (series > series.shift(1)).astype(int)
    features['units_is_descending'] = (series < series.shift(1)).astype(int)
    features['units_is_equal'] = (series == series.shift(1)).astype(int)
    
    for window in window_sizes:
        # 升降序列统计
        features[f'units_ascending_ratio_{window}'] = features['units_is_ascending'].rolling(window=window).mean()
        features[f'units_descending_ratio_{window}'] = features['units_is_descending'].rolling(window=window).mean()
        features[f'units_equal_ratio_{window}'] = features['units_is_equal'].rolling(window=window).mean()
        
        # 连续升降序列长度
        features[f'units_max_ascending_{window}'] = _calculate_max_consecutive_sequence(
            features['units_is_ascending'], window
        )
        features[f'units_max_descending_{window}'] = _calculate_max_consecutive_sequence(
            features['units_is_descending'], window
        )
        
        # 序列变化强度
        features[f'units_sequence_intensity_{window}'] = _calculate_sequence_intensity(series, window)
    
    return features


def _generate_units_correlation_features(df: pd.DataFrame, window_sizes: List[int]) -> pd.DataFrame:
    """生成个位关联性特征"""
    features = pd.DataFrame(index=df.index)
    
    hundreds = df['hundreds']
    tens = df['tens']
    units = df['units']
    
    for window in window_sizes:
        # 与百位的相关性
        features[f'units_hundreds_corr_{window}'] = units.rolling(window=window).corr(hundreds)
        
        # 与十位的相关性
        features[f'units_tens_corr_{window}'] = units.rolling(window=window).corr(tens)
        
        # 尾数效应
        features[f'units_tail_effect_{window}'] = _calculate_tail_effect(hundreds, tens, units, window)
        
        # 数字间的差值特征
        features[f'units_hundreds_diff_{window}'] = (units - hundreds).rolling(window=window).mean()
        features[f'units_tens_diff_{window}'] = (units - tens).rolling(window=window).mean()
        
        # 个位的独立性
        features[f'units_independence_{window}'] = _calculate_independence(hundreds, tens, units, window)
        
        # 完整性指标
        features[f'units_completeness_{window}'] = _calculate_completeness(hundreds, tens, units, window)
    
    return features


# 辅助函数
def _calculate_repeat_pattern(series: pd.Series, window: int) -> pd.Series:
    """计算重复出现模式"""
    patterns = []
    for i in range(len(series)):
        if i < window:
            patterns.append(0)
        else:
            window_data = series.iloc[i-window+1:i+1]
            # 计算重复模式的复杂度
            unique_count = len(window_data.unique())
            pattern_score = unique_count / window
            patterns.append(pattern_score)
    return pd.Series(patterns, index=series.index)


def _calculate_missing_cycle(series: pd.Series, window: int) -> pd.Series:
    """计算遗漏周期性"""
    cycles = []
    for i in range(len(series)):
        if i < window:
            cycles.append(0)
        else:
            # 简化的周期性计算
            window_data = series.iloc[i-window+1:i+1]
            cycle_score = len(window_data.unique()) / 10.0
            cycles.append(cycle_score)
    return pd.Series(cycles, index=series.index)


def _calculate_temperature_persistence(temp_scores: pd.Series, window: int) -> pd.Series:
    """计算温度持续性"""
    persistence = []
    for i in range(len(temp_scores)):
        if i < window:
            persistence.append(0)
        else:
            window_temps = temp_scores.iloc[i-window+1:i+1]
            # 计算温度状态的持续性
            positive_count = (window_temps > 0).sum()
            negative_count = (window_temps < 0).sum()
            persist_score = max(positive_count, negative_count) / window
            persistence.append(persist_score)
    return pd.Series(persistence, index=temp_scores.index)


def _calculate_turning_points(series: pd.Series, window: int) -> pd.Series:
    """计算趋势转折点"""
    turning_points = []
    for i in range(len(series)):
        if i < 2:
            turning_points.append(0)
        else:
            # 检测转折点
            if ((series.iloc[i-1] > series.iloc[i-2] and series.iloc[i] < series.iloc[i-1]) or
                (series.iloc[i-1] < series.iloc[i-2] and series.iloc[i] > series.iloc[i-1])):
                turning_points.append(1)
            else:
                turning_points.append(0)
    
    # 计算窗口内的转折点数量
    tp_series = pd.Series(turning_points, index=series.index)
    return tp_series.rolling(window=window).sum()


def _calculate_distribution_entropy(series: pd.Series, window: int) -> pd.Series:
    """计算分布熵"""
    entropies = []
    for i in range(len(series)):
        if i < window:
            entropies.append(0)
        else:
            window_data = series.iloc[i-window+1:i+1]
            value_counts = window_data.value_counts()
            probabilities = value_counts / len(window_data)
            entropy = -np.sum(probabilities * np.log2(probabilities + 1e-8))
            entropies.append(entropy)
    return pd.Series(entropies, index=series.index)


def _calculate_max_consecutive_sequence(binary_series: pd.Series, window: int) -> pd.Series:
    """计算最大连续序列长度"""
    max_consecutive = []
    for i in range(len(binary_series)):
        if i < window:
            max_consecutive.append(0)
        else:
            window_data = binary_series.iloc[i-window+1:i+1]
            max_seq = 0
            current_seq = 0
            for val in window_data:
                if val == 1:
                    current_seq += 1
                    max_seq = max(max_seq, current_seq)
                else:
                    current_seq = 0
            max_consecutive.append(max_seq)
    return pd.Series(max_consecutive, index=binary_series.index)


def _calculate_sequence_intensity(series: pd.Series, window: int) -> pd.Series:
    """计算序列变化强度"""
    intensities = []
    for i in range(len(series)):
        if i < window:
            intensities.append(0)
        else:
            window_data = series.iloc[i-window+1:i+1]
            changes = np.abs(window_data.diff().dropna())
            intensity = changes.mean() if len(changes) > 0 else 0
            intensities.append(intensity)
    return pd.Series(intensities, index=series.index)


def _calculate_tail_effect(hundreds: pd.Series, tens: pd.Series, units: pd.Series, window: int) -> pd.Series:
    """计算尾数效应"""
    tail_effects = []
    for i in range(len(units)):
        if i < window:
            tail_effects.append(0)
        else:
            # 计算个位对整体数字的影响
            window_units = units.iloc[i-window+1:i+1]
            unit_variance = window_units.var()
            tail_effect = unit_variance / (unit_variance + 1)
            tail_effects.append(tail_effect)
    return pd.Series(tail_effects, index=units.index)


def _calculate_independence(hundreds: pd.Series, tens: pd.Series, units: pd.Series, window: int) -> pd.Series:
    """计算个位的独立性"""
    independence_scores = []
    for i in range(len(units)):
        if i < window:
            independence_scores.append(0.5)
        else:
            # 计算个位与前两位的独立性
            h_corr = units.iloc[i-window+1:i+1].corr(hundreds.iloc[i-window+1:i+1])
            t_corr = units.iloc[i-window+1:i+1].corr(tens.iloc[i-window+1:i+1])
            
            # 独立性：相关性越低越独立
            independence = 1 - (abs(h_corr) + abs(t_corr)) / 2 if not (pd.isna(h_corr) or pd.isna(t_corr)) else 0.5
            independence_scores.append(independence)
    
    return pd.Series(independence_scores, index=units.index)


def _calculate_completeness(hundreds: pd.Series, tens: pd.Series, units: pd.Series, window: int) -> pd.Series:
    """计算完整性指标"""
    completeness_scores = []
    for i in range(len(units)):
        if i < window:
            completeness_scores.append(0)
        else:
            # 计算三位数字组合的完整性
            combinations = hundreds.iloc[i-window+1:i+1] * 100 + tens.iloc[i-window+1:i+1] * 10 + units.iloc[i-window+1:i+1]
            unique_combinations = len(combinations.unique())
            completeness = unique_combinations / window
            completeness_scores.append(completeness)
    
    return pd.Series(completeness_scores, index=units.index)
