#!/usr/bin/env python3
"""
P9优化任务队列管理器

该模块负责管理P9闭环优化系统的任务队列，包括：
1. 任务的创建、调度和执行
2. 任务优先级和依赖关系管理
3. 任务状态跟踪和错误处理
4. 任务重试和超时处理

作者: Augment Code AI Assistant
创建日期: 2025-01-14
版本: 1.0.0
"""

import sqlite3
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum
import threading
import time
from queue import PriorityQueue

from .intelligent_closed_loop_optimizer import OptimizationTask, OptimizationTaskType, OptimizationStatus

class TaskPriority(Enum):
    """任务优先级枚举"""
    CRITICAL = 10
    HIGH = 8
    MEDIUM = 5
    LOW = 3
    BACKGROUND = 1

@dataclass
class TaskQueueItem:
    """任务队列项"""
    priority: int
    task_id: int
    task: OptimizationTask
    
    def __lt__(self, other):
        # 优先级高的任务排在前面（数值越大优先级越高）
        return self.priority > other.priority

class OptimizationTaskQueue:
    """优化任务队列管理器"""
    
    def __init__(self, db_path: str):
        """
        初始化任务队列管理器
        
        Args:
            db_path: 数据库路径
        """
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        
        # 内存中的任务队列
        self.task_queue = PriorityQueue()
        self.running_tasks = {}  # task_id -> task_info
        self.completed_tasks = {}  # task_id -> result
        
        # 任务优先级映射
        self.task_priorities = {
            OptimizationTaskType.DATA_UPDATE: TaskPriority.HIGH.value,
            OptimizationTaskType.PERFORMANCE_EVAL: TaskPriority.HIGH.value,
            OptimizationTaskType.FUSION_WEIGHT_ADJUST: TaskPriority.MEDIUM.value,
            OptimizationTaskType.PARAMETER_TUNE: TaskPriority.MEDIUM.value,
            OptimizationTaskType.MODEL_RETRAIN: TaskPriority.LOW.value,
            OptimizationTaskType.SYSTEM_MAINTENANCE: TaskPriority.BACKGROUND.value
        }
        
        # 任务依赖关系
        self.task_dependencies = {
            OptimizationTaskType.MODEL_RETRAIN: [OptimizationTaskType.DATA_UPDATE],
            OptimizationTaskType.PARAMETER_TUNE: [OptimizationTaskType.PERFORMANCE_EVAL],
            OptimizationTaskType.FUSION_WEIGHT_ADJUST: [OptimizationTaskType.PERFORMANCE_EVAL]
        }
        
        # 线程锁
        self.lock = threading.Lock()
        
        # 初始化数据库表
        self._ensure_task_queue_table()
        
        # 从数据库恢复未完成的任务
        self._restore_pending_tasks()
        
        self.logger.info("任务队列管理器初始化完成")
    
    def _ensure_task_queue_table(self):
        """确保任务队列表存在"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 检查表是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='optimization_task_queue'")
            if not cursor.fetchone():
                self.logger.warning("optimization_task_queue表不存在，将创建基本表结构")
                
                # 创建基本表结构
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS optimization_task_queue (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        task_type TEXT NOT NULL,
                        component_name TEXT,
                        priority INTEGER DEFAULT 5,
                        scheduled_time TIMESTAMP,
                        dependencies TEXT,
                        max_retries INTEGER DEFAULT 3,
                        retry_count INTEGER DEFAULT 0,
                        status TEXT NOT NULL DEFAULT 'pending',
                        error_message TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        started_at TIMESTAMP,
                        completed_at TIMESTAMP
                    )
                """)
                
                conn.commit()
                self.logger.info("optimization_task_queue表创建成功")
            
            conn.close()
            
        except Exception as e:
            self.logger.error(f"确保任务队列表失败: {e}")
    
    def _restore_pending_tasks(self):
        """从数据库恢复未完成的任务"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 查询未完成的任务
            cursor.execute("""
                SELECT id, task_type, component_name, priority, scheduled_time, 
                       dependencies, max_retries, retry_count, status, error_message
                FROM optimization_task_queue
                WHERE status IN ('pending', 'running')
                ORDER BY priority DESC, created_at ASC
            """)
            
            pending_tasks = cursor.fetchall()
            
            for task_data in pending_tasks:
                task_id, task_type, component_name, priority, scheduled_time, \
                dependencies, max_retries, retry_count, status, error_message = task_data
                
                # 创建任务对象
                task = OptimizationTask(
                    task_type=OptimizationTaskType(task_type),
                    component_name=component_name,
                    priority=priority,
                    scheduled_time=datetime.fromisoformat(scheduled_time) if scheduled_time else None,
                    dependencies=json.loads(dependencies) if dependencies else [],
                    max_retries=max_retries,
                    retry_count=retry_count,
                    status=OptimizationStatus(status)
                )
                
                # 将运行中的任务重置为待处理
                if status == 'running':
                    task.status = OptimizationStatus.PENDING
                    self._update_task_status_in_db(task_id, OptimizationStatus.PENDING)
                
                # 添加到内存队列
                queue_item = TaskQueueItem(priority, task_id, task)
                self.task_queue.put(queue_item)
            
            conn.close()
            
            if pending_tasks:
                self.logger.info(f"从数据库恢复了 {len(pending_tasks)} 个未完成任务")
            
        except Exception as e:
            self.logger.error(f"恢复未完成任务失败: {e}")
    
    def add_task(self, task: OptimizationTask) -> int:
        """
        添加任务到队列
        
        Args:
            task: 优化任务
            
        Returns:
            int: 任务ID
        """
        try:
            with self.lock:
                # 检查任务依赖
                if not self._check_task_dependencies(task):
                    raise ValueError(f"任务依赖未满足: {task.task_type.value}")
                
                # 设置任务优先级
                if task.priority == 5:  # 默认优先级
                    task.priority = self.task_priorities.get(task.task_type, TaskPriority.MEDIUM.value)
                
                # 保存到数据库
                task_id = self._save_task_to_db(task)
                
                # 添加到内存队列
                queue_item = TaskQueueItem(task.priority, task_id, task)
                self.task_queue.put(queue_item)
                
                self.logger.info(f"任务已添加到队列: {task.task_type.value}, ID: {task_id}, 优先级: {task.priority}")
                
                return task_id
                
        except Exception as e:
            self.logger.error(f"添加任务失败: {e}")
            raise
    
    def get_next_task(self) -> Optional[TaskQueueItem]:
        """
        获取下一个待执行的任务
        
        Returns:
            TaskQueueItem: 下一个任务，如果队列为空则返回None
        """
        try:
            with self.lock:
                if self.task_queue.empty():
                    return None
                
                # 获取最高优先级的任务
                queue_item = self.task_queue.get()
                
                # 检查任务是否已过期或被取消
                if not self._is_task_valid(queue_item):
                    return self.get_next_task()  # 递归获取下一个有效任务
                
                # 更新任务状态为运行中
                queue_item.task.status = OptimizationStatus.RUNNING
                self._update_task_status_in_db(queue_item.task_id, OptimizationStatus.RUNNING)
                
                # 记录运行中的任务
                self.running_tasks[queue_item.task_id] = {
                    'task': queue_item.task,
                    'started_at': datetime.now()
                }
                
                self.logger.info(f"获取下一个任务: {queue_item.task.task_type.value}, ID: {queue_item.task_id}")
                
                return queue_item
                
        except Exception as e:
            self.logger.error(f"获取下一个任务失败: {e}")
            return None
    
    def update_task_status(self, task_id: int, status: OptimizationStatus, 
                          result: Optional[Dict[str, Any]] = None, 
                          error_message: Optional[str] = None):
        """
        更新任务状态
        
        Args:
            task_id: 任务ID
            status: 新状态
            result: 任务结果
            error_message: 错误信息
        """
        try:
            with self.lock:
                # 更新数据库中的任务状态
                self._update_task_status_in_db(task_id, status, error_message)
                
                # 更新内存中的任务状态
                if task_id in self.running_tasks:
                    task_info = self.running_tasks[task_id]
                    task_info['task'].status = status
                    task_info['completed_at'] = datetime.now()
                    
                    if result:
                        task_info['result'] = result
                    
                    if error_message:
                        task_info['error_message'] = error_message
                    
                    # 如果任务完成或失败，从运行中任务列表移除
                    if status in [OptimizationStatus.COMPLETED, OptimizationStatus.FAILED, OptimizationStatus.CANCELLED]:
                        self.completed_tasks[task_id] = self.running_tasks.pop(task_id)
                    
                    # 如果任务失败且可以重试，重新添加到队列
                    elif status == OptimizationStatus.FAILED:
                        task = task_info['task']
                        if task.retry_count < task.max_retries:
                            task.retry_count += 1
                            task.status = OptimizationStatus.PENDING
                            
                            # 重新添加到队列
                            queue_item = TaskQueueItem(task.priority, task_id, task)
                            self.task_queue.put(queue_item)
                            
                            self.logger.info(f"任务重试: {task.task_type.value}, ID: {task_id}, 重试次数: {task.retry_count}")
                
                self.logger.info(f"任务状态已更新: ID: {task_id}, 状态: {status.value}")
                
        except Exception as e:
            self.logger.error(f"更新任务状态失败: {e}")
    
    def get_queue_status(self) -> Dict[str, Any]:
        """获取队列状态"""
        with self.lock:
            return {
                'pending_tasks': self.task_queue.qsize(),
                'running_tasks': len(self.running_tasks),
                'completed_tasks': len(self.completed_tasks),
                'task_priorities': {task_type.value: priority for task_type, priority in self.task_priorities.items()},
                'running_task_details': [
                    {
                        'task_id': task_id,
                        'task_type': info['task'].task_type.value,
                        'component_name': info['task'].component_name,
                        'started_at': info['started_at'].isoformat(),
                        'duration_seconds': (datetime.now() - info['started_at']).total_seconds()
                    }
                    for task_id, info in self.running_tasks.items()
                ]
            }

    def cancel_task(self, task_id: int) -> bool:
        """
        取消任务

        Args:
            task_id: 任务ID

        Returns:
            bool: 是否成功取消
        """
        try:
            with self.lock:
                # 更新数据库状态
                self._update_task_status_in_db(task_id, OptimizationStatus.CANCELLED)

                # 从运行中任务移除
                if task_id in self.running_tasks:
                    self.completed_tasks[task_id] = self.running_tasks.pop(task_id)
                    self.logger.info(f"运行中任务已取消: ID: {task_id}")
                    return True

                # 从队列中移除（需要重建队列）
                temp_queue = PriorityQueue()
                cancelled = False

                while not self.task_queue.empty():
                    item = self.task_queue.get()
                    if item.task_id == task_id:
                        cancelled = True
                        self.logger.info(f"队列中任务已取消: ID: {task_id}")
                    else:
                        temp_queue.put(item)

                self.task_queue = temp_queue
                return cancelled

        except Exception as e:
            self.logger.error(f"取消任务失败: {e}")
            return False

    def clear_completed_tasks(self, older_than_hours: int = 24):
        """
        清理已完成的任务

        Args:
            older_than_hours: 清理多少小时前的任务
        """
        try:
            cutoff_time = datetime.now() - timedelta(hours=older_than_hours)

            with self.lock:
                # 清理内存中的已完成任务
                to_remove = []
                for task_id, task_info in self.completed_tasks.items():
                    completed_at = task_info.get('completed_at', datetime.now())
                    if completed_at < cutoff_time:
                        to_remove.append(task_id)

                for task_id in to_remove:
                    del self.completed_tasks[task_id]

                # 清理数据库中的已完成任务
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()

                cursor.execute("""
                    DELETE FROM optimization_task_queue
                    WHERE status IN ('completed', 'failed', 'cancelled')
                    AND completed_at < ?
                """, (cutoff_time.isoformat(),))

                deleted_count = cursor.rowcount
                conn.commit()
                conn.close()

                self.logger.info(f"清理了 {len(to_remove)} 个内存任务和 {deleted_count} 个数据库任务")

        except Exception as e:
            self.logger.error(f"清理已完成任务失败: {e}")

    def _check_task_dependencies(self, task: OptimizationTask) -> bool:
        """检查任务依赖是否满足"""
        try:
            dependencies = self.task_dependencies.get(task.task_type, [])

            if not dependencies:
                return True  # 无依赖

            # 检查依赖任务是否已完成
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            for dep_type in dependencies:
                cursor.execute("""
                    SELECT COUNT(*) FROM optimization_task_queue
                    WHERE task_type = ? AND status = 'completed'
                    AND completed_at > datetime('now', '-24 hours')
                """, (dep_type.value,))

                count = cursor.fetchone()[0]
                if count == 0:
                    conn.close()
                    self.logger.warning(f"任务依赖未满足: {task.task_type.value} 依赖 {dep_type.value}")
                    return False

            conn.close()
            return True

        except Exception as e:
            self.logger.error(f"检查任务依赖失败: {e}")
            return False

    def _is_task_valid(self, queue_item: TaskQueueItem) -> bool:
        """检查任务是否有效"""
        try:
            task = queue_item.task

            # 检查任务是否已被取消
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("SELECT status FROM optimization_task_queue WHERE id = ?", (queue_item.task_id,))
            result = cursor.fetchone()

            if not result:
                conn.close()
                return False

            status = result[0]
            conn.close()

            if status == 'cancelled':
                return False

            # 检查任务是否过期
            if task.scheduled_time and task.scheduled_time < datetime.now():
                # 如果任务已过期超过1小时，则认为无效
                if datetime.now() - task.scheduled_time > timedelta(hours=1):
                    self._update_task_status_in_db(queue_item.task_id, OptimizationStatus.CANCELLED, "任务已过期")
                    return False

            return True

        except Exception as e:
            self.logger.error(f"检查任务有效性失败: {e}")
            return False

    def _save_task_to_db(self, task: OptimizationTask) -> int:
        """保存任务到数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                INSERT INTO optimization_task_queue
                (task_type, component_name, priority, scheduled_time, dependencies,
                 max_retries, retry_count, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                task.task_type.value,
                task.component_name,
                task.priority,
                task.scheduled_time.isoformat() if task.scheduled_time else None,
                json.dumps([dep.value for dep in task.dependencies]) if task.dependencies else None,
                task.max_retries,
                task.retry_count,
                task.status.value
            ))

            task_id = cursor.lastrowid
            conn.commit()
            conn.close()

            return task_id

        except Exception as e:
            self.logger.error(f"保存任务到数据库失败: {e}")
            raise

    def _update_task_status_in_db(self, task_id: int, status: OptimizationStatus,
                                 error_message: Optional[str] = None):
        """更新数据库中的任务状态"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            if status == OptimizationStatus.RUNNING:
                cursor.execute("""
                    UPDATE optimization_task_queue
                    SET status = ?, started_at = ?
                    WHERE id = ?
                """, (status.value, datetime.now().isoformat(), task_id))

            elif status in [OptimizationStatus.COMPLETED, OptimizationStatus.FAILED, OptimizationStatus.CANCELLED]:
                cursor.execute("""
                    UPDATE optimization_task_queue
                    SET status = ?, completed_at = ?, error_message = ?
                    WHERE id = ?
                """, (status.value, datetime.now().isoformat(), error_message, task_id))

            else:
                cursor.execute("""
                    UPDATE optimization_task_queue
                    SET status = ?, error_message = ?
                    WHERE id = ?
                """, (status.value, error_message, task_id))

            conn.commit()
            conn.close()

        except Exception as e:
            self.logger.error(f"更新数据库任务状态失败: {e}")
