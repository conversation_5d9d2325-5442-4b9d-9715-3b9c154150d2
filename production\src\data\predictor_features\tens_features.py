"""
十位专用特征生成器
为十位预测器提供50+维专用特征，包括：
- 频次特征：出现频率、遗漏次数、连续性分析
- 冷热度特征：冷热状态、温度变化、周期性分析
- 趋势特征：上升下降趋势、变化幅度、稳定性
- 统计特征：均值、方差、偏度、峰度
- 关联特征：与百位个位的关联度、中位数特性
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional
from collections import Counter, deque


def generate_tens_features(df: pd.DataFrame, window_sizes: List[int] = [5, 10, 20, 50]) -> pd.DataFrame:
    """
    生成十位专用特征
    
    Args:
        df: 包含历史数据的DataFrame，必须包含'tens'列
        window_sizes: 滑动窗口大小列表
        
    Returns:
        pd.DataFrame: 包含十位专用特征的DataFrame
    """
    if 'tens' not in df.columns:
        raise ValueError("DataFrame必须包含'tens'列")
    
    result_df = df.copy()
    tens_series = df['tens']
    
    # 1. 基础频次特征
    result_df.update(_generate_tens_frequency_features(tens_series, window_sizes))
    
    # 2. 遗漏分析特征
    result_df.update(_generate_tens_missing_features(tens_series, window_sizes))
    
    # 3. 冷热度特征
    result_df.update(_generate_tens_hot_cold_features(tens_series, window_sizes))
    
    # 4. 趋势分析特征
    result_df.update(_generate_tens_trend_features(tens_series, window_sizes))
    
    # 5. 统计分布特征
    result_df.update(_generate_tens_statistical_features(tens_series, window_sizes))
    
    # 6. 中位数特性特征
    result_df.update(_generate_tens_middle_position_features(tens_series, window_sizes))
    
    # 7. 关联性特征（如果存在百位和个位数据）
    if 'hundreds' in df.columns and 'units' in df.columns:
        result_df.update(_generate_tens_correlation_features(df[['hundreds', 'tens', 'units']], window_sizes))
    
    return result_df


def _generate_tens_frequency_features(series: pd.Series, window_sizes: List[int]) -> pd.DataFrame:
    """生成十位频次特征"""
    features = pd.DataFrame(index=series.index)
    
    for window in window_sizes:
        # 滑动窗口频次统计
        freq_counts = series.rolling(window=window, min_periods=1).apply(
            lambda x: Counter(x)[x.iloc[-1]] if len(x) > 0 else 0
        )
        features[f'tens_freq_{window}'] = freq_counts
        
        # 频次排名
        features[f'tens_freq_rank_{window}'] = freq_counts.rolling(window=window).rank(pct=True)
        
        # 频次变化率
        features[f'tens_freq_change_{window}'] = freq_counts.pct_change()
        
        # 连续出现次数
        features[f'tens_consecutive_{window}'] = _calculate_consecutive_count(series, window)
    
    # 全局频次特征
    global_counts = series.value_counts()
    features['tens_global_freq'] = series.map(global_counts)
    features['tens_global_freq_rank'] = features['tens_global_freq'].rank(pct=True)
    
    return features


def _generate_tens_missing_features(series: pd.Series, window_sizes: List[int]) -> pd.DataFrame:
    """生成十位遗漏分析特征"""
    features = pd.DataFrame(index=series.index)
    
    # 计算每个数字的遗漏次数
    for digit in range(10):
        missing_counts = []
        current_missing = 0
        
        for value in series:
            if value == digit:
                missing_counts.append(current_missing)
                current_missing = 0
            else:
                current_missing += 1
                missing_counts.append(current_missing)
        
        features[f'tens_missing_{digit}'] = missing_counts
    
    # 当前数字的遗漏特征
    current_missing = []
    for i, value in enumerate(series):
        if i == 0:
            current_missing.append(0)
        else:
            missing = 0
            for j in range(i-1, -1, -1):
                if series.iloc[j] == value:
                    break
                missing += 1
            current_missing.append(missing)
    
    features['tens_current_missing'] = current_missing
    
    # 遗漏统计特征
    for window in window_sizes:
        features[f'tens_avg_missing_{window}'] = features['tens_current_missing'].rolling(window).mean()
        features[f'tens_max_missing_{window}'] = features['tens_current_missing'].rolling(window).max()
        features[f'tens_missing_variance_{window}'] = features['tens_current_missing'].rolling(window).var()
    
    return features


def _generate_tens_hot_cold_features(series: pd.Series, window_sizes: List[int]) -> pd.DataFrame:
    """生成十位冷热度特征"""
    features = pd.DataFrame(index=series.index)
    
    for window in window_sizes:
        # 计算窗口内的出现频次
        window_freq = series.rolling(window=window, min_periods=1).apply(
            lambda x: Counter(x)[x.iloc[-1]] if len(x) > 0 else 0
        )
        
        # 计算期望频次
        expected_freq = window / 10  # 10个数字的期望频次
        
        # 冷热度评分
        features[f'tens_hot_cold_score_{window}'] = (window_freq - expected_freq) / expected_freq
        
        # 冷热度分类
        def classify_tens_hot_cold(score):
            if score > 0.5:
                return 'hot'
            elif score < -0.5:
                return 'cold'
            else:
                return 'normal'
        
        features[f'tens_hot_cold_{window}'] = features[f'tens_hot_cold_score_{window}'].apply(classify_tens_hot_cold)
        
        # 温度变化趋势
        features[f'tens_temp_trend_{window}'] = features[f'tens_hot_cold_score_{window}'].diff()
    
    return features


def _generate_tens_trend_features(series: pd.Series, window_sizes: List[int]) -> pd.DataFrame:
    """生成十位趋势分析特征"""
    features = pd.DataFrame(index=series.index)
    
    for window in window_sizes:
        # 数值趋势
        features[f'tens_trend_{window}'] = series.rolling(window=window).apply(
            lambda x: np.polyfit(range(len(x)), x, 1)[0] if len(x) > 1 else 0
        )
        
        # 变化幅度
        features[f'tens_volatility_{window}'] = series.rolling(window=window).std()
        
        # 上升下降趋势
        features[f'tens_increasing_{window}'] = (series > series.shift(1)).rolling(window=window).sum()
        features[f'tens_decreasing_{window}'] = (series < series.shift(1)).rolling(window=window).sum()
        
        # 震荡特征
        features[f'tens_oscillation_{window}'] = (series.diff().abs()).rolling(window=window).sum()
        
        # 趋势强度
        features[f'tens_trend_strength_{window}'] = np.abs(features[f'tens_trend_{window}'])
    
    return features


def _generate_tens_statistical_features(series: pd.Series, window_sizes: List[int]) -> pd.DataFrame:
    """生成十位统计分布特征"""
    features = pd.DataFrame(index=series.index)
    
    for window in window_sizes:
        # 基础统计量
        features[f'tens_mean_{window}'] = series.rolling(window=window).mean()
        features[f'tens_std_{window}'] = series.rolling(window=window).std()
        features[f'tens_range_{window}'] = series.rolling(window=window).max() - series.rolling(window=window).min()
        
        # 分位数特征
        features[f'tens_q25_{window}'] = series.rolling(window=window).quantile(0.25)
        features[f'tens_q75_{window}'] = series.rolling(window=window).quantile(0.75)
        features[f'tens_iqr_{window}'] = features[f'tens_q75_{window}'] - features[f'tens_q25_{window}']
        
        # 分布形状
        features[f'tens_skew_{window}'] = series.rolling(window=window).skew()
        features[f'tens_kurt_{window}'] = series.rolling(window=window).kurt()
        
        # 众数特征
        features[f'tens_mode_{window}'] = series.rolling(window=window).apply(
            lambda x: x.mode().iloc[0] if len(x.mode()) > 0 else x.iloc[-1]
        )
    
    return features


def _generate_tens_middle_position_features(series: pd.Series, window_sizes: List[int]) -> pd.DataFrame:
    """生成十位中位数特性特征"""
    features = pd.DataFrame(index=series.index)
    
    for window in window_sizes:
        # 中位数特征
        median = series.rolling(window=window).median()
        features[f'tens_median_{window}'] = median
        
        # 与中位数的关系
        features[f'tens_above_median_{window}'] = (series > median).astype(int)
        features[f'tens_below_median_{window}'] = (series < median).astype(int)
        features[f'tens_median_distance_{window}'] = np.abs(series - median)
        
        # 中位数稳定性
        features[f'tens_median_stability_{window}'] = median.rolling(window=window//2).std()
        
        # 中位数变化
        features[f'tens_median_change_{window}'] = median.diff()
    
    # 十位作为中间位的特殊性
    if 'hundreds' in series.index.names or len(series) > 100:
        # 平衡性特征（十位在百位和个位之间的平衡）
        features['tens_balance_indicator'] = _calculate_balance_indicator(series)
    
    return features


def _generate_tens_correlation_features(df: pd.DataFrame, window_sizes: List[int]) -> pd.DataFrame:
    """生成十位关联性特征"""
    features = pd.DataFrame(index=df.index)
    
    hundreds = df['hundreds']
    tens = df['tens']
    units = df['units']
    
    for window in window_sizes:
        # 与百位的相关性
        features[f'tens_hundreds_corr_{window}'] = tens.rolling(window=window).corr(hundreds)
        
        # 与个位的相关性
        features[f'tens_units_corr_{window}'] = tens.rolling(window=window).corr(units)
        
        # 中位数效应
        features[f'tens_middle_effect_{window}'] = _calculate_middle_effect(hundreds, tens, units, window)
        
        # 数字间的平衡度
        features[f'tens_balance_{window}'] = _calculate_position_balance(hundreds, tens, units, window)
        
        # 与邻位的差值特征
        features[f'tens_hundreds_diff_{window}'] = (tens - hundreds).rolling(window=window).mean()
        features[f'tens_units_diff_{window}'] = (tens - units).rolling(window=window).mean()
        
        # 十位的桥梁作用
        features[f'tens_bridge_effect_{window}'] = _calculate_bridge_effect(hundreds, tens, units, window)
    
    return features


def _calculate_consecutive_count(series: pd.Series, window: int) -> pd.Series:
    """计算连续出现次数"""
    consecutive_counts = []
    for i in range(len(series)):
        if i == 0:
            consecutive_counts.append(1)
        else:
            if series.iloc[i] == series.iloc[i-1]:
                consecutive_counts.append(consecutive_counts[-1] + 1)
            else:
                consecutive_counts.append(1)
    return pd.Series(consecutive_counts, index=series.index)


def _calculate_balance_indicator(series: pd.Series) -> pd.Series:
    """计算十位的平衡指标"""
    # 简化的平衡指标：十位数字的分布均匀性
    balance_scores = []
    for i in range(len(series)):
        if i < 10:
            balance_scores.append(0.5)
        else:
            recent_values = series.iloc[max(0, i-9):i+1]
            unique_count = len(recent_values.unique())
            balance_scores.append(unique_count / 10.0)
    return pd.Series(balance_scores, index=series.index)


def _calculate_middle_effect(hundreds: pd.Series, tens: pd.Series, units: pd.Series, window: int) -> pd.Series:
    """计算中位数效应"""
    middle_effects = []
    for i in range(len(tens)):
        if i < window:
            middle_effects.append(0)
        else:
            window_data = pd.DataFrame({
                'h': hundreds.iloc[i-window+1:i+1],
                't': tens.iloc[i-window+1:i+1], 
                'u': units.iloc[i-window+1:i+1]
            })
            # 计算十位是否经常处于百位和个位之间
            between_count = ((window_data['t'] >= window_data[['h', 'u']].min(axis=1)) & 
                           (window_data['t'] <= window_data[['h', 'u']].max(axis=1))).sum()
            middle_effects.append(between_count / window)
    
    return pd.Series(middle_effects, index=tens.index)


def _calculate_position_balance(hundreds: pd.Series, tens: pd.Series, units: pd.Series, window: int) -> pd.Series:
    """计算位置平衡度"""
    balance_scores = []
    for i in range(len(tens)):
        if i < window:
            balance_scores.append(0.5)
        else:
            # 计算三个位置的数字分布均匀性
            h_std = hundreds.iloc[i-window+1:i+1].std()
            t_std = tens.iloc[i-window+1:i+1].std()
            u_std = units.iloc[i-window+1:i+1].std()
            
            # 平衡度：标准差越小越平衡
            balance = 1 / (1 + t_std)
            balance_scores.append(balance)
    
    return pd.Series(balance_scores, index=tens.index)


def _calculate_bridge_effect(hundreds: pd.Series, tens: pd.Series, units: pd.Series, window: int) -> pd.Series:
    """计算十位的桥梁作用"""
    bridge_effects = []
    for i in range(len(tens)):
        if i < window:
            bridge_effects.append(0)
        else:
            # 计算十位与百位、个位的相关性差异
            h_corr = tens.iloc[i-window+1:i+1].corr(hundreds.iloc[i-window+1:i+1])
            u_corr = tens.iloc[i-window+1:i+1].corr(units.iloc[i-window+1:i+1])
            
            # 桥梁效应：与两边相关性的平衡
            bridge_effect = 1 - abs(h_corr - u_corr) if not (pd.isna(h_corr) or pd.isna(u_corr)) else 0
            bridge_effects.append(bridge_effect)
    
    return pd.Series(bridge_effects, index=tens.index)
