#!/usr/bin/env python3
"""
系统性能监控脚本

实现系统性能监控、告警和日志轮转功能
为P8智能交集融合系统提供完整的监控支持

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import os
import sys
import time
import json
import psutil
import logging
import logging.config
import yaml
import sqlite3
import threading
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

class SystemPerformanceMonitor:
    """系统性能监控器"""
    
    def __init__(self, config_path: str = "config/system_logging_config.yaml"):
        """
        初始化系统监控器
        
        Args:
            config_path: 日志配置文件路径
        """
        self.project_root = Path(__file__).parent.parent
        self.config_path = self.project_root / config_path
        self.logs_dir = self.project_root / "logs"
        self.reports_dir = self.project_root / "reports"
        
        # 确保目录存在
        self.logs_dir.mkdir(exist_ok=True)
        self.reports_dir.mkdir(exist_ok=True)
        
        # 设置日志
        self._setup_logging()
        self.logger = logging.getLogger('SystemPerformanceMonitor')
        
        # 监控配置
        self.monitoring_config = {
            'cpu_threshold': 80.0,      # CPU使用率阈值
            'memory_threshold': 85.0,   # 内存使用率阈值
            'disk_threshold': 90.0,     # 磁盘使用率阈值
            'response_time_threshold': 5.0,  # 响应时间阈值(秒)
            'check_interval': 60,       # 检查间隔(秒)
            'alert_cooldown': 300,      # 告警冷却时间(秒)
        }
        
        # 告警状态跟踪
        self.last_alerts = {}
        self.monitoring_active = False
        
        # 性能数据存储
        self.performance_data = []
        
    def _setup_logging(self):
        """设置日志配置"""
        try:
            if self.config_path.exists():
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                logging.config.dictConfig(config)
            else:
                # 基础日志配置
                logging.basicConfig(
                    level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                    handlers=[
                        logging.StreamHandler(),
                        logging.FileHandler(self.logs_dir / 'system_monitor.log')
                    ]
                )
        except Exception as e:
            print(f"日志配置失败: {e}")
            logging.basicConfig(level=logging.INFO)
    
    def get_system_metrics(self) -> Dict[str, Any]:
        """获取系统性能指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存使用情况
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_available = memory.available / (1024**3)  # GB
            
            # 磁盘使用情况
            disk = psutil.disk_usage('/')
            disk_percent = disk.percent
            disk_free = disk.free / (1024**3)  # GB
            
            # 网络IO
            net_io = psutil.net_io_counters()
            
            # 进程信息
            process_count = len(psutil.pids())
            
            metrics = {
                'timestamp': datetime.now().isoformat(),
                'cpu_percent': cpu_percent,
                'memory_percent': memory_percent,
                'memory_available_gb': round(memory_available, 2),
                'disk_percent': disk_percent,
                'disk_free_gb': round(disk_free, 2),
                'network_bytes_sent': net_io.bytes_sent,
                'network_bytes_recv': net_io.bytes_recv,
                'process_count': process_count,
            }
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"获取系统指标失败: {e}")
            return {}
    
    def check_thresholds(self, metrics: Dict[str, Any]) -> List[Dict[str, Any]]:
        """检查性能阈值并生成告警"""
        alerts = []
        current_time = datetime.now()
        
        # CPU告警
        if metrics.get('cpu_percent', 0) > self.monitoring_config['cpu_threshold']:
            alert_key = 'cpu_high'
            if self._should_send_alert(alert_key, current_time):
                alerts.append({
                    'type': 'cpu_high',
                    'level': 'WARNING',
                    'message': f"CPU使用率过高: {metrics['cpu_percent']:.1f}%",
                    'value': metrics['cpu_percent'],
                    'threshold': self.monitoring_config['cpu_threshold'],
                    'timestamp': current_time.isoformat()
                })
        
        # 内存告警
        if metrics.get('memory_percent', 0) > self.monitoring_config['memory_threshold']:
            alert_key = 'memory_high'
            if self._should_send_alert(alert_key, current_time):
                alerts.append({
                    'type': 'memory_high',
                    'level': 'WARNING',
                    'message': f"内存使用率过高: {metrics['memory_percent']:.1f}%",
                    'value': metrics['memory_percent'],
                    'threshold': self.monitoring_config['memory_threshold'],
                    'timestamp': current_time.isoformat()
                })
        
        # 磁盘告警
        if metrics.get('disk_percent', 0) > self.monitoring_config['disk_threshold']:
            alert_key = 'disk_high'
            if self._should_send_alert(alert_key, current_time):
                alerts.append({
                    'type': 'disk_high',
                    'level': 'CRITICAL',
                    'message': f"磁盘使用率过高: {metrics['disk_percent']:.1f}%",
                    'value': metrics['disk_percent'],
                    'threshold': self.monitoring_config['disk_threshold'],
                    'timestamp': current_time.isoformat()
                })
        
        return alerts
    
    def _should_send_alert(self, alert_key: str, current_time: datetime) -> bool:
        """检查是否应该发送告警（避免重复告警）"""
        if alert_key not in self.last_alerts:
            self.last_alerts[alert_key] = current_time
            return True
        
        time_diff = (current_time - self.last_alerts[alert_key]).total_seconds()
        if time_diff >= self.monitoring_config['alert_cooldown']:
            self.last_alerts[alert_key] = current_time
            return True
        
        return False
    
    def send_alerts(self, alerts: List[Dict[str, Any]]):
        """发送告警通知"""
        alert_logger = logging.getLogger('AlertSystem')
        
        for alert in alerts:
            # 记录到告警日志
            alert_logger.warning(f"{alert['type']}: {alert['message']}")
            
            # 打印到控制台
            print(f"🚨 {alert['level']}: {alert['message']}")
            
            # 这里可以添加邮件、短信等通知方式
            # self._send_email_alert(alert)
    
    def save_performance_data(self, metrics: Dict[str, Any]):
        """保存性能数据到文件"""
        try:
            # 添加到内存缓存
            self.performance_data.append(metrics)
            
            # 每100条数据保存一次
            if len(self.performance_data) >= 100:
                self._flush_performance_data()
                
        except Exception as e:
            self.logger.error(f"保存性能数据失败: {e}")
    
    def _flush_performance_data(self):
        """将性能数据刷新到文件"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = self.reports_dir / f"performance_data_{timestamp}.json"
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.performance_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"性能数据已保存到: {filename}")
            self.performance_data.clear()
            
        except Exception as e:
            self.logger.error(f"刷新性能数据失败: {e}")
    
    def start_monitoring(self):
        """启动监控"""
        self.monitoring_active = True
        self.logger.info("系统性能监控已启动")
        
        try:
            while self.monitoring_active:
                # 获取系统指标
                metrics = self.get_system_metrics()
                if metrics:
                    # 记录性能数据
                    self.logger.info(f"系统指标: CPU={metrics.get('cpu_percent', 0):.1f}%, "
                                   f"内存={metrics.get('memory_percent', 0):.1f}%, "
                                   f"磁盘={metrics.get('disk_percent', 0):.1f}%")
                    
                    # 检查告警
                    alerts = self.check_thresholds(metrics)
                    if alerts:
                        self.send_alerts(alerts)
                    
                    # 保存数据
                    self.save_performance_data(metrics)
                
                # 等待下次检查
                time.sleep(self.monitoring_config['check_interval'])
                
        except KeyboardInterrupt:
            self.logger.info("监控被用户中断")
        except Exception as e:
            self.logger.error(f"监控过程中发生错误: {e}")
        finally:
            self.stop_monitoring()
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring_active = False
        
        # 保存剩余的性能数据
        if self.performance_data:
            self._flush_performance_data()
        
        self.logger.info("系统性能监控已停止")
    
    def generate_report(self, hours: int = 24) -> Dict[str, Any]:
        """生成性能报告"""
        try:
            # 这里可以从保存的数据文件中读取历史数据
            # 目前返回当前状态报告
            current_metrics = self.get_system_metrics()
            
            report = {
                'report_time': datetime.now().isoformat(),
                'period_hours': hours,
                'current_status': current_metrics,
                'summary': {
                    'status': 'healthy' if all([
                        current_metrics.get('cpu_percent', 0) < self.monitoring_config['cpu_threshold'],
                        current_metrics.get('memory_percent', 0) < self.monitoring_config['memory_threshold'],
                        current_metrics.get('disk_percent', 0) < self.monitoring_config['disk_threshold']
                    ]) else 'warning'
                }
            }
            
            return report
            
        except Exception as e:
            self.logger.error(f"生成报告失败: {e}")
            return {}


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="系统性能监控工具")
    parser.add_argument('--action', choices=['start', 'status', 'report'], 
                       default='start', help='执行的操作')
    parser.add_argument('--hours', type=int, default=24, 
                       help='报告时间范围(小时)')
    
    args = parser.parse_args()
    
    monitor = SystemPerformanceMonitor()
    
    if args.action == 'start':
        print("启动系统性能监控...")
        monitor.start_monitoring()
    elif args.action == 'status':
        metrics = monitor.get_system_metrics()
        print("当前系统状态:")
        print(json.dumps(metrics, indent=2, ensure_ascii=False))
    elif args.action == 'report':
        report = monitor.generate_report(args.hours)
        print("系统性能报告:")
        print(json.dumps(report, indent=2, ensure_ascii=False))


if __name__ == "__main__":
    main()
