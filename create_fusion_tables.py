#!/usr/bin/env python3
"""
创建P8融合系统数据库表
"""

import sqlite3
import os

def create_fusion_tables():
    """创建融合系统数据表"""
    
    # 确保数据目录存在
    os.makedirs("data", exist_ok=True)
    
    # 数据库路径
    db_path = "data/lottery.db"
    
    # SQL脚本内容
    sql_script = """
-- P8智能交集融合系统数据表创建脚本

-- 最终预测结果表
CREATE TABLE IF NOT EXISTS final_predictions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    issue TEXT NOT NULL,
    prediction_rank INTEGER NOT NULL,
    hundreds INTEGER NOT NULL,
    tens INTEGER NOT NULL,
    units INTEGER NOT NULL,
    sum_value INTEGER NOT NULL,
    span_value INTEGER NOT NULL,
    combined_probability REAL NOT NULL,
    hundreds_prob REAL NOT NULL,
    tens_prob REAL NOT NULL,
    units_prob REAL NOT NULL,
    sum_prob REAL NOT NULL,
    span_prob REAL NOT NULL,
    sum_consistency REAL NOT NULL,
    span_consistency REAL NOT NULL,
    constraint_score REAL NOT NULL,
    diversity_score REAL NOT NULL,
    confidence_level TEXT,
    fusion_method TEXT,
    ranking_strategy TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(issue, prediction_rank)
);

-- 融合权重配置表
CREATE TABLE IF NOT EXISTS fusion_weights (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    weight_type TEXT NOT NULL,
    predictor_name TEXT NOT NULL,
    model_name TEXT,
    weight_value REAL NOT NULL,
    performance_score REAL,
    accuracy_rate REAL,
    confidence_score REAL,
    is_active BOOLEAN DEFAULT TRUE,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(weight_type, predictor_name, model_name)
);

-- 预测性能评估表
CREATE TABLE IF NOT EXISTS prediction_performance (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    issue TEXT NOT NULL,
    actual_hundreds INTEGER,
    actual_tens INTEGER,
    actual_units INTEGER,
    actual_sum INTEGER,
    actual_span INTEGER,
    predicted_rank INTEGER,
    hit_type TEXT,
    hundreds_accuracy BOOLEAN,
    tens_accuracy BOOLEAN,
    units_accuracy BOOLEAN,
    sum_accuracy BOOLEAN,
    span_accuracy BOOLEAN,
    overall_score REAL,
    probability_score REAL,
    constraint_score REAL,
    diversity_effectiveness REAL,
    fusion_method TEXT,
    ranking_strategy TEXT,
    top_k_hit INTEGER,
    confidence_accuracy REAL,
    evaluated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(issue)
);

-- 约束规则表
CREATE TABLE IF NOT EXISTS fusion_constraint_rules (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    rule_name TEXT NOT NULL,
    rule_type TEXT NOT NULL,
    rule_expression TEXT NOT NULL,
    weight REAL DEFAULT 1.0,
    tolerance REAL DEFAULT 0.1,
    is_active BOOLEAN DEFAULT TRUE,
    priority INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(rule_name)
);

-- 融合会话记录表
CREATE TABLE IF NOT EXISTS fusion_sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_id TEXT NOT NULL,
    issue TEXT NOT NULL,
    fusion_method TEXT NOT NULL,
    ranking_strategy TEXT NOT NULL,
    input_data TEXT NOT NULL,
    output_data TEXT NOT NULL,
    execution_time REAL,
    success BOOLEAN DEFAULT TRUE,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(session_id)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_final_predictions_issue ON final_predictions(issue);
CREATE INDEX IF NOT EXISTS idx_final_predictions_rank ON final_predictions(prediction_rank);
CREATE INDEX IF NOT EXISTS idx_fusion_weights_type ON fusion_weights(weight_type);
CREATE INDEX IF NOT EXISTS idx_fusion_weights_predictor ON fusion_weights(predictor_name);
CREATE INDEX IF NOT EXISTS idx_prediction_performance_issue ON prediction_performance(issue);
CREATE INDEX IF NOT EXISTS idx_fusion_constraint_rules_type ON fusion_constraint_rules(rule_type);
CREATE INDEX IF NOT EXISTS idx_fusion_sessions_issue ON fusion_sessions(issue);

-- 插入默认权重配置
INSERT OR IGNORE INTO fusion_weights (weight_type, predictor_name, model_name, weight_value, performance_score, accuracy_rate, confidence_score) VALUES
('position', 'hundreds', 'ensemble', 1.0, 0.7, 0.6, 0.8),
('position', 'tens', 'ensemble', 1.0, 0.7, 0.6, 0.8),
('position', 'units', 'ensemble', 1.0, 0.7, 0.6, 0.8),
('auxiliary', 'sum', 'ensemble', 0.8, 0.6, 0.5, 0.7),
('auxiliary', 'span', 'ensemble', 0.6, 0.5, 0.4, 0.6),
('constraint', 'constraint', 'optimizer', 0.4, 0.4, 0.3, 0.5);

-- 插入默认约束规则
INSERT OR IGNORE INTO fusion_constraint_rules (rule_name, rule_type, rule_expression, weight, tolerance, priority) VALUES
('sum_range_constraint', 'sum_span', 'sum_value >= 0 AND sum_value <= 27', 1.0, 0.0, 1),
('span_range_constraint', 'sum_span', 'span_value >= 0 AND span_value <= 9', 1.0, 0.0, 1),
('probability_threshold', 'probability', 'combined_probability >= 0.001', 0.8, 0.0001, 2),
('diversity_minimum', 'diversity', 'diversity_score >= 0.1', 0.6, 0.05, 3),
('consistency_threshold', 'consistency', 'constraint_score >= 0.3', 0.7, 0.1, 2);
"""
    
    try:
        # 连接数据库并执行脚本
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 执行SQL脚本
            cursor.executescript(sql_script)
            conn.commit()
            
            print("✓ 融合系统数据表创建成功")
            
            # 验证表是否创建成功
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND (name LIKE '%fusion%' OR name LIKE '%final%' OR name LIKE '%prediction_performance%')")
            tables = cursor.fetchall()
            
            print(f"✓ 创建了 {len(tables)} 个融合系统相关表:")
            for table in tables:
                print(f"  - {table[0]}")
            
            # 检查默认数据
            cursor.execute("SELECT COUNT(*) FROM fusion_weights")
            weight_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM fusion_constraint_rules")
            rule_count = cursor.fetchone()[0]
            
            print(f"\n✓ 默认数据:")
            print(f"  - 权重配置: {weight_count} 条")
            print(f"  - 约束规则: {rule_count} 条")
            
            return True
            
    except Exception as e:
        print(f"错误: 创建融合系统数据表失败: {e}")
        return False

if __name__ == '__main__':
    print("开始创建P8融合系统数据表...")
    success = create_fusion_tables()
    
    if success:
        print("\n🎉 P8融合系统数据库初始化完成!")
    else:
        print("\n❌ P8融合系统数据库初始化失败!")
