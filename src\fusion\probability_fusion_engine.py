#!/usr/bin/env python3
"""
概率融合引擎

实现多种概率融合算法：加权乘积、加权平均、贝叶斯融合、自适应融合
为P8智能交集融合系统提供核心融合功能

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Callable
import logging
from datetime import datetime
import json
from scipy import stats
from scipy.optimize import minimize

class ProbabilityFusionEngine:
    """概率融合引擎 - P8核心组件"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化概率融合引擎
        
        Args:
            config: 配置参数
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 融合方法映射
        self.fusion_methods = {
            'weighted_product': self._weighted_product_fusion,
            'weighted_average': self._weighted_average_fusion,
            'bayesian_fusion': self._bayesian_fusion,
            'adaptive_fusion': self._adaptive_fusion,
            'entropy_weighted': self._entropy_weighted_fusion,
            'confidence_weighted': self._confidence_weighted_fusion
        }
        
        # 默认权重配置（基于P6-P7实际性能）
        self.default_weights = {
            'hundreds': 1.0,
            'tens': 1.0,
            'units': 1.0,
            'sum': 0.8,      # 和值约束权重
            'span': 0.6,     # 跨度约束权重
            'constraint': 0.4 # 一致性约束权重
        }
        
        # 融合参数
        self.fusion_params = {
            'min_probability': 1e-8,  # 最小概率阈值
            'max_combinations': 1000,  # 最大组合数
            'normalization_method': 'softmax',  # 归一化方法
            'temperature': 1.0,  # softmax温度参数
            'confidence_threshold': 0.1  # 置信度阈值
        }
        
        self.logger.info("概率融合引擎初始化完成")
    
    def fuse_probabilities(self, predictions: Dict[str, Any], 
                          weights: Optional[Dict[str, float]] = None,
                          method: str = 'adaptive_fusion') -> Dict[str, float]:
        """
        融合各预测器的概率分布
        
        Args:
            predictions: 标准化预测结果
            weights: 动态权重配置
            method: 融合方法
        
        Returns:
            融合后的号码概率分布 {combination: probability}
        """
        if method not in self.fusion_methods:
            raise ValueError(f"不支持的融合方法: {method}")
        
        # 使用默认权重如果未提供
        if weights is None:
            weights = self.default_weights.copy()
        
        try:
            # 执行融合
            fused_combinations = self.fusion_methods[method](predictions, weights)
            
            # 后处理
            fused_combinations = self._post_process_combinations(fused_combinations)
            
            self.logger.info(f"使用{method}方法融合完成，生成{len(fused_combinations)}个组合")
            return fused_combinations
            
        except Exception as e:
            self.logger.error(f"概率融合失败: {e}")
            return self._create_fallback_combinations()
    
    def _weighted_product_fusion(self, predictions: Dict[str, Any], 
                                weights: Dict[str, float]) -> Dict[str, float]:
        """加权乘积融合（适合独立预测器）"""
        fused_combinations = {}
        
        # 获取各预测器的概率
        pos_probs = self._extract_position_probabilities(predictions)
        sum_probs = self._extract_sum_probabilities(predictions)
        span_probs = self._extract_span_probabilities(predictions)
        
        # 遍历所有可能的三位数组合
        for h in range(10):
            for t in range(10):
                for u in range(10):
                    combination = f"{h}{t}{u}"
                    
                    # 位置概率
                    pos_prob = (
                        pos_probs['hundreds'][h] ** weights.get('hundreds', 1.0) *
                        pos_probs['tens'][t] ** weights.get('tens', 1.0) *
                        pos_probs['units'][u] ** weights.get('units', 1.0)
                    )
                    
                    # 和值约束
                    sum_value = h + t + u
                    sum_prob = sum_probs[sum_value] ** weights.get('sum', 1.0)
                    
                    # 跨度约束
                    span_value = max(h, t, u) - min(h, t, u)
                    span_prob = span_probs[span_value] ** weights.get('span', 1.0)
                    
                    # 综合概率（乘积形式）
                    fused_combinations[combination] = pos_prob * sum_prob * span_prob
        
        return self._normalize_combinations(fused_combinations)
    
    def _weighted_average_fusion(self, predictions: Dict[str, Any], 
                                weights: Dict[str, float]) -> Dict[str, float]:
        """加权平均融合（适合相关预测器）"""
        fused_combinations = {}
        
        pos_probs = self._extract_position_probabilities(predictions)
        sum_probs = self._extract_sum_probabilities(predictions)
        span_probs = self._extract_span_probabilities(predictions)
        
        # 计算权重总和
        total_weight = sum(weights.values())
        
        for h in range(10):
            for t in range(10):
                for u in range(10):
                    combination = f"{h}{t}{u}"
                    
                    # 加权平均
                    weighted_sum = (
                        pos_probs['hundreds'][h] * weights.get('hundreds', 1.0) +
                        pos_probs['tens'][t] * weights.get('tens', 1.0) +
                        pos_probs['units'][u] * weights.get('units', 1.0)
                    ) / 3  # 位置预测器平均
                    
                    # 和值约束
                    sum_value = h + t + u
                    sum_constraint = sum_probs[sum_value] * weights.get('sum', 1.0)
                    
                    # 跨度约束
                    span_value = max(h, t, u) - min(h, t, u)
                    span_constraint = span_probs[span_value] * weights.get('span', 1.0)
                    
                    # 综合概率（加权平均）
                    fused_combinations[combination] = (
                        weighted_sum + sum_constraint + span_constraint
                    ) / (3 + weights.get('sum', 1.0) + weights.get('span', 1.0))
        
        return self._normalize_combinations(fused_combinations)
    
    def _adaptive_fusion(self, predictions: Dict[str, Any], 
                        weights: Dict[str, float]) -> Dict[str, float]:
        """自适应融合（根据置信度动态调整）"""
        # 计算各预测器的置信度
        confidences = self._extract_confidences(predictions)
        
        # 动态调整权重
        adaptive_weights = {}
        for key, base_weight in weights.items():
            if key in confidences:
                # 置信度越高，权重越大
                confidence_factor = confidences[key] ** 0.5
                adaptive_weights[key] = base_weight * confidence_factor
            else:
                adaptive_weights[key] = base_weight
        
        # 使用调整后的权重进行加权乘积融合
        return self._weighted_product_fusion(predictions, adaptive_weights)
    
    def _bayesian_fusion(self, predictions: Dict[str, Any], 
                        weights: Dict[str, float]) -> Dict[str, float]:
        """贝叶斯融合（考虑预测器间的相关性）"""
        fused_combinations = {}
        
        pos_probs = self._extract_position_probabilities(predictions)
        sum_probs = self._extract_sum_probabilities(predictions)
        span_probs = self._extract_span_probabilities(predictions)
        
        # 先验概率（均匀分布）
        prior = 1.0 / 1000  # 1000个可能的组合
        
        for h in range(10):
            for t in range(10):
                for u in range(10):
                    combination = f"{h}{t}{u}"
                    
                    # 似然函数
                    likelihood = (
                        pos_probs['hundreds'][h] *
                        pos_probs['tens'][t] *
                        pos_probs['units'][u]
                    )
                    
                    # 和值约束似然
                    sum_value = h + t + u
                    sum_likelihood = sum_probs[sum_value]
                    
                    # 跨度约束似然
                    span_value = max(h, t, u) - min(h, t, u)
                    span_likelihood = span_probs[span_value]
                    
                    # 贝叶斯后验概率
                    posterior = (
                        prior * 
                        likelihood ** weights.get('position', 1.0) *
                        sum_likelihood ** weights.get('sum', 1.0) *
                        span_likelihood ** weights.get('span', 1.0)
                    )
                    
                    fused_combinations[combination] = posterior
        
        return self._normalize_combinations(fused_combinations)
    
    def _entropy_weighted_fusion(self, predictions: Dict[str, Any], 
                                weights: Dict[str, float]) -> Dict[str, float]:
        """基于熵的加权融合"""
        # 计算各预测器的熵
        entropies = self._calculate_prediction_entropies(predictions)
        
        # 熵越低（确定性越高），权重越大
        entropy_weights = {}
        for key, entropy in entropies.items():
            if entropy > 0:
                entropy_weights[key] = 1.0 / (1.0 + entropy)
            else:
                entropy_weights[key] = 1.0
        
        # 结合原始权重和熵权重
        combined_weights = {}
        for key in weights:
            combined_weights[key] = weights[key] * entropy_weights.get(key, 1.0)
        
        return self._weighted_product_fusion(predictions, combined_weights)
    
    def _confidence_weighted_fusion(self, predictions: Dict[str, Any], 
                                   weights: Dict[str, float]) -> Dict[str, float]:
        """基于置信度的加权融合"""
        confidences = self._extract_confidences(predictions)
        
        # 置信度加权
        confidence_weights = {}
        for key, base_weight in weights.items():
            confidence = confidences.get(key, 0.5)
            confidence_weights[key] = base_weight * confidence
        
        return self._weighted_average_fusion(predictions, confidence_weights)
    
    def _extract_position_probabilities(self, predictions: Dict[str, Any]) -> Dict[str, List[float]]:
        """提取位置概率分布"""
        pos_probs = {
            'hundreds': [0.1] * 10,
            'tens': [0.1] * 10,
            'units': [0.1] * 10
        }
        
        for position in ['hundreds', 'tens', 'units']:
            if position in predictions and 'probabilities' in predictions[position]:
                probs = predictions[position]['probabilities']
                if len(probs) == 10:
                    pos_probs[position] = probs
        
        return pos_probs
    
    def _extract_sum_probabilities(self, predictions: Dict[str, Any]) -> List[float]:
        """提取和值概率分布"""
        if 'sum' in predictions and 'probabilities' in predictions['sum']:
            probs = predictions['sum']['probabilities']
            if len(probs) == 28:
                return probs
        
        # 默认均匀分布
        return [1/28] * 28
    
    def _extract_span_probabilities(self, predictions: Dict[str, Any]) -> List[float]:
        """提取跨度概率分布"""
        if 'span' in predictions and 'probabilities' in predictions['span']:
            probs = predictions['span']['probabilities']
            if len(probs) == 10:
                return probs
        
        # 默认均匀分布
        return [0.1] * 10
    
    def _extract_confidences(self, predictions: Dict[str, Any]) -> Dict[str, float]:
        """提取置信度"""
        confidences = {}
        
        for name, pred in predictions.items():
            if 'confidence' in pred:
                confidences[name] = pred['confidence']
            else:
                confidences[name] = 0.5  # 默认置信度
        
        return confidences
    
    def _calculate_prediction_entropies(self, predictions: Dict[str, Any]) -> Dict[str, float]:
        """计算预测器的熵"""
        entropies = {}
        
        for name, pred in predictions.items():
            if 'probabilities' in pred:
                probs = np.array(pred['probabilities'])
                probs = probs[probs > 0]  # 只考虑非零概率
                if len(probs) > 0:
                    entropy = -np.sum(probs * np.log2(probs))
                    entropies[name] = entropy
                else:
                    entropies[name] = 0.0
            else:
                entropies[name] = 1.0  # 默认熵值
        
        return entropies
    
    def _normalize_combinations(self, combinations: Dict[str, float]) -> Dict[str, float]:
        """归一化组合概率"""
        total_prob = sum(combinations.values())
        
        if total_prob > 0:
            return {k: v / total_prob for k, v in combinations.items()}
        else:
            # 如果总概率为0，返回均匀分布
            uniform_prob = 1.0 / len(combinations)
            return {k: uniform_prob for k in combinations.keys()}
    
    def _post_process_combinations(self, combinations: Dict[str, float]) -> Dict[str, float]:
        """后处理组合结果"""
        # 应用最小概率阈值
        min_prob = self.fusion_params['min_probability']
        processed = {}
        
        for combo, prob in combinations.items():
            if prob >= min_prob:
                processed[combo] = prob
        
        # 如果过滤后没有组合，保留概率最高的组合
        if not processed:
            max_combo = max(combinations.items(), key=lambda x: x[1])
            processed[max_combo[0]] = max_combo[1]
        
        # 重新归一化
        return self._normalize_combinations(processed)
    
    def _create_fallback_combinations(self) -> Dict[str, float]:
        """创建备用组合（当融合失败时）"""
        # 返回一些常见的号码组合
        fallback_combos = ['123', '456', '789', '012', '345', '678', '901', '234', '567', '890']
        uniform_prob = 1.0 / len(fallback_combos)
        
        return {combo: uniform_prob for combo in fallback_combos}
