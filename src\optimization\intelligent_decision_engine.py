#!/usr/bin/env python3
"""
P9智能决策引擎

该模块实现P9闭环优化系统的智能决策功能，包括：
1. 基于历史数据的优化决策
2. 风险评估和策略选择
3. 多目标优化决策
4. 自适应学习机制

作者: Augment Code AI Assistant
创建日期: 2025-01-14
版本: 1.0.0
"""

import numpy as np
import pandas as pd
import sqlite3
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import statistics
from collections import defaultdict

class DecisionType(Enum):
    """决策类型枚举"""
    OPTIMIZATION_STRATEGY = "optimization_strategy"
    PARAMETER_ADJUSTMENT = "parameter_adjustment"
    MODEL_SELECTION = "model_selection"
    RESOURCE_ALLOCATION = "resource_allocation"
    RISK_MITIGATION = "risk_mitigation"

class RiskLevel(Enum):
    """风险级别枚举"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class DecisionContext:
    """决策上下文"""
    decision_type: DecisionType
    current_performance: Dict[str, float]
    historical_data: Dict[str, Any]
    constraints: Dict[str, Any]
    objectives: List[str]
    risk_tolerance: RiskLevel = RiskLevel.MEDIUM
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

@dataclass
class DecisionResult:
    """决策结果"""
    decision_type: DecisionType
    recommended_action: str
    confidence: float
    risk_assessment: Dict[str, Any]
    expected_impact: Dict[str, float]
    alternative_actions: List[Dict[str, Any]]
    reasoning: str
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

class IntelligentDecisionEngine:
    """智能决策引擎"""
    
    def __init__(self, db_path: str, config: Optional[Dict[str, Any]] = None):
        """
        初始化智能决策引擎
        
        Args:
            db_path: 数据库路径
            config: 配置参数
        """
        self.db_path = db_path
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 决策配置
        self.decision_config = {
            'lookback_days': self.config.get('lookback_days', 30),
            'min_samples': self.config.get('min_samples', 10),
            'confidence_threshold': self.config.get('confidence_threshold', 0.7),
            'risk_weight': self.config.get('risk_weight', 0.3),
            'performance_weight': self.config.get('performance_weight', 0.7),
            'learning_rate': self.config.get('learning_rate', 0.01)
        }
        
        # 决策历史
        self.decision_history = []
        
        # 性能权重
        self.performance_weights = {
            'accuracy': 0.3,
            'hit_rate': 0.25,
            'top10_hit_rate': 0.2,
            'mae': 0.15,
            'response_time': 0.1
        }
        
        # 风险因子
        self.risk_factors = {
            'performance_volatility': 0.3,
            'recent_failures': 0.25,
            'resource_usage': 0.2,
            'system_stability': 0.15,
            'external_factors': 0.1
        }
        
        self.logger.info("智能决策引擎初始化完成")
    
    def make_optimization_decision(self, context: DecisionContext) -> DecisionResult:
        """
        制定优化决策
        
        Args:
            context: 决策上下文
            
        Returns:
            DecisionResult: 决策结果
        """
        try:
            self.logger.info(f"开始制定优化决策: {context.decision_type.value}")
            
            # 分析历史数据
            historical_analysis = self._analyze_historical_data(context)
            
            # 评估当前性能
            performance_analysis = self._analyze_current_performance(context)
            
            # 风险评估
            risk_assessment = self._evaluate_optimization_risk(context)
            
            # 生成候选策略
            candidate_strategies = self._generate_candidate_strategies(context, historical_analysis)
            
            # 评估和选择最佳策略
            best_strategy = self._select_best_strategy(
                candidate_strategies, 
                performance_analysis, 
                risk_assessment, 
                context
            )
            
            # 计算置信度
            confidence = self._calculate_decision_confidence(
                best_strategy, 
                historical_analysis, 
                risk_assessment
            )
            
            # 生成决策结果
            decision_result = DecisionResult(
                decision_type=context.decision_type,
                recommended_action=best_strategy['action'],
                confidence=confidence,
                risk_assessment=risk_assessment,
                expected_impact=best_strategy['expected_impact'],
                alternative_actions=candidate_strategies[1:3],  # 前2个备选方案
                reasoning=best_strategy['reasoning']
            )
            
            # 记录决策历史
            self._record_decision(context, decision_result)
            
            self.logger.info(f"优化决策完成: {decision_result.recommended_action}, 置信度: {confidence:.3f}")
            
            return decision_result
            
        except Exception as e:
            self.logger.error(f"制定优化决策失败: {e}")
            return DecisionResult(
                decision_type=context.decision_type,
                recommended_action="maintain_current_state",
                confidence=0.0,
                risk_assessment={'error': str(e)},
                expected_impact={},
                alternative_actions=[],
                reasoning=f"决策过程出错: {e}"
            )
    
    def _analyze_historical_data(self, context: DecisionContext) -> Dict[str, Any]:
        """分析历史数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            # 查询历史优化记录
            lookback_date = datetime.now() - timedelta(days=self.decision_config['lookback_days'])
            
            query = """
                SELECT optimization_type, component_name, status, improvement_score, 
                       details, start_time, end_time
                FROM optimization_logs
                WHERE start_time > ?
                ORDER BY start_time DESC
            """
            
            df = pd.read_sql_query(query, conn, params=[lookback_date.isoformat()])
            conn.close()
            
            if df.empty:
                return {'message': '暂无历史数据', 'patterns': {}}
            
            # 分析优化模式
            patterns = self._extract_optimization_patterns(df)
            
            # 计算成功率
            success_rates = self._calculate_success_rates(df)
            
            # 分析性能趋势
            performance_trends = self._analyze_performance_trends(df)
            
            return {
                'total_optimizations': len(df),
                'patterns': patterns,
                'success_rates': success_rates,
                'performance_trends': performance_trends,
                'recent_failures': self._count_recent_failures(df),
                'avg_improvement': df['improvement_score'].mean() if 'improvement_score' in df.columns else 0.0
            }
            
        except Exception as e:
            self.logger.error(f"分析历史数据失败: {e}")
            return {'error': str(e)}
    
    def _extract_optimization_patterns(self, df: pd.DataFrame) -> Dict[str, Any]:
        """提取优化模式"""
        patterns = {}
        
        try:
            # 按优化类型分组分析
            for opt_type, group in df.groupby('optimization_type'):
                success_rate = (group['status'] == 'completed').mean()
                avg_improvement = group['improvement_score'].mean() if 'improvement_score' in group.columns else 0.0
                
                patterns[opt_type] = {
                    'count': len(group),
                    'success_rate': success_rate,
                    'avg_improvement': avg_improvement,
                    'last_execution': group['start_time'].max()
                }
            
            # 按组件分组分析
            component_patterns = {}
            for component, group in df.groupby('component_name'):
                if pd.notna(component):
                    success_rate = (group['status'] == 'completed').mean()
                    component_patterns[component] = {
                        'count': len(group),
                        'success_rate': success_rate
                    }
            
            patterns['by_component'] = component_patterns
            
            return patterns
            
        except Exception as e:
            self.logger.error(f"提取优化模式失败: {e}")
            return {}
    
    def _calculate_success_rates(self, df: pd.DataFrame) -> Dict[str, float]:
        """计算成功率"""
        try:
            overall_success_rate = (df['status'] == 'completed').mean()
            
            # 最近7天的成功率
            recent_date = datetime.now() - timedelta(days=7)
            recent_df = df[pd.to_datetime(df['start_time']) > recent_date]
            recent_success_rate = (recent_df['status'] == 'completed').mean() if not recent_df.empty else 0.0
            
            return {
                'overall': overall_success_rate,
                'recent_7_days': recent_success_rate,
                'trend': 'improving' if recent_success_rate > overall_success_rate else 'declining'
            }
            
        except Exception as e:
            self.logger.error(f"计算成功率失败: {e}")
            return {'overall': 0.0, 'recent_7_days': 0.0, 'trend': 'unknown'}
    
    def _analyze_performance_trends(self, df: pd.DataFrame) -> Dict[str, Any]:
        """分析性能趋势"""
        try:
            if 'improvement_score' not in df.columns:
                return {'message': '无性能改进数据'}
            
            # 过滤有效的改进分数
            valid_scores = df['improvement_score'].dropna()
            
            if valid_scores.empty:
                return {'message': '无有效的改进分数'}
            
            # 计算趋势
            if len(valid_scores) >= 2:
                # 使用线性回归计算趋势
                x = np.arange(len(valid_scores))
                y = valid_scores.values
                
                if len(x) > 1:
                    slope = np.polyfit(x, y, 1)[0]
                    trend_direction = 'improving' if slope > 0 else 'declining' if slope < 0 else 'stable'
                else:
                    trend_direction = 'stable'
            else:
                trend_direction = 'insufficient_data'
            
            return {
                'trend_direction': trend_direction,
                'avg_improvement': float(valid_scores.mean()),
                'std_improvement': float(valid_scores.std()),
                'best_improvement': float(valid_scores.max()),
                'worst_improvement': float(valid_scores.min()),
                'sample_count': len(valid_scores)
            }
            
        except Exception as e:
            self.logger.error(f"分析性能趋势失败: {e}")
            return {'error': str(e)}

    def _count_recent_failures(self, df: pd.DataFrame, days: int = 7) -> int:
        """统计最近失败次数"""
        try:
            recent_date = datetime.now() - timedelta(days=days)
            recent_df = df[pd.to_datetime(df['start_time']) > recent_date]
            return len(recent_df[recent_df['status'] == 'failed'])
        except Exception as e:
            self.logger.error(f"统计最近失败次数失败: {e}")
            return 0

    def _analyze_current_performance(self, context: DecisionContext) -> Dict[str, Any]:
        """分析当前性能"""
        try:
            current_perf = context.current_performance

            # 计算加权性能分数
            weighted_score = 0.0
            total_weight = 0.0

            for metric, value in current_perf.items():
                weight = self.performance_weights.get(metric, 0.1)

                # 对于误差类指标，需要反转
                if metric.lower() in ['mae', 'rmse', 'error', 'response_time']:
                    # 假设较小的值更好，转换为0-1分数
                    normalized_value = max(0, 1 - (value / 10))  # 简单归一化
                else:
                    # 假设较大的值更好
                    normalized_value = min(1, value)

                weighted_score += normalized_value * weight
                total_weight += weight

            if total_weight > 0:
                overall_score = weighted_score / total_weight
            else:
                overall_score = 0.5  # 默认中等分数

            # 确定性能等级
            if overall_score >= 0.8:
                performance_level = 'excellent'
            elif overall_score >= 0.6:
                performance_level = 'good'
            elif overall_score >= 0.4:
                performance_level = 'fair'
            else:
                performance_level = 'poor'

            return {
                'overall_score': overall_score,
                'performance_level': performance_level,
                'metrics': current_perf,
                'weighted_metrics': {
                    metric: value * self.performance_weights.get(metric, 0.1)
                    for metric, value in current_perf.items()
                }
            }

        except Exception as e:
            self.logger.error(f"分析当前性能失败: {e}")
            return {'overall_score': 0.5, 'performance_level': 'unknown', 'error': str(e)}

    def _evaluate_optimization_risk(self, context: DecisionContext) -> Dict[str, Any]:
        """评估优化风险"""
        try:
            risk_scores = {}

            # 性能波动风险
            current_perf = context.current_performance
            if current_perf:
                volatility = np.std(list(current_perf.values())) if len(current_perf) > 1 else 0.0
                risk_scores['performance_volatility'] = min(1.0, volatility)
            else:
                risk_scores['performance_volatility'] = 0.5

            # 最近失败风险
            historical_data = context.historical_data
            recent_failures = historical_data.get('recent_failures', 0)
            risk_scores['recent_failures'] = min(1.0, recent_failures / 5)  # 5次失败为最高风险

            # 资源使用风险（模拟）
            risk_scores['resource_usage'] = 0.3  # 假设中等风险

            # 系统稳定性风险
            success_rates = historical_data.get('success_rates', {})
            recent_success_rate = success_rates.get('recent_7_days', 0.5)
            risk_scores['system_stability'] = 1.0 - recent_success_rate

            # 外部因素风险
            risk_scores['external_factors'] = 0.2  # 假设低风险

            # 计算加权风险分数
            total_risk = sum(
                risk_scores[factor] * weight
                for factor, weight in self.risk_factors.items()
                if factor in risk_scores
            )

            # 确定风险级别
            if total_risk >= 0.8:
                risk_level = RiskLevel.CRITICAL
            elif total_risk >= 0.6:
                risk_level = RiskLevel.HIGH
            elif total_risk >= 0.4:
                risk_level = RiskLevel.MEDIUM
            else:
                risk_level = RiskLevel.LOW

            return {
                'total_risk': total_risk,
                'risk_level': risk_level.value,
                'risk_factors': risk_scores,
                'risk_tolerance': context.risk_tolerance.value,
                'recommendation': self._get_risk_recommendation(risk_level, context.risk_tolerance)
            }

        except Exception as e:
            self.logger.error(f"评估优化风险失败: {e}")
            return {
                'total_risk': 0.5,
                'risk_level': RiskLevel.MEDIUM.value,
                'error': str(e)
            }

    def _get_risk_recommendation(self, risk_level: RiskLevel, risk_tolerance: RiskLevel) -> str:
        """获取风险建议"""
        if risk_level == RiskLevel.CRITICAL:
            return "风险极高，建议暂停优化操作"
        elif risk_level == RiskLevel.HIGH:
            if risk_tolerance in [RiskLevel.LOW, RiskLevel.MEDIUM]:
                return "风险较高，建议采用保守策略"
            else:
                return "风险较高，可谨慎进行优化"
        elif risk_level == RiskLevel.MEDIUM:
            return "风险适中，可正常进行优化"
        else:
            return "风险较低，可积极进行优化"

    def _generate_candidate_strategies(self, context: DecisionContext,
                                     historical_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成候选策略"""
        strategies = []

        try:
            decision_type = context.decision_type
            current_perf = context.current_performance

            if decision_type == DecisionType.OPTIMIZATION_STRATEGY:
                # 基于历史成功率生成策略
                patterns = historical_analysis.get('patterns', {})

                # 策略1：基于最成功的优化类型
                best_opt_type = None
                best_success_rate = 0

                for opt_type, pattern in patterns.items():
                    if isinstance(pattern, dict) and pattern.get('success_rate', 0) > best_success_rate:
                        best_success_rate = pattern['success_rate']
                        best_opt_type = opt_type

                if best_opt_type:
                    strategies.append({
                        'action': f'execute_{best_opt_type}',
                        'reasoning': f'基于历史数据，{best_opt_type}的成功率最高({best_success_rate:.2f})',
                        'expected_impact': {'success_probability': best_success_rate},
                        'priority': 1
                    })

                # 策略2：保守策略
                strategies.append({
                    'action': 'gradual_optimization',
                    'reasoning': '采用渐进式优化，降低风险',
                    'expected_impact': {'success_probability': 0.7, 'improvement': 0.02},
                    'priority': 2
                })

                # 策略3：激进策略
                strategies.append({
                    'action': 'comprehensive_optimization',
                    'reasoning': '全面优化，追求最大改进',
                    'expected_impact': {'success_probability': 0.5, 'improvement': 0.1},
                    'priority': 3
                })

            elif decision_type == DecisionType.PARAMETER_ADJUSTMENT:
                # 参数调整策略
                strategies.extend([
                    {
                        'action': 'fine_tune_parameters',
                        'reasoning': '微调参数，保持稳定性',
                        'expected_impact': {'improvement': 0.03},
                        'priority': 1
                    },
                    {
                        'action': 'reset_to_baseline',
                        'reasoning': '重置到基线参数',
                        'expected_impact': {'stability': 0.9},
                        'priority': 2
                    }
                ])

            # 默认策略：维持现状
            strategies.append({
                'action': 'maintain_current_state',
                'reasoning': '维持当前状态，避免风险',
                'expected_impact': {'stability': 1.0},
                'priority': 999
            })

            # 按优先级排序
            strategies.sort(key=lambda x: x.get('priority', 999))

            return strategies

        except Exception as e:
            self.logger.error(f"生成候选策略失败: {e}")
            return [{
                'action': 'maintain_current_state',
                'reasoning': f'策略生成失败: {e}',
                'expected_impact': {},
                'priority': 999
            }]

    def _select_best_strategy(self, strategies: List[Dict[str, Any]],
                            performance_analysis: Dict[str, Any],
                            risk_assessment: Dict[str, Any],
                            context: DecisionContext) -> Dict[str, Any]:
        """选择最佳策略"""
        try:
            if not strategies:
                return {
                    'action': 'maintain_current_state',
                    'reasoning': '无可用策略',
                    'expected_impact': {}
                }

            # 计算每个策略的综合评分
            scored_strategies = []

            for strategy in strategies:
                score = self._calculate_strategy_score(
                    strategy, performance_analysis, risk_assessment, context
                )
                strategy['score'] = score
                scored_strategies.append(strategy)

            # 选择评分最高的策略
            best_strategy = max(scored_strategies, key=lambda x: x.get('score', 0))

            return best_strategy

        except Exception as e:
            self.logger.error(f"选择最佳策略失败: {e}")
            return {
                'action': 'maintain_current_state',
                'reasoning': f'策略选择失败: {e}',
                'expected_impact': {}
            }

    def _calculate_strategy_score(self, strategy: Dict[str, Any],
                                performance_analysis: Dict[str, Any],
                                risk_assessment: Dict[str, Any],
                                context: DecisionContext) -> float:
        """计算策略评分"""
        try:
            # 基础分数
            base_score = 0.5

            # 性能改进潜力
            expected_impact = strategy.get('expected_impact', {})
            improvement_potential = expected_impact.get('improvement', 0.0)
            success_probability = expected_impact.get('success_probability', 0.5)

            # 性能分数
            performance_score = improvement_potential * success_probability

            # 风险调整
            total_risk = risk_assessment.get('total_risk', 0.5)
            risk_penalty = total_risk * self.decision_config['risk_weight']

            # 当前性能状况调整
            current_performance_level = performance_analysis.get('performance_level', 'fair')
            if current_performance_level == 'poor':
                urgency_bonus = 0.2  # 性能差时更积极
            elif current_performance_level == 'excellent':
                urgency_bonus = -0.1  # 性能好时更保守
            else:
                urgency_bonus = 0.0

            # 综合评分
            final_score = (
                base_score +
                performance_score * self.decision_config['performance_weight'] -
                risk_penalty +
                urgency_bonus
            )

            return max(0.0, min(1.0, final_score))

        except Exception as e:
            self.logger.error(f"计算策略评分失败: {e}")
            return 0.0

    def _calculate_decision_confidence(self, strategy: Dict[str, Any],
                                     historical_analysis: Dict[str, Any],
                                     risk_assessment: Dict[str, Any]) -> float:
        """计算决策置信度"""
        try:
            # 基础置信度
            base_confidence = 0.5

            # 历史数据充分性
            total_optimizations = historical_analysis.get('total_optimizations', 0)
            data_confidence = min(1.0, total_optimizations / self.decision_config['min_samples'])

            # 策略成功概率
            strategy_confidence = strategy.get('expected_impact', {}).get('success_probability', 0.5)

            # 风险调整
            total_risk = risk_assessment.get('total_risk', 0.5)
            risk_confidence = 1.0 - total_risk

            # 综合置信度
            confidence = (
                base_confidence * 0.2 +
                data_confidence * 0.3 +
                strategy_confidence * 0.3 +
                risk_confidence * 0.2
            )

            return max(0.0, min(1.0, confidence))

        except Exception as e:
            self.logger.error(f"计算决策置信度失败: {e}")
            return 0.0

    def _record_decision(self, context: DecisionContext, result: DecisionResult):
        """记录决策历史"""
        try:
            decision_record = {
                'context': {
                    'decision_type': context.decision_type.value,
                    'current_performance': context.current_performance,
                    'objectives': context.objectives,
                    'risk_tolerance': context.risk_tolerance.value,
                    'timestamp': context.timestamp.isoformat()
                },
                'result': {
                    'recommended_action': result.recommended_action,
                    'confidence': result.confidence,
                    'risk_assessment': result.risk_assessment,
                    'expected_impact': result.expected_impact,
                    'reasoning': result.reasoning,
                    'timestamp': result.timestamp.isoformat()
                }
            }

            self.decision_history.append(decision_record)

            # 保持历史记录在合理范围内
            if len(self.decision_history) > 100:
                self.decision_history = self.decision_history[-100:]

            self.logger.debug(f"决策记录已保存: {result.recommended_action}")

        except Exception as e:
            self.logger.error(f"记录决策历史失败: {e}")

    def get_decision_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取决策历史"""
        try:
            return self.decision_history[-limit:] if self.decision_history else []
        except Exception as e:
            self.logger.error(f"获取决策历史失败: {e}")
            return []

    def evaluate_decision_effectiveness(self, decision_id: str,
                                      actual_outcome: Dict[str, Any]) -> Dict[str, Any]:
        """评估决策有效性"""
        try:
            # 这里可以实现决策效果评估逻辑
            # 比较预期结果和实际结果

            return {
                'decision_id': decision_id,
                'evaluation': 'effective',  # 简化实现
                'actual_outcome': actual_outcome,
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"评估决策有效性失败: {e}")
            return {'error': str(e)}
