"""
百位专用特征生成器
为百位预测器提供50+维专用特征，包括：
- 频次特征：出现频率、遗漏次数、连续性分析
- 冷热度特征：冷热状态、温度变化、周期性分析
- 趋势特征：上升下降趋势、变化幅度、稳定性
- 统计特征：均值、方差、偏度、峰度
- 关联特征：与十位个位的关联度、组合模式
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional
from collections import Counter, deque


def generate_hundreds_features(df: pd.DataFrame, window_sizes: List[int] = [5, 10, 20, 50]) -> pd.DataFrame:
    """
    生成百位专用特征
    
    Args:
        df: 包含历史数据的DataFrame，必须包含'hundreds'列
        window_sizes: 滑动窗口大小列表
        
    Returns:
        pd.DataFrame: 包含百位专用特征的DataFrame
    """
    if 'hundreds' not in df.columns:
        raise ValueError("DataFrame必须包含'hundreds'列")
    
    result_df = df.copy()
    hundreds_series = df['hundreds']
    
    # 1. 基础频次特征
    result_df.update(_generate_frequency_features(hundreds_series, window_sizes))
    
    # 2. 遗漏分析特征
    result_df.update(_generate_missing_features(hundreds_series, window_sizes))
    
    # 3. 冷热度特征
    result_df.update(_generate_hot_cold_features(hundreds_series, window_sizes))
    
    # 4. 趋势分析特征
    result_df.update(_generate_trend_features(hundreds_series, window_sizes))
    
    # 5. 统计分布特征
    result_df.update(_generate_statistical_features(hundreds_series, window_sizes))
    
    # 6. 周期性特征
    result_df.update(_generate_periodic_features(hundreds_series))
    
    # 7. 关联性特征（如果存在十位和个位数据）
    if 'tens' in df.columns and 'units' in df.columns:
        result_df.update(_generate_correlation_features(df[['hundreds', 'tens', 'units']], window_sizes))
    
    return result_df


def _generate_frequency_features(series: pd.Series, window_sizes: List[int]) -> pd.DataFrame:
    """生成频次特征"""
    features = pd.DataFrame(index=series.index)
    
    for window in window_sizes:
        # 滑动窗口频次统计
        freq_counts = series.rolling(window=window, min_periods=1).apply(
            lambda x: Counter(x)[x.iloc[-1]] if len(x) > 0 else 0
        )
        features[f'hundreds_freq_{window}'] = freq_counts
        
        # 频次排名
        features[f'hundreds_freq_rank_{window}'] = freq_counts.rolling(window=window).rank(pct=True)
        
        # 频次变化率
        features[f'hundreds_freq_change_{window}'] = freq_counts.pct_change()
    
    # 全局频次特征
    global_counts = series.value_counts()
    features['hundreds_global_freq'] = series.map(global_counts)
    features['hundreds_global_freq_rank'] = features['hundreds_global_freq'].rank(pct=True)
    
    return features


def _generate_missing_features(series: pd.Series, window_sizes: List[int]) -> pd.DataFrame:
    """生成遗漏分析特征"""
    features = pd.DataFrame(index=series.index)
    
    # 计算每个数字的遗漏次数
    for digit in range(10):
        missing_counts = []
        current_missing = 0
        
        for value in series:
            if value == digit:
                missing_counts.append(current_missing)
                current_missing = 0
            else:
                current_missing += 1
                missing_counts.append(current_missing)
        
        features[f'hundreds_missing_{digit}'] = missing_counts
    
    # 当前数字的遗漏特征
    current_missing = []
    for i, value in enumerate(series):
        if i == 0:
            current_missing.append(0)
        else:
            # 计算当前数字的遗漏次数
            missing = 0
            for j in range(i-1, -1, -1):
                if series.iloc[j] == value:
                    break
                missing += 1
            current_missing.append(missing)
    
    features['hundreds_current_missing'] = current_missing
    
    # 遗漏统计特征
    for window in window_sizes:
        features[f'hundreds_avg_missing_{window}'] = features['hundreds_current_missing'].rolling(window).mean()
        features[f'hundreds_max_missing_{window}'] = features['hundreds_current_missing'].rolling(window).max()
        features[f'hundreds_min_missing_{window}'] = features['hundreds_current_missing'].rolling(window).min()
    
    return features


def _generate_hot_cold_features(series: pd.Series, window_sizes: List[int]) -> pd.DataFrame:
    """生成冷热度特征"""
    features = pd.DataFrame(index=series.index)
    
    for window in window_sizes:
        # 计算窗口内的出现频次
        window_freq = series.rolling(window=window, min_periods=1).apply(
            lambda x: Counter(x)[x.iloc[-1]] if len(x) > 0 else 0
        )
        
        # 计算平均频次
        avg_freq = window_freq.rolling(window=window).mean()
        
        # 冷热度分类
        hot_threshold = avg_freq.quantile(0.7)
        cold_threshold = avg_freq.quantile(0.3)
        
        def classify_hot_cold(freq, avg, hot_th, cold_th):
            if freq > hot_th:
                return 'hot'
            elif freq < cold_th:
                return 'cold'
            else:
                return 'normal'
        
        features[f'hundreds_hot_cold_{window}'] = window_freq.combine(
            avg_freq, lambda f, a: classify_hot_cold(f, a, hot_threshold, cold_threshold)
        )
        
        # 冷热度数值
        features[f'hundreds_hot_cold_score_{window}'] = (window_freq - avg_freq) / (avg_freq + 1e-8)
    
    return features


def _generate_trend_features(series: pd.Series, window_sizes: List[int]) -> pd.DataFrame:
    """生成趋势分析特征"""
    features = pd.DataFrame(index=series.index)
    
    for window in window_sizes:
        # 数值趋势（将数字作为数值处理）
        features[f'hundreds_trend_{window}'] = series.rolling(window=window).apply(
            lambda x: np.polyfit(range(len(x)), x, 1)[0] if len(x) > 1 else 0
        )
        
        # 变化幅度
        features[f'hundreds_volatility_{window}'] = series.rolling(window=window).std()
        
        # 上升下降趋势
        features[f'hundreds_increasing_{window}'] = (series > series.shift(1)).rolling(window=window).sum()
        features[f'hundreds_decreasing_{window}'] = (series < series.shift(1)).rolling(window=window).sum()
        
        # 稳定性指标
        features[f'hundreds_stability_{window}'] = 1 / (1 + features[f'hundreds_volatility_{window}'])
    
    return features


def _generate_statistical_features(series: pd.Series, window_sizes: List[int]) -> pd.DataFrame:
    """生成统计分布特征"""
    features = pd.DataFrame(index=series.index)
    
    for window in window_sizes:
        # 基础统计量
        features[f'hundreds_mean_{window}'] = series.rolling(window=window).mean()
        features[f'hundreds_std_{window}'] = series.rolling(window=window).std()
        features[f'hundreds_min_{window}'] = series.rolling(window=window).min()
        features[f'hundreds_max_{window}'] = series.rolling(window=window).max()
        features[f'hundreds_median_{window}'] = series.rolling(window=window).median()
        
        # 分位数
        features[f'hundreds_q25_{window}'] = series.rolling(window=window).quantile(0.25)
        features[f'hundreds_q75_{window}'] = series.rolling(window=window).quantile(0.75)
        
        # 偏度和峰度
        features[f'hundreds_skew_{window}'] = series.rolling(window=window).skew()
        features[f'hundreds_kurt_{window}'] = series.rolling(window=window).kurt()
        
        # 与均值的偏差
        features[f'hundreds_deviation_{window}'] = series - features[f'hundreds_mean_{window}']
        features[f'hundreds_abs_deviation_{window}'] = np.abs(features[f'hundreds_deviation_{window}'])
    
    return features


def _generate_periodic_features(series: pd.Series) -> pd.DataFrame:
    """生成周期性特征"""
    features = pd.DataFrame(index=series.index)
    
    # 周期性分析（7天、30天周期）
    for period in [7, 30]:
        if len(series) > period:
            # 周期内的位置
            features[f'hundreds_period_pos_{period}'] = series.index % period
            
            # 周期性相关性
            shifted_series = series.shift(period)
            features[f'hundreds_period_corr_{period}'] = series.rolling(window=period*2).corr(shifted_series)
            
            # 周期性差异
            features[f'hundreds_period_diff_{period}'] = series - shifted_series
    
    # 星期几特征（如果有日期信息）
    if hasattr(series.index, 'dayofweek'):
        features['hundreds_dayofweek'] = series.index.dayofweek
        features['hundreds_is_weekend'] = (series.index.dayofweek >= 5).astype(int)
    
    return features


def _generate_correlation_features(df: pd.DataFrame, window_sizes: List[int]) -> pd.DataFrame:
    """生成关联性特征"""
    features = pd.DataFrame(index=df.index)
    
    hundreds = df['hundreds']
    tens = df['tens']
    units = df['units']
    
    for window in window_sizes:
        # 与十位的相关性
        features[f'hundreds_tens_corr_{window}'] = hundreds.rolling(window=window).corr(tens)
        
        # 与个位的相关性
        features[f'hundreds_units_corr_{window}'] = hundreds.rolling(window=window).corr(units)
        
        # 三位数字的组合特征
        combination = hundreds * 100 + tens * 10 + units
        features[f'hundreds_combination_freq_{window}'] = combination.rolling(window=window).apply(
            lambda x: Counter(x)[x.iloc[-1]] if len(x) > 0 else 0
        )
        
        # 数字间的差值特征
        features[f'hundreds_tens_diff_{window}'] = (hundreds - tens).rolling(window=window).mean()
        features[f'hundreds_units_diff_{window}'] = (hundreds - units).rolling(window=window).mean()
        
        # 数字间的比值特征
        features[f'hundreds_tens_ratio_{window}'] = (hundreds / (tens + 1e-8)).rolling(window=window).mean()
        features[f'hundreds_units_ratio_{window}'] = (hundreds / (units + 1e-8)).rolling(window=window).mean()
    
    return features
