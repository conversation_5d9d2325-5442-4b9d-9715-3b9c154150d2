# P8-智能交集融合系统

## 项目概述
**前置条件**：P6-P7预测器完成 ✅
**核心目标**：融合所有预测器结果，生成最终预测
**预计时间**：2-3周
**项目状态**：准备开发

## 🔗 P6-P7衔接分析

### 已完成的预测器接口
- **P3-P5位置预测器**：提供`predict_probability()`方法，返回10维概率分布
- **P6和值预测器**：提供`predict()`和`predict_with_confidence()`方法
- **P7跨度预测器**：提供`predict_probability()`和`predict()`方法

### 发现的接口问题
1. **P6和值预测器缺少概率分布接口**：需要添加`predict_probability()`方法
2. **数据格式不统一**：不同预测器返回格式差异较大
3. **约束信息获取不明确**：需要标准化约束信息接口

## 技术要求

### 融合目标
- **输入**：各预测器的标准化概率分布和数值预测
- **输出**：排序的号码推荐列表（Top 10-20）
- **约束**：确保预测结果符合彩票规则和约束条件
- **优化**：智能权重分配和多维度约束优化

### 系统架构
- **预测器接口统一层**：标准化所有预测器的输入输出接口
- **概率融合引擎**：融合P3-P7的概率分布和数值预测
- **约束优化器**：应用和值跨度约束和一致性检验
- **智能排序器**：基于综合概率和约束分数排序
- **一致性检验器**：验证预测结果的数学合理性
- **动态权重调整器**：根据历史表现自动调整融合权重
- **性能评估系统**：实时监控和评估预测效果

## 🔧 阶段1：接口优化和统一

### 1.1 P6和值预测器接口补充
需要为P6和值预测器添加概率分布接口：

```python
# 在SumPredictor类中添加
def predict_probability(self, X: np.ndarray) -> np.ndarray:
    """
    预测和值概率分布（适配融合系统接口）

    Args:
        X: 特征矩阵

    Returns:
        概率分布数组，shape: (n_samples, 28) # 和值0-27
    """
    if not self.is_trained:
        raise ValueError("模型尚未训练")

    try:
        # 使用分布模型预测概率分布
        if 'distribution' in self.models and self.models['distribution'].is_trained:
            probabilities = self.models['distribution'].predict_distribution(X)
            return probabilities
        else:
            # 如果分布模型不可用，使用回归模型转换为概率
            predictions = self.predict(X)
            probabilities = self._convert_regression_to_probability(predictions)
            return probabilities
    except Exception as e:
        self.logger.error(f"预测概率分布失败: {e}")
        raise

def _convert_regression_to_probability(self, predictions: np.ndarray) -> np.ndarray:
    """将回归预测转换为概率分布"""
    probabilities = []
    for pred in predictions:
        # 创建以预测值为中心的正态分布
        prob_dist = np.zeros(28)  # 和值0-27
        center = int(np.clip(pred, 0, 27))

        # 使用高斯分布
        for i in range(28):
            prob_dist[i] = np.exp(-0.5 * ((i - pred) / 2.0) ** 2)

        # 归一化
        prob_dist = prob_dist / np.sum(prob_dist)
        probabilities.append(prob_dist)

    return np.array(probabilities)
```

### 1.2 统一预测接口
创建统一的预测器管理接口：

```python
class UnifiedPredictorInterface:
    """统一预测器接口管理器"""

    def __init__(self, db_path: str):
        self.db_path = db_path
        self.predictors = {}
        self.logger = logging.getLogger(__name__)

    def load_all_predictors(self):
        """加载所有预测器"""
        from src.predictors.hundreds_predictor import HundredsPredictor
        from src.predictors.tens_predictor import TensPredictor
        from src.predictors.units_predictor import UnitsPredictor
        from src.predictors.sum_predictor import SumPredictor
        from src.predictors.span_predictor import SpanPredictor

        self.predictors = {
            'hundreds': HundredsPredictor(self.db_path),
            'tens': TensPredictor(self.db_path),
            'units': UnitsPredictor(self.db_path),
            'sum': SumPredictor(self.db_path),
            'span': SpanPredictor(self.db_path)
        }

        self.logger.info("所有预测器加载完成")

    def get_all_predictions(self, issue: str) -> Dict[str, Any]:
        """获取所有预测器的标准化预测结果"""
        predictions = {}

        for name, predictor in self.predictors.items():
            try:
                if name in ['hundreds', 'tens', 'units']:
                    # 位置预测器
                    result = predictor.predict_next_period(issue)
                    predictions[name] = {
                        'probabilities': result['probabilities'],
                        'predicted_digit': result['predicted_digit'],
                        'confidence': result['confidence']
                    }
                elif name == 'sum':
                    # 和值预测器
                    features = predictor._build_features_for_prediction()
                    sum_pred = predictor.predict(features)[0]
                    sum_probs = predictor.predict_probability(features)[0]

                    predictions[name] = {
                        'predicted_sum': float(sum_pred),
                        'probabilities': sum_probs.tolist(),
                        'confidence': float(np.max(sum_probs))
                    }
                elif name == 'span':
                    # 跨度预测器
                    result = predictor.predict_next_period(issue)
                    predictions[name] = {
                        'predicted_span': result.get('optimized_prediction', result.get('predicted_span')),
                        'probabilities': result.get('probabilities', []),
                        'confidence': result.get('confidence', 0.5)
                    }

            except Exception as e:
                self.logger.error(f"{name}预测器预测失败: {e}")
                predictions[name] = {'error': str(e)}

        return predictions
```

## 🗄️ 阶段3：数据库扩展

### 3.1 最终预测结果表
```sql
-- 最终预测结果表（优化版）
CREATE TABLE final_predictions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    issue TEXT NOT NULL,
    prediction_rank INTEGER NOT NULL,   -- 推荐排名
    hundreds INTEGER NOT NULL,
    tens INTEGER NOT NULL,
    units INTEGER NOT NULL,
    sum_value INTEGER NOT NULL,
    span_value INTEGER NOT NULL,

    -- 概率信息
    combined_probability REAL NOT NULL, -- 综合概率
    hundreds_prob REAL NOT NULL,        -- 百位概率
    tens_prob REAL NOT NULL,            -- 十位概率
    units_prob REAL NOT NULL,           -- 个位概率
    sum_prob REAL NOT NULL,             -- 和值概率
    span_prob REAL NOT NULL,            -- 跨度概率

    -- 一致性分数
    sum_consistency REAL NOT NULL,      -- 和值一致性
    span_consistency REAL NOT NULL,     -- 跨度一致性
    constraint_score REAL NOT NULL,     -- 约束分数
    diversity_score REAL NOT NULL,      -- 多样性分数

    -- 元数据
    confidence_level TEXT,              -- 置信水平(high/medium/low)
    fusion_method TEXT,                 -- 融合方法
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 索引
    UNIQUE(issue, prediction_rank)
);

### 3.2 融合权重配置表
```sql
-- 融合权重配置表（优化版）
CREATE TABLE fusion_weights (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    weight_type TEXT NOT NULL,          -- position/sum/span/constraint/diversity
    predictor_name TEXT NOT NULL,       -- 预测器名称(hundreds/tens/units/sum/span)
    model_name TEXT,                    -- 具体模型名称(xgb/lgb/lstm/ensemble等)
    weight_value REAL NOT NULL,         -- 权重值
    performance_score REAL,             -- 性能分数
    accuracy_rate REAL,                 -- 准确率
    confidence_score REAL,              -- 置信度分数
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,

    -- 索引
    UNIQUE(weight_type, predictor_name, model_name)
);

### 3.3 预测性能评估表
```sql
-- 预测性能评估表（优化版）
CREATE TABLE prediction_performance (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    issue TEXT NOT NULL,

    -- 实际结果
    actual_hundreds INTEGER,
    actual_tens INTEGER,
    actual_units INTEGER,
    actual_sum INTEGER,
    actual_span INTEGER,

    -- 预测结果评估
    predicted_rank INTEGER,             -- 实际号码在预测中的排名
    hit_type TEXT,                      -- 命中类型(exact/position/partial/none)

    -- 各位置准确性
    hundreds_accuracy BOOLEAN,
    tens_accuracy BOOLEAN,
    units_accuracy BOOLEAN,
    sum_accuracy BOOLEAN,
    span_accuracy BOOLEAN,

    -- 评分指标
    overall_score REAL,                 -- 综合评分
    probability_score REAL,             -- 概率预测评分
    constraint_score REAL,              -- 约束一致性评分
    diversity_effectiveness REAL,       -- 多样性有效性

    -- 融合系统特定指标
    fusion_method TEXT,                 -- 使用的融合方法
    top_k_hit INTEGER,                  -- Top-K命中情况
    confidence_accuracy REAL,           -- 置信度准确性

    evaluated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 索引
    INDEX idx_issue (issue),
    INDEX idx_hit_type (hit_type),
    INDEX idx_evaluated_at (evaluated_at)
);

### 3.4 约束规则表
```sql
-- 约束规则表（优化版）
CREATE TABLE fusion_constraint_rules (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    rule_name TEXT NOT NULL,
    rule_type TEXT NOT NULL,            -- sum_span/probability/consistency/diversity
    rule_expression TEXT NOT NULL,      -- 规则表达式
    weight REAL DEFAULT 1.0,
    tolerance REAL DEFAULT 0.1,         -- 容差范围
    is_active BOOLEAN DEFAULT TRUE,
    priority INTEGER DEFAULT 1,         -- 规则优先级
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 索引
    INDEX idx_rule_type (rule_type),
    INDEX idx_is_active (is_active)
);

-- 融合会话记录表
CREATE TABLE fusion_sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_id TEXT NOT NULL,
    issue TEXT NOT NULL,
    fusion_method TEXT NOT NULL,
    input_data TEXT NOT NULL,           -- JSON格式的输入数据
    output_data TEXT NOT NULL,          -- JSON格式的输出结果
    execution_time REAL,                -- 执行时间(秒)
    success BOOLEAN DEFAULT TRUE,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 索引
    INDEX idx_session_id (session_id),
    INDEX idx_issue (issue),
    INDEX idx_created_at (created_at)
);
```

## 🚀 阶段2：融合引擎开发

### 2.1 概率融合算法（优化版）
```python
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
import sqlite3
import json
from itertools import product
from scipy.optimize import minimize
import logging
from datetime import datetime
import uuid

class ProbabilityFusionEngine:
    """概率融合引擎 - P8核心组件"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.fusion_methods = {
            'weighted_product': self._weighted_product_fusion,
            'weighted_average': self._weighted_average_fusion,
            'bayesian_fusion': self._bayesian_fusion,
            'adaptive_fusion': self._adaptive_fusion
        }

        # 默认权重配置（基于P6-P7实际性能）
        self.default_weights = {
            'hundreds': 1.0,
            'tens': 1.0,
            'units': 1.0,
            'sum': 0.8,      # 和值约束权重
            'span': 0.6,     # 跨度约束权重
            'constraint': 0.4 # 一致性约束权重
        }
        
        # 约束参数
        self.constraint_params = {
            'sum_tolerance': 2.0,      # 和值容差
            'span_tolerance': 1.0,     # 跨度容差
            'min_probability': 1e-6,   # 最小概率阈值
            'max_recommendations': 20   # 最大推荐数量
        }
        
        self.logger = logging.getLogger(__name__)
    
    def load_fusion_weights(self):
        """加载融合权重配置"""
        conn = sqlite3.connect(self.db_path)
        
        query = """
            SELECT weight_type, model_name, weight_value, performance_score
            FROM fusion_weights
            WHERE is_active = TRUE
            ORDER BY weight_type, model_name
        """
        
        df = pd.read_sql_query(query, conn)
        
        if not df.empty:
            weights = {}
            for _, row in df.iterrows():
                weight_type = row['weight_type']
                model_name = row['model_name']
                weight_value = row['weight_value']
                
                if weight_type not in weights:
                    weights[weight_type] = {}
                weights[weight_type][model_name] = weight_value
            
            self.fusion_weights = weights
        else:
            self.fusion_weights = self.default_weights
        
        conn.close()
        return self.fusion_weights
    
    def load_constraint_rules(self):
        """加载约束规则"""
        conn = sqlite3.connect(self.db_path)
        
        query = """
            SELECT rule_name, rule_type, rule_expression, weight
            FROM fusion_constraint_rules
            WHERE is_active = TRUE
        """
        
        df = pd.read_sql_query(query, conn)
        self.constraint_rules = df.to_dict('records')
        
        conn.close()
        return self.constraint_rules
    
    def generate_all_combinations(self, hundreds_prob: np.ndarray, 
                                tens_prob: np.ndarray, 
                                units_prob: np.ndarray) -> List[Dict]:
        """生成所有可能的号码组合"""
        combinations = []
        
        for h in range(10):
            for t in range(10):
                for u in range(10):
                    # 计算基础概率
                    base_probability = hundreds_prob[h] * tens_prob[t] * units_prob[u]
                    
                    # 计算和值和跨度
                    sum_value = h + t + u
                    span_value = max(h, t, u) - min(h, t, u)
                    
                    combination = {
                        'hundreds': h,
                        'tens': t,
                        'units': u,
                        'sum_value': sum_value,
                        'span_value': span_value,
                        'base_probability': base_probability,
                        'hundreds_prob': hundreds_prob[h],
                        'tens_prob': tens_prob[t],
                        'units_prob': units_prob[u]
                    }
                    
                    combinations.append(combination)
        
        return combinations
    
    def apply_auxiliary_constraints(self, combinations: List[Dict], 
                                  predicted_sum: float, 
                                  predicted_span: float) -> List[Dict]:
        """应用和值跨度约束"""
        constrained_combinations = []
        
        for combo in combinations:
            # 计算和值一致性
            sum_diff = abs(combo['sum_value'] - predicted_sum)
            sum_consistency = max(0, 1 - sum_diff / self.constraint_params['sum_tolerance'])
            
            # 计算跨度一致性
            span_diff = abs(combo['span_value'] - predicted_span)
            span_consistency = max(0, 1 - span_diff / self.constraint_params['span_tolerance'])
            
            # 只保留满足基本约束的组合
            if (sum_diff <= self.constraint_params['sum_tolerance'] and 
                span_diff <= self.constraint_params['span_tolerance']):
                
                combo['sum_consistency'] = sum_consistency
                combo['span_consistency'] = span_consistency
                constrained_combinations.append(combo)
        
        return constrained_combinations
    
    def calculate_combined_probability(self, combination: Dict, 
                                     auxiliary_weights: Dict) -> float:
        """计算综合概率"""
        # 基础位置概率
        position_prob = combination['base_probability']
        
        # 和值一致性权重
        sum_weight = auxiliary_weights.get('sum', 0.4)
        sum_contribution = sum_weight * combination['sum_consistency']
        
        # 跨度一致性权重
        span_weight = auxiliary_weights.get('span', 0.3)
        span_contribution = span_weight * combination['span_consistency']
        
        # 约束权重
        constraint_weight = auxiliary_weights.get('constraint', 0.3)
        constraint_score = (combination['sum_consistency'] + combination['span_consistency']) / 2
        constraint_contribution = constraint_weight * constraint_score
        
        # 综合概率计算
        combined_probability = (
            position_prob * 
            (1 + sum_contribution + span_contribution + constraint_contribution)
        )
        
        return combined_probability
    
    def intelligent_ranking(self, combinations: List[Dict]) -> List[Dict]:
        """智能排序算法"""
        if not combinations:
            return []
        
        # 加载权重配置
        if self.fusion_weights is None:
            self.load_fusion_weights()
        
        auxiliary_weights = self.fusion_weights.get('auxiliary', self.default_weights['auxiliary'])
        
        # 计算综合概率
        for combo in combinations:
            combo['combined_probability'] = self.calculate_combined_probability(
                combo, auxiliary_weights
            )
            
            # 计算约束分数
            combo['constraint_score'] = (
                combo['sum_consistency'] + combo['span_consistency']
            ) / 2
        
        # 按综合概率排序
        sorted_combinations = sorted(
            combinations, 
            key=lambda x: x['combined_probability'], 
            reverse=True
        )
        
        # 添加排名信息
        for i, combo in enumerate(sorted_combinations):
            combo['rank'] = i + 1
            
            # 确定置信水平
            if combo['combined_probability'] > 0.001:
                combo['confidence_level'] = 'high'
            elif combo['combined_probability'] > 0.0005:
                combo['confidence_level'] = 'medium'
            else:
                combo['confidence_level'] = 'low'
        
        return sorted_combinations[:self.constraint_params['max_recommendations']]
    
    def diversity_optimization(self, combinations: List[Dict]) -> List[Dict]:
        """多样性优化，避免推荐过于相似的号码"""
        if len(combinations) <= 5:
            return combinations
        
        optimized_combinations = [combinations[0]]  # 保留最高概率的
        
        for combo in combinations[1:]:
            # 检查与已选择组合的相似性
            is_diverse = True
            
            for selected in optimized_combinations:
                # 计算相似度
                similarity = self.calculate_similarity(combo, selected)
                
                # 如果相似度过高，跳过
                if similarity > 0.7:
                    is_diverse = False
                    break
            
            if is_diverse:
                optimized_combinations.append(combo)
                
                # 限制推荐数量
                if len(optimized_combinations) >= 15:
                    break
        
        return optimized_combinations
    
    def calculate_similarity(self, combo1: Dict, combo2: Dict) -> float:
        """计算两个组合的相似度"""
        # 位置相似度
        position_similarity = 0
        if combo1['hundreds'] == combo2['hundreds']:
            position_similarity += 0.33
        if combo1['tens'] == combo2['tens']:
            position_similarity += 0.33
        if combo1['units'] == combo2['units']:
            position_similarity += 0.34
        
        # 和值跨度相似度
        sum_similarity = 1 - abs(combo1['sum_value'] - combo2['sum_value']) / 27
        span_similarity = 1 - abs(combo1['span_value'] - combo2['span_value']) / 9
        
        # 综合相似度
        total_similarity = (
            0.5 * position_similarity + 
            0.25 * sum_similarity + 
            0.25 * span_similarity
        )
        
        return total_similarity
    
    def fusion_predict(self, predictions: Dict) -> List[Dict]:
        """主要融合预测方法"""
        try:
            # 提取各预测器结果
            hundreds_prob = np.array(predictions['hundreds']['probabilities'])
            tens_prob = np.array(predictions['tens']['probabilities'])
            units_prob = np.array(predictions['units']['probabilities'])
            predicted_sum = predictions['sum']['predicted_sum']
            predicted_span = predictions['span']['predicted_span']
            
            self.logger.info(f"开始融合预测 - 和值: {predicted_sum:.2f}, 跨度: {predicted_span:.2f}")
            
            # 生成所有组合
            all_combinations = self.generate_all_combinations(
                hundreds_prob, tens_prob, units_prob
            )
            
            # 应用约束
            constrained_combinations = self.apply_auxiliary_constraints(
                all_combinations, predicted_sum, predicted_span
            )
            
            self.logger.info(f"约束后组合数量: {len(constrained_combinations)}")
            
            if not constrained_combinations:
                self.logger.warning("没有满足约束的组合，放宽约束条件")
                # 放宽约束条件
                self.constraint_params['sum_tolerance'] *= 1.5
                self.constraint_params['span_tolerance'] *= 1.5
                
                constrained_combinations = self.apply_auxiliary_constraints(
                    all_combinations, predicted_sum, predicted_span
                )
            
            # 智能排序
            ranked_combinations = self.intelligent_ranking(constrained_combinations)
            
            # 多样性优化
            final_recommendations = self.diversity_optimization(ranked_combinations)
            
            self.logger.info(f"最终推荐数量: {len(final_recommendations)}")
            
            return final_recommendations
            
        except Exception as e:
            self.logger.error(f"融合预测失败: {e}")
            return []
    
    def save_predictions(self, issue: str, recommendations: List[Dict]):
        """保存预测结果"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 清除该期的旧预测
        cursor.execute("DELETE FROM final_predictions WHERE issue = ?", (issue,))
        
        # 保存新预测
        for recommendation in recommendations:
            cursor.execute("""
                INSERT INTO final_predictions 
                (issue, prediction_rank, hundreds, tens, units, sum_value, span,
                 combined_probability, hundreds_prob, tens_prob, units_prob,
                 sum_consistency, span_consistency, constraint_score, confidence_level)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                issue, recommendation['rank'], recommendation['hundreds'],
                recommendation['tens'], recommendation['units'], 
                recommendation['sum_value'], recommendation['span_value'],
                recommendation['combined_probability'], recommendation['hundreds_prob'],
                recommendation['tens_prob'], recommendation['units_prob'],
                recommendation['sum_consistency'], recommendation['span_consistency'],
                recommendation['constraint_score'], recommendation['confidence_level']
            ))
        
        conn.commit()
        conn.close()
        
        self.logger.info(f"已保存 {len(recommendations)} 个预测结果到数据库")
    
    def evaluate_predictions(self, issue: str, actual_result: Dict):
        """评估预测结果"""
        conn = sqlite3.connect(self.db_path)
        
        # 获取该期的预测结果
        query = """
            SELECT prediction_rank, hundreds, tens, units, sum_value, span,
                   combined_probability, constraint_score
            FROM final_predictions
            WHERE issue = ?
            ORDER BY prediction_rank
        """
        
        predictions_df = pd.read_sql_query(query, conn, params=(issue,))
        
        if predictions_df.empty:
            self.logger.warning(f"没有找到期号 {issue} 的预测结果")
            return
        
        actual_h = actual_result['hundreds']
        actual_t = actual_result['tens']
        actual_u = actual_result['units']
        actual_sum = actual_result['sum_value']
        actual_span = actual_result['span']
        
        # 查找实际结果在预测中的排名
        predicted_rank = None
        hit_type = 'none'
        
        for _, row in predictions_df.iterrows():
            if (row['hundreds'] == actual_h and 
                row['tens'] == actual_t and 
                row['units'] == actual_u):
                predicted_rank = row['prediction_rank']
                hit_type = 'exact'
                break
        
        # 检查位置命中
        position_hits = {
            'hundreds': any(predictions_df['hundreds'] == actual_h),
            'tens': any(predictions_df['tens'] == actual_t),
            'units': any(predictions_df['units'] == actual_u)
        }
        
        if hit_type == 'none' and any(position_hits.values()):
            hit_type = 'position'
        
        # 计算综合评分
        overall_score = self.calculate_overall_score(
            predicted_rank, hit_type, position_hits, 
            predictions_df, actual_sum, actual_span
        )
        
        # 保存评估结果
        cursor = conn.cursor()
        cursor.execute("""
            INSERT INTO prediction_performance 
            (issue, actual_hundreds, actual_tens, actual_units, actual_sum, actual_span,
             predicted_rank, hit_type, hundreds_accuracy, tens_accuracy, units_accuracy,
             sum_accuracy, span_accuracy, overall_score)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            issue, actual_h, actual_t, actual_u, actual_sum, actual_span,
            predicted_rank, hit_type, position_hits['hundreds'],
            position_hits['tens'], position_hits['units'],
            any(abs(predictions_df['sum_value'] - actual_sum) <= 1),
            any(abs(predictions_df['span'] - actual_span) <= 1),
            overall_score
        ))
        
        conn.commit()
        conn.close()
        
        self.logger.info(f"期号 {issue} 评估完成 - 命中类型: {hit_type}, 排名: {predicted_rank}, 评分: {overall_score:.3f}")
        
        return {
            'hit_type': hit_type,
            'predicted_rank': predicted_rank,
            'position_hits': position_hits,
            'overall_score': overall_score
        }
    
    def calculate_overall_score(self, predicted_rank: Optional[int], hit_type: str,
                              position_hits: Dict, predictions_df: pd.DataFrame,
                              actual_sum: int, actual_span: int) -> float:
        """计算综合评分"""
        score = 0.0
        
        # 完全命中奖励
        if hit_type == 'exact':
            if predicted_rank == 1:
                score += 100
            elif predicted_rank <= 3:
                score += 80
            elif predicted_rank <= 5:
                score += 60
            elif predicted_rank <= 10:
                score += 40
            else:
                score += 20
        
        # 位置命中奖励
        elif hit_type == 'position':
            position_score = sum(position_hits.values()) * 10
            score += position_score
        
        # 和值跨度准确性奖励
        sum_accuracy = any(abs(predictions_df['sum_value'] - actual_sum) <= 1)
        span_accuracy = any(abs(predictions_df['span'] - actual_span) <= 1)
        
        if sum_accuracy:
            score += 15
        if span_accuracy:
            score += 10
        
        return score
    
    def update_fusion_weights(self):
        """根据历史表现更新融合权重"""
        conn = sqlite3.connect(self.db_path)
        
        # 分析最近的表现
        query = """
            SELECT hit_type, overall_score, hundreds_accuracy, tens_accuracy, 
                   units_accuracy, sum_accuracy, span_accuracy
            FROM prediction_performance
            WHERE evaluated_at >= datetime('now', '-30 days')
            ORDER BY evaluated_at DESC
        """
        
        performance_df = pd.read_sql_query(query, conn)
        
        if not performance_df.empty:
            # 计算各组件的表现
            component_performance = {
                'hundreds': performance_df['hundreds_accuracy'].mean(),
                'tens': performance_df['tens_accuracy'].mean(),
                'units': performance_df['units_accuracy'].mean(),
                'sum': performance_df['sum_accuracy'].mean(),
                'span': performance_df['span_accuracy'].mean()
            }
            
            # 更新权重
            self.adjust_weights_based_on_performance(component_performance)
        
        conn.close()
    
    def adjust_weights_based_on_performance(self, performance: Dict):
        """基于表现调整权重"""
        # 位置权重调整
        position_total = sum([
            performance['hundreds'], 
            performance['tens'], 
            performance['units']
        ])
        
        if position_total > 0:
            new_position_weights = {
                'hundreds': performance['hundreds'] / position_total,
                'tens': performance['tens'] / position_total,
                'units': performance['units'] / position_total
            }
            
            # 更新权重配置
            self.fusion_weights['position'] = new_position_weights
        
        # 辅助权重调整
        auxiliary_total = performance['sum'] + performance['span']
        if auxiliary_total > 0:
            new_auxiliary_weights = {
                'sum': 0.6 * performance['sum'] / auxiliary_total,
                'span': 0.4 * performance['span'] / auxiliary_total,
                'constraint': 0.3  # 保持约束权重稳定
            }
            
            self.fusion_weights['auxiliary'] = new_auxiliary_weights
        
        # 保存更新的权重
        self.save_fusion_weights()
        
        self.logger.info(f"权重已更新: {self.fusion_weights}")
    
    def save_fusion_weights(self):
        """保存融合权重到数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 清除旧权重
        cursor.execute("DELETE FROM fusion_weights")
        
        # 保存新权重
        for weight_type, weights in self.fusion_weights.items():
            for model_name, weight_value in weights.items():
                cursor.execute("""
                    INSERT INTO fusion_weights 
                    (weight_type, model_name, weight_value, is_active)
                    VALUES (?, ?, ?, ?)
                """, (weight_type, model_name, weight_value, True))
        
        conn.commit()
        conn.close()
```

### 2. 主要融合接口
```python
class FusionPredictor:
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.fusion_engine = IntelligentIntersectionFusion(db_path)
        
    def predict_next_period(self, issue: str, all_predictions: Dict) -> Dict:
        """预测下一期的主要接口"""
        try:
            # 执行融合预测
            recommendations = self.fusion_engine.fusion_predict(all_predictions)
            
            if not recommendations:
                raise ValueError("融合预测失败，没有生成推荐结果")
            
            # 保存预测结果
            self.fusion_engine.save_predictions(issue, recommendations)
            
            # 准备返回结果
            result = {
                'issue': issue,
                'total_recommendations': len(recommendations),
                'top_5_recommendations': recommendations[:5],
                'high_confidence_count': len([r for r in recommendations if r['confidence_level'] == 'high']),
                'prediction_summary': {
                    'best_combination': f"{recommendations[0]['hundreds']}{recommendations[0]['tens']}{recommendations[0]['units']}",
                    'best_probability': recommendations[0]['combined_probability'],
                    'best_sum': recommendations[0]['sum_value'],
                    'best_span': recommendations[0]['span_value'],
                    'confidence_level': recommendations[0]['confidence_level']
                },
                'all_recommendations': recommendations
            }
            
            return result
            
        except Exception as e:
            self.fusion_engine.logger.error(f"预测失败: {e}")
            return {'error': str(e)}
    
    def evaluate_last_prediction(self, issue: str, actual_result: Dict) -> Dict:
        """评估上期预测结果"""
        return self.fusion_engine.evaluate_predictions(issue, actual_result)
    
    def get_performance_summary(self, days: int = 30) -> Dict:
        """获取性能摘要"""
        conn = sqlite3.connect(self.db_path)
        
        query = """
            SELECT 
                COUNT(*) as total_predictions,
                SUM(CASE WHEN hit_type = 'exact' THEN 1 ELSE 0 END) as exact_hits,
                SUM(CASE WHEN hit_type = 'position' THEN 1 ELSE 0 END) as position_hits,
                AVG(overall_score) as avg_score,
                AVG(CASE WHEN predicted_rank IS NOT NULL THEN predicted_rank ELSE 999 END) as avg_rank
            FROM prediction_performance
            WHERE evaluated_at >= datetime('now', '-{} days')
        """.format(days)
        
        result = pd.read_sql_query(query, conn).iloc[0]
        
        conn.close()
        
        exact_hit_rate = result['exact_hits'] / result['total_predictions'] if result['total_predictions'] > 0 else 0
        position_hit_rate = result['position_hits'] / result['total_predictions'] if result['total_predictions'] > 0 else 0
        
        return {
            'total_predictions': int(result['total_predictions']),
            'exact_hit_rate': exact_hit_rate,
            'position_hit_rate': position_hit_rate,
            'average_score': result['avg_score'],
            'average_rank': result['avg_rank']
        }
```

## 成功标准

### 融合效果
- [ ] 生成推荐数量 10-20个
- [ ] 高置信度推荐 ≥ 3个
- [ ] 约束一致性分数 > 0.8
- [ ] 多样性指标合理

### 预测质量
- [ ] 完全命中率 > 20%
- [ ] 位置命中率 > 60%
- [ ] Top5命中率 > 40%
- [ ] 平均排名 < 10

### 系统稳定性
- [ ] 融合算法收敛稳定
- [ ] 权重调整机制有效
- [ ] 约束优化正常工作

## 部署说明

```python
# 使用示例
fusion_predictor = FusionPredictor("data/lottery.db")

# 准备各预测器结果
all_predictions = {
    'hundreds': {
        'probabilities': [0.1, 0.2, 0.15, 0.1, 0.05, 0.1, 0.1, 0.05, 0.1, 0.05]
    },
    'tens': {
        'probabilities': [0.05, 0.1, 0.2, 0.15, 0.1, 0.1, 0.1, 0.05, 0.1, 0.05]
    },
    'units': {
        'probabilities': [0.08, 0.12, 0.18, 0.12, 0.08, 0.12, 0.12, 0.08, 0.05, 0.05]
    },
    'sum': {
        'predicted_sum': 15.5
    },
    'span': {
        'predicted_span': 6.2
    }
}

# 预测下一期
result = fusion_predictor.predict_next_period("2024001", all_predictions)
print(f"融合预测结果: {result}")

# 评估预测结果
actual_result = {
    'hundreds': 3,
    'tens': 5,
    'units': 7,
    'sum_value': 15,
    'span': 4
}
evaluation = fusion_predictor.evaluate_last_prediction("2024001", actual_result)
print(f"预测评估: {evaluation}")
```

## 下一步
完成P8后，进入**P9-闭环自动优化系统**开发阶段。
