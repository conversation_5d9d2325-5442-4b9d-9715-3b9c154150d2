"""
增量更新器模块
实现增量更新机制，避免重复采集，提高效率
"""

import logging
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import time

logger = logging.getLogger(__name__)


class IncrementalUpdater:
    """增量更新器"""

    def __init__(self, db_path: str = "data/lottery.db"):
        self.db_path = db_path
        # 优化：增量更新使用倒序数据源（最新数据在前）
        self.data_sources = {
            'incremental': "https://data.17500.cn/3d_desc.txt",  # 倒序：适合增量更新
            'complete': "https://data.17500.cn/3d_asc.txt",      # 正序：适合完整采集
            'backup': "https://data.17500.cn/3d_desc.txt"        # 备用：倒序数据
        }
        self.update_stats = {
            'last_update_time': None,
            'total_updates': 0,
            'new_records': 0,
            'updated_records': 0,
            'skipped_records': 0,
            'error_records': 0
        }
    
    def get_connection(self) -> sqlite3.Connection:
        """获取数据库连接"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def get_latest_issue(self) -> Optional[str]:
        """获取数据库中最新的期号"""
        try:
            with self.get_connection() as conn:
                cursor = conn.execute(
                    "SELECT issue FROM lottery_data ORDER BY draw_date DESC, issue DESC LIMIT 1"
                )
                result = cursor.fetchone()
                return result['issue'] if result else None
        except Exception as e:
            logger.error(f"获取最新期号失败: {e}")
            return None
    
    def get_latest_date(self) -> Optional[str]:
        """获取数据库中最新的开奖日期"""
        try:
            with self.get_connection() as conn:
                cursor = conn.execute(
                    "SELECT draw_date FROM lottery_data ORDER BY draw_date DESC LIMIT 1"
                )
                result = cursor.fetchone()
                return result['draw_date'] if result else None
        except Exception as e:
            logger.error(f"获取最新日期失败: {e}")
            return None
    
    def get_missing_issues(self, start_issue: str, end_issue: str) -> List[str]:
        """获取缺失的期号列表"""
        try:
            with self.get_connection() as conn:
                cursor = conn.execute(
                    "SELECT issue FROM lottery_data WHERE issue BETWEEN ? AND ? ORDER BY issue",
                    (start_issue, end_issue)
                )
                existing_issues = {row['issue'] for row in cursor.fetchall()}
            
            # 生成期号范围
            start_num = int(start_issue)
            end_num = int(end_issue)
            all_issues = {str(i).zfill(7) for i in range(start_num, end_num + 1)}
            
            # 找出缺失的期号
            missing_issues = sorted(all_issues - existing_issues)
            return missing_issues
            
        except Exception as e:
            logger.error(f"获取缺失期号失败: {e}")
            return []
    
    def check_record_exists(self, issue: str) -> bool:
        """检查记录是否已存在"""
        try:
            with self.get_connection() as conn:
                cursor = conn.execute(
                    "SELECT 1 FROM lottery_data WHERE issue = ? LIMIT 1",
                    (issue,)
                )
                return cursor.fetchone() is not None
        except Exception as e:
            logger.error(f"检查记录存在性失败: {e}")
            return False
    
    def get_record_by_issue(self, issue: str) -> Optional[Dict[str, Any]]:
        """根据期号获取记录"""
        try:
            with self.get_connection() as conn:
                cursor = conn.execute(
                    "SELECT * FROM lottery_data WHERE issue = ?",
                    (issue,)
                )
                result = cursor.fetchone()
                return dict(result) if result else None
        except Exception as e:
            logger.error(f"获取记录失败: {e}")
            return None
    
    def insert_new_record(self, record: Dict[str, Any]) -> bool:
        """插入新记录"""
        try:
            with self.get_connection() as conn:
                sql = """
                INSERT INTO lottery_data 
                (issue, draw_date, hundreds, tens, units, trial_hundreds, trial_tens, trial_units,
                 machine_number, sales_amount, prize_info, sum_value, span, number_type)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                conn.execute(sql, (
                    record.get('issue'),
                    record.get('draw_date'),
                    record.get('hundreds'),
                    record.get('tens'),
                    record.get('units'),
                    record.get('trial_hundreds'),
                    record.get('trial_tens'),
                    record.get('trial_units'),
                    record.get('machine_number'),
                    record.get('sales_amount'),
                    record.get('prize_info'),
                    record.get('sum_value'),
                    record.get('span'),
                    record.get('number_type')
                ))
                conn.commit()
                self.update_stats['new_records'] += 1
                return True
        except Exception as e:
            logger.error(f"插入新记录失败: {e}")
            self.update_stats['error_records'] += 1
            return False
    
    def update_existing_record(self, record: Dict[str, Any]) -> bool:
        """更新现有记录"""
        try:
            with self.get_connection() as conn:
                sql = """
                UPDATE lottery_data SET
                    draw_date = ?, hundreds = ?, tens = ?, units = ?,
                    trial_hundreds = ?, trial_tens = ?, trial_units = ?,
                    machine_number = ?, sales_amount = ?, prize_info = ?,
                    sum_value = ?, span = ?, number_type = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE issue = ?
                """
                conn.execute(sql, (
                    record.get('draw_date'),
                    record.get('hundreds'),
                    record.get('tens'),
                    record.get('units'),
                    record.get('trial_hundreds'),
                    record.get('trial_tens'),
                    record.get('trial_units'),
                    record.get('machine_number'),
                    record.get('sales_amount'),
                    record.get('prize_info'),
                    record.get('sum_value'),
                    record.get('span'),
                    record.get('number_type'),
                    record.get('issue')
                ))
                conn.commit()
                self.update_stats['updated_records'] += 1
                return True
        except Exception as e:
            logger.error(f"更新记录失败: {e}")
            self.update_stats['error_records'] += 1
            return False
    
    def is_record_different(self, new_record: Dict[str, Any], existing_record: Dict[str, Any]) -> bool:
        """检查记录是否有差异"""
        # 比较关键字段
        key_fields = [
            'draw_date', 'hundreds', 'tens', 'units',
            'trial_hundreds', 'trial_tens', 'trial_units',
            'machine_number', 'sales_amount', 'prize_info'
        ]
        
        for field in key_fields:
            new_value = new_record.get(field)
            existing_value = existing_record.get(field)
            
            # 处理None值比较
            if new_value != existing_value:
                # 特殊处理数值类型
                if isinstance(new_value, (int, float)) and isinstance(existing_value, (int, float)):
                    if abs(new_value - existing_value) > 0.001:  # 浮点数比较
                        return True
                else:
                    return True
        
        return False
    
    def process_incremental_update(self, new_records: List[Dict[str, Any]], 
                                 force_update: bool = False) -> Dict[str, Any]:
        """处理增量更新"""
        start_time = time.time()
        logger.info(f"开始增量更新，共 {len(new_records)} 条记录")
        
        update_summary = {
            'total_processed': 0,
            'new_records': 0,
            'updated_records': 0,
            'skipped_records': 0,
            'error_records': 0,
            'processing_time': 0,
            'details': []
        }
        
        for record in new_records:
            self.update_stats['total_updates'] += 1
            update_summary['total_processed'] += 1
            
            issue = record.get('issue')
            if not issue:
                logger.warning("记录缺少期号，跳过")
                update_summary['error_records'] += 1
                continue
            
            try:
                existing_record = self.get_record_by_issue(issue)
                
                if existing_record is None:
                    # 新记录
                    if self.insert_new_record(record):
                        update_summary['new_records'] += 1
                        logger.info(f"插入新记录: {issue}")
                        update_summary['details'].append({
                            'issue': issue,
                            'action': 'inserted',
                            'status': 'success'
                        })
                    else:
                        update_summary['error_records'] += 1
                        update_summary['details'].append({
                            'issue': issue,
                            'action': 'insert',
                            'status': 'failed'
                        })
                else:
                    # 检查是否需要更新
                    if force_update or self.is_record_different(record, existing_record):
                        if self.update_existing_record(record):
                            update_summary['updated_records'] += 1
                            logger.info(f"更新记录: {issue}")
                            update_summary['details'].append({
                                'issue': issue,
                                'action': 'updated',
                                'status': 'success'
                            })
                        else:
                            update_summary['error_records'] += 1
                            update_summary['details'].append({
                                'issue': issue,
                                'action': 'update',
                                'status': 'failed'
                            })
                    else:
                        # 记录相同，跳过
                        update_summary['skipped_records'] += 1
                        self.update_stats['skipped_records'] += 1
                        update_summary['details'].append({
                            'issue': issue,
                            'action': 'skipped',
                            'status': 'no_change'
                        })
            
            except Exception as e:
                logger.error(f"处理记录 {issue} 时出错: {e}")
                update_summary['error_records'] += 1
                update_summary['details'].append({
                    'issue': issue,
                    'action': 'error',
                    'status': 'failed',
                    'error': str(e)
                })
        
        # 更新统计信息
        processing_time = time.time() - start_time
        update_summary['processing_time'] = processing_time
        self.update_stats['last_update_time'] = datetime.now().isoformat()
        
        logger.info(f"增量更新完成:")
        logger.info(f"  处理时间: {processing_time:.2f} 秒")
        logger.info(f"  总处理数: {update_summary['total_processed']}")
        logger.info(f"  新增记录: {update_summary['new_records']}")
        logger.info(f"  更新记录: {update_summary['updated_records']}")
        logger.info(f"  跳过记录: {update_summary['skipped_records']}")
        logger.info(f"  错误记录: {update_summary['error_records']}")
        
        return update_summary
    
    def get_update_stats(self) -> Dict[str, Any]:
        """获取更新统计信息"""
        return self.update_stats.copy()
    
    def reset_stats(self):
        """重置统计信息"""
        self.update_stats = {
            'last_update_time': None,
            'total_updates': 0,
            'new_records': 0,
            'updated_records': 0,
            'skipped_records': 0,
            'error_records': 0
        }


class DataSynchronizer:
    """数据同步器 - 确保数据一致性"""
    
    def __init__(self, db_path: str = "data/lottery.db"):
        self.db_path = db_path
        self.updater = IncrementalUpdater(db_path)
    
    def sync_with_source(self, source_data: List[Dict[str, Any]], 
                        sync_mode: str = "incremental") -> Dict[str, Any]:
        """与数据源同步"""
        logger.info(f"开始数据同步，模式: {sync_mode}")
        
        if sync_mode == "incremental":
            return self._incremental_sync(source_data)
        elif sync_mode == "full":
            return self._full_sync(source_data)
        else:
            raise ValueError(f"不支持的同步模式: {sync_mode}")
    
    def _incremental_sync(self, source_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """增量同步"""
        # 获取最新期号
        latest_issue = self.updater.get_latest_issue()
        
        if latest_issue:
            # 只处理比最新期号更新的数据
            filtered_data = [
                record for record in source_data
                if record.get('issue', '') > latest_issue
            ]
            logger.info(f"增量同步: 最新期号 {latest_issue}, 待处理 {len(filtered_data)} 条新记录")
        else:
            # 数据库为空，处理所有数据
            filtered_data = source_data
            logger.info(f"首次同步: 处理所有 {len(filtered_data)} 条记录")
        
        return self.updater.process_incremental_update(filtered_data)
    
    def _full_sync(self, source_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """全量同步"""
        logger.info(f"全量同步: 处理所有 {len(source_data)} 条记录")
        return self.updater.process_incremental_update(source_data, force_update=True)
    
    def verify_data_integrity(self) -> Dict[str, Any]:
        """验证数据完整性"""
        try:
            with self.updater.get_connection() as conn:
                # 检查数据统计
                cursor = conn.execute("SELECT COUNT(*) as total FROM lottery_data")
                total_records = cursor.fetchone()['total']
                
                # 检查日期范围
                cursor = conn.execute(
                    "SELECT MIN(draw_date) as min_date, MAX(draw_date) as max_date FROM lottery_data"
                )
                date_range = cursor.fetchone()
                
                # 检查期号连续性
                cursor = conn.execute(
                    "SELECT issue FROM lottery_data ORDER BY issue"
                )
                issues = [row['issue'] for row in cursor.fetchall()]
                
                # 分析期号间隔
                gaps = []
                if len(issues) > 1:
                    for i in range(1, len(issues)):
                        prev_num = int(issues[i-1])
                        curr_num = int(issues[i])
                        if curr_num - prev_num > 1:
                            gaps.append((issues[i-1], issues[i]))
                
                integrity_report = {
                    'total_records': total_records,
                    'date_range': {
                        'min_date': date_range['min_date'],
                        'max_date': date_range['max_date']
                    },
                    'issue_gaps': gaps,
                    'gap_count': len(gaps),
                    'integrity_score': 1.0 - (len(gaps) / max(total_records, 1))
                }
                
                logger.info(f"数据完整性检查完成:")
                logger.info(f"  总记录数: {total_records}")
                logger.info(f"  日期范围: {date_range['min_date']} ~ {date_range['max_date']}")
                logger.info(f"  期号间隔: {len(gaps)} 个")
                logger.info(f"  完整性评分: {integrity_report['integrity_score']:.2%}")
                
                return integrity_report
                
        except Exception as e:
            logger.error(f"数据完整性检查失败: {e}")
            return {'error': str(e)}


if __name__ == "__main__":
    # 测试增量更新器
    print("=== 福彩3D增量更新器测试 ===")
    
    # 创建测试数据
    test_data = [
        {
            'issue': '2024004',
            'draw_date': '2024-01-04',
            'hundreds': 7,
            'tens': 8,
            'units': 9,
            'sum_value': 24,
            'span': 2,
            'number_type': '组六'
        },
        {
            'issue': '2024005',
            'draw_date': '2024-01-05',
            'hundreds': 0,
            'tens': 1,
            'units': 2,
            'sum_value': 3,
            'span': 2,
            'number_type': '组六'
        }
    ]
    
    # 测试增量更新
    updater = IncrementalUpdater()
    
    # 获取最新期号
    latest_issue = updater.get_latest_issue()
    print(f"数据库最新期号: {latest_issue}")
    
    # 处理增量更新
    result = updater.process_incremental_update(test_data)
    
    print(f"更新结果:")
    print(f"  新增记录: {result['new_records']}")
    print(f"  更新记录: {result['updated_records']}")
    print(f"  跳过记录: {result['skipped_records']}")
    print(f"  处理时间: {result['processing_time']:.2f} 秒")
    
    # 测试数据同步器
    print(f"\n=== 数据同步器测试 ===")
    synchronizer = DataSynchronizer()
    
    # 验证数据完整性
    integrity = synchronizer.verify_data_integrity()
    print(f"数据完整性:")
    print(f"  总记录数: {integrity.get('total_records', 0)}")
    print(f"  完整性评分: {integrity.get('integrity_score', 0):.2%}")
    
    print(f"\n🎉 增量更新器测试完成！")


def smart_incremental_update(max_new_records: int = 50) -> bool:
    """
    智能增量更新 - 优化版本
    使用倒序数据源，只获取最新的数据进行更新

    Args:
        max_new_records: 最大新记录数，避免过度更新

    Returns:
        更新是否成功
    """
    try:
        import requests

        # 使用倒序数据源进行增量更新
        desc_url = "https://data.17500.cn/3d_desc.txt"
        logger.info(f"🔄 开始智能增量更新，最多获取 {max_new_records} 条最新记录")

        # 获取数据
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }

        response = requests.get(desc_url, headers=headers, timeout=30)
        response.raise_for_status()

        content = response.text
        lines = content.strip().split('\n')

        # 解析最新的记录（倒序数据源，前面的就是最新的）
        new_records = []
        for line in lines[:max_new_records * 2]:  # 多取一些以防格式问题
            line = line.strip()
            if not line:
                continue

            # 解析格式：期号 日期 开奖号码 试机号码
            import re
            match = re.match(r'(\d{7})\s+(\d{4}-\d{2}-\d{2})\s+(\d{3})\s+(\d{3})', line)
            if match:
                period, date, numbers, trial_numbers = match.groups()

                record = {
                    'period': period,
                    'date': date,
                    'numbers': numbers,
                    'trial_numbers': trial_numbers,
                    'draw_machine': 0,
                    'trial_machine': 0,
                    'sales_amount': 0,
                    'direct_prize': 0,
                    'group3_prize': 0,
                    'group6_prize': 0,
                    'unknown_field1': 0,
                    'unknown_field2': 0,
                    'unknown_field3': 0
                }

                new_records.append(record)

                if len(new_records) >= max_new_records:
                    break

        if not new_records:
            logger.warning("⚠️ 未获取到新记录")
            return False

        # 保存到数据库
        success = _save_incremental_records(new_records)

        if success:
            logger.info(f"✅ 智能增量更新成功，处理了 {len(new_records)} 条记录")
        else:
            logger.error("❌ 智能增量更新失败")

        return success

    except Exception as e:
        logger.error(f"❌ 智能增量更新异常: {e}")
        return False


def _save_incremental_records(records: List[Dict]) -> bool:
    """保存增量记录到数据库"""
    try:
        import sqlite3

        conn = sqlite3.connect("data/lottery.db")
        cursor = conn.cursor()

        # 确保表存在
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS lottery_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                period TEXT UNIQUE NOT NULL,
                date DATE NOT NULL,
                numbers TEXT NOT NULL,
                trial_numbers TEXT NOT NULL,
                draw_machine INTEGER NOT NULL,
                trial_machine INTEGER NOT NULL,
                sales_amount INTEGER NOT NULL,
                direct_prize INTEGER NOT NULL,
                group3_prize INTEGER NOT NULL,
                group6_prize INTEGER NOT NULL,
                unknown_field1 INTEGER DEFAULT 0,
                unknown_field2 INTEGER DEFAULT 0,
                unknown_field3 INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # 批量插入或更新
        insert_count = 0
        update_count = 0

        for record in records:
            try:
                cursor.execute('''
                    INSERT OR REPLACE INTO lottery_records
                    (period, date, numbers, trial_numbers, draw_machine, trial_machine,
                     sales_amount, direct_prize, group3_prize, group6_prize,
                     unknown_field1, unknown_field2, unknown_field3)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    record['period'], record['date'], record['numbers'], record['trial_numbers'],
                    record['draw_machine'], record['trial_machine'], record['sales_amount'],
                    record['direct_prize'], record['group3_prize'], record['group6_prize'],
                    record['unknown_field1'], record['unknown_field2'], record['unknown_field3']
                ))

                if cursor.rowcount > 0:
                    insert_count += 1

            except sqlite3.IntegrityError:
                update_count += 1
                continue

        conn.commit()
        conn.close()

        logger.info(f"📊 增量更新统计: 新增 {insert_count} 条，更新 {update_count} 条")
        return True

    except Exception as e:
        logger.error(f"❌ 保存增量记录失败: {e}")
        return False


def update_from_17500_desc() -> bool:
    """从17500.cn的desc数据源更新数据 - 保持向后兼容"""
    return smart_incremental_update()


def schedule_daily_update():
    """安排每日自动更新"""
    try:
        import schedule

        # 每天晚上21:30执行更新
        schedule.every().day.at("21:30").do(update_from_17500_desc)

        logger.info("✅ 已安排每日21:30自动更新")

        # 运行调度器
        while True:
            schedule.run_pending()
            time.sleep(60)  # 每分钟检查一次

    except ImportError:
        logger.warning("⚠️ schedule库未安装，无法设置自动更新")
        logger.info("💡 可以手动运行 update_from_17500_desc() 进行更新")
    except Exception as e:
        logger.error(f"❌ 调度器运行失败: {e}")


if __name__ == "__main__":
    # 测试新的更新功能
    print("🚀 测试17500 desc数据源更新...")
    success = update_from_17500_desc()

    if success:
        print("✅ 更新测试成功")
    else:
        print("❌ 更新测试失败")
