#!/usr/bin/env python3
"""
P6和值预测器预测脚本

支持命令行参数，支持单次和批量预测，显示详细信息
提供多种输出格式和约束优化选项

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import sys
import os
import argparse
import logging
import json
import numpy as np
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.predictors.sum_predictor import SumPredictor

def setup_logging(log_level: str = "INFO"):
    """设置日志配置"""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[logging.StreamHandler(sys.stdout)]
    )

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="P6和值预测器预测脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 预测下一期和值
  python predict_sum.py --issue 2025001
  
  # 使用指定模型预测
  python predict_sum.py --issue 2025001 --model xgb
  
  # 批量预测多期
  python predict_sum.py --batch --count 5
  
  # 启用约束优化
  python predict_sum.py --issue 2025001 --with-constraints
  
  # 输出详细信息
  python predict_sum.py --issue 2025001 --verbose --output-format json
        """
    )
    
    parser.add_argument(
        "--issue", "-i",
        type=str,
        help="期号 (如: 2025001)"
    )
    
    parser.add_argument(
        "--model", "-m",
        type=str,
        choices=["xgb", "lgb", "lstm", "distribution", "constraint", "ensemble"],
        default="ensemble",
        help="使用的模型类型 (默认: ensemble)"
    )
    
    parser.add_argument(
        "--model-path",
        type=str,
        default=None,
        help="模型文件路径 (可选，用于加载已训练的模型)"
    )
    
    parser.add_argument(
        "--config", "-c",
        type=str,
        default="config/sum_predictor_config.yaml",
        help="配置文件路径 (默认: config/sum_predictor_config.yaml)"
    )
    
    parser.add_argument(
        "--db-path", "-d",
        type=str,
        default="data/lottery.db",
        help="数据库文件路径 (默认: data/lottery.db)"
    )
    
    parser.add_argument(
        "--batch", "-b",
        action="store_true",
        help="批量预测模式"
    )
    
    parser.add_argument(
        "--count",
        type=int,
        default=1,
        help="批量预测期数 (默认: 1)"
    )
    
    parser.add_argument(
        "--with-constraints",
        action="store_true",
        help="启用约束优化 (需要位置预测器)"
    )
    
    parser.add_argument(
        "--output-format",
        type=str,
        choices=["text", "json", "csv"],
        default="text",
        help="输出格式 (默认: text)"
    )
    
    parser.add_argument(
        "--output-file", "-o",
        type=str,
        default=None,
        help="输出文件路径 (可选)"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="显示详细信息"
    )
    
    parser.add_argument(
        "--confidence-threshold",
        type=float,
        default=0.0,
        help="置信度阈值，低于此值的预测将被标记 (默认: 0.0)"
    )
    
    parser.add_argument(
        "--log-level",
        type=str,
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="WARNING",
        help="日志级别 (默认: WARNING)"
    )
    
    return parser.parse_args()

def load_predictor(args) -> SumPredictor:
    """加载预测器"""
    logger = logging.getLogger("load_predictor")
    
    try:
        # 初始化预测器
        predictor = SumPredictor(args.db_path, args.config)
        
        # 如果指定了模型路径，加载模型
        if args.model_path:
            logger.info(f"从 {args.model_path} 加载模型")
            if not predictor.load_model(args.model_path):
                raise ValueError(f"无法加载模型: {args.model_path}")
        else:
            # 构建模型（需要先训练）
            logger.info("构建模型")
            predictor.build_model()
            
            # 检查模型是否已训练
            if not predictor.is_trained:
                logger.warning("模型尚未训练，预测结果可能不准确")
        
        # 切换到指定模型
        predictor.switch_model(args.model)
        
        return predictor
        
    except Exception as e:
        logger.error(f"加载预测器失败: {e}")
        raise

def generate_features() -> np.ndarray:
    """生成预测特征（示例实现）"""
    # 这里应该实现真实的特征生成逻辑
    # 暂时返回示例特征
    return np.array([[13.5] * 10])  # 示例：10个特征

def predict_single(predictor: SumPredictor, issue: str, args) -> Dict[str, Any]:
    """单次预测"""
    logger = logging.getLogger("predict_single")
    
    try:
        logger.info(f"预测期号: {issue}")
        
        # 生成特征
        features = generate_features()
        
        # 执行预测
        if args.with_constraints:
            # 这里需要位置预测器的结果
            # 暂时使用示例数据
            position_predictions = {
                'hundreds_pred': np.array([5]),
                'tens_pred': np.array([5]),
                'units_pred': np.array([5])
            }
            
            predictions, constraint_info = predictor.predict_with_position_constraints(
                features, position_predictions
            )
            confidence = 0.7  # 示例置信度
        else:
            predictions, confidences = predictor.predict_with_confidence(features)
            confidence = confidences[0]
            constraint_info = None
        
        # 构建结果
        result = {
            'issue': issue,
            'model': args.model,
            'predicted_sum': float(predictions[0]),
            'confidence': float(confidence),
            'prediction_range': {
                'min': max(0, int(predictions[0] - 2)),
                'max': min(27, int(predictions[0] + 2))
            },
            'timestamp': datetime.now().isoformat()
        }
        
        # 添加约束信息
        if constraint_info:
            result['constraint_optimization'] = constraint_info
        
        # 添加详细信息
        if args.verbose:
            result['model_details'] = {
                'available_models': predictor.get_available_models(),
                'current_model': predictor.current_model,
                'features_shape': features.shape
            }
            
            # 如果是集成模型，添加权重信息
            if args.model == 'ensemble':
                result['ensemble_weights'] = predictor.get_model_weights()
        
        return result
        
    except Exception as e:
        logger.error(f"预测失败: {e}")
        raise

def predict_batch(predictor: SumPredictor, args) -> List[Dict[str, Any]]:
    """批量预测"""
    logger = logging.getLogger("predict_batch")
    
    try:
        logger.info(f"批量预测 {args.count} 期")
        
        results = []
        base_issue = int(args.issue) if args.issue else 2025001
        
        for i in range(args.count):
            issue = str(base_issue + i).zfill(7)
            result = predict_single(predictor, issue, args)
            results.append(result)
        
        return results
        
    except Exception as e:
        logger.error(f"批量预测失败: {e}")
        raise

def format_output(results: List[Dict[str, Any]], args) -> str:
    """格式化输出"""
    if args.output_format == "json":
        return json.dumps(results, indent=2, ensure_ascii=False)
    
    elif args.output_format == "csv":
        if not results:
            return ""
        
        # CSV头部
        headers = ["期号", "模型", "预测和值", "置信度", "预测范围", "时间"]
        csv_lines = [",".join(headers)]
        
        # CSV数据
        for result in results:
            line = [
                result['issue'],
                result['model'],
                f"{result['predicted_sum']:.2f}",
                f"{result['confidence']:.3f}",
                f"{result['prediction_range']['min']}-{result['prediction_range']['max']}",
                result['timestamp']
            ]
            csv_lines.append(",".join(line))
        
        return "\n".join(csv_lines)
    
    else:  # text format
        output_lines = []
        
        for result in results:
            output_lines.append("=" * 50)
            output_lines.append(f"期号: {result['issue']}")
            output_lines.append(f"模型: {result['model']}")
            output_lines.append(f"预测和值: {result['predicted_sum']:.2f}")
            output_lines.append(f"置信度: {result['confidence']:.3f}")
            output_lines.append(f"预测范围: {result['prediction_range']['min']}-{result['prediction_range']['max']}")
            
            # 置信度警告
            if result['confidence'] < args.confidence_threshold:
                output_lines.append("⚠️  警告: 预测置信度较低")
            
            # 约束优化信息
            if 'constraint_optimization' in result:
                output_lines.append("\n约束优化信息:")
                constraint_details = result['constraint_optimization']['constraint_details'][0]
                output_lines.append(f"  基础预测: {constraint_details['base_prediction']:.2f}")
                output_lines.append(f"  位置和值: {constraint_details['position_sum']:.2f}")
                output_lines.append(f"  优化后: {constraint_details['optimized_prediction']:.2f}")
                output_lines.append(f"  约束分数: {constraint_details['constraint_score']:.3f}")
            
            # 详细信息
            if args.verbose and 'model_details' in result:
                output_lines.append("\n模型详细信息:")
                details = result['model_details']
                output_lines.append(f"  可用模型: {', '.join(details['available_models'])}")
                output_lines.append(f"  当前模型: {details['current_model']}")
                
                if 'ensemble_weights' in result:
                    output_lines.append("  集成权重:")
                    for model, weight in result['ensemble_weights'].items():
                        output_lines.append(f"    {model}: {weight:.3f}")
            
            output_lines.append(f"预测时间: {result['timestamp']}")
            output_lines.append("")
        
        return "\n".join(output_lines)

def save_output(content: str, args):
    """保存输出到文件"""
    if args.output_file:
        try:
            output_path = Path(args.output_file)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"预测结果已保存到: {output_path}")
            
        except Exception as e:
            print(f"保存输出文件失败: {e}")

def main():
    """主函数"""
    args = parse_arguments()
    
    # 设置日志
    setup_logging(args.log_level)
    logger = logging.getLogger("main")
    
    try:
        # 检查参数
        if not args.batch and not args.issue:
            print("错误: 必须指定期号 (--issue) 或启用批量模式 (--batch)")
            return 1
        
        # 检查数据库文件
        if not Path(args.db_path).exists():
            print(f"错误: 数据库文件不存在: {args.db_path}")
            return 1
        
        # 加载预测器
        logger.info("加载预测器")
        predictor = load_predictor(args)
        
        # 执行预测
        if args.batch:
            results = predict_batch(predictor, args)
        else:
            result = predict_single(predictor, args.issue, args)
            results = [result]
        
        # 格式化输出
        output_content = format_output(results, args)
        
        # 显示结果
        print(output_content)
        
        # 保存到文件
        save_output(output_content, args)
        
        logger.info("预测完成")
        return 0
        
    except Exception as e:
        logger.error(f"预测过程发生错误: {e}")
        print(f"错误: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
