#!/usr/bin/env python3
"""
P9系统状态检查工具

该脚本提供P9闭环自动优化系统的状态检查功能，包括：
1. 系统健康状态检查
2. 服务运行状态监控
3. 性能指标实时查看
4. 数据库连接状态
5. 配置文件验证
6. 错误日志分析

作者: Augment Code AI Assistant
创建日期: 2025-01-14
版本: 1.0.0
"""

import os
import sys
import sqlite3
import json
import logging
import argparse
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from pathlib import Path
import platform
import psutil
import time

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class P9StatusChecker:
    """P9系统状态检查器"""
    
    def __init__(self, verbose: bool = False):
        """
        初始化状态检查器
        
        Args:
            verbose: 是否详细输出
        """
        self.project_root = project_root
        self.verbose = verbose
        
        # 设置日志
        self._setup_logging()
        
        # 状态检查结果
        self.check_results = {}
        
        self.logger.info("P9系统状态检查器初始化完成")
    
    def _setup_logging(self):
        """设置日志"""
        log_level = logging.DEBUG if self.verbose else logging.INFO
        
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[logging.StreamHandler(sys.stdout)]
        )
        
        self.logger = logging.getLogger(__name__)
    
    def check_all(self) -> Dict[str, Any]:
        """执行所有状态检查"""
        try:
            print("🔍 开始P9系统状态检查...")
            print("="*60)
            
            # 1. 系统基础检查
            self.check_results['system_basic'] = self._check_system_basic()
            
            # 2. 数据库状态检查
            self.check_results['database'] = self._check_database_status()
            
            # 3. P9组件状态检查
            self.check_results['p9_components'] = self._check_p9_components()
            
            # 4. 性能指标检查
            self.check_results['performance'] = self._check_performance_metrics()
            
            # 5. 配置文件检查
            self.check_results['configuration'] = self._check_configuration()
            
            # 6. 日志状态检查
            self.check_results['logs'] = self._check_log_status()
            
            # 生成总体状态
            self.check_results['overall'] = self._generate_overall_status()
            
            # 打印摘要
            self._print_status_summary()
            
            return self.check_results
            
        except Exception as e:
            self.logger.error(f"状态检查失败: {e}")
            return {'error': str(e)}
    
    def _check_system_basic(self) -> Dict[str, Any]:
        """检查系统基础状态"""
        try:
            print("📊 检查系统基础状态...")
            
            # 系统信息
            system_info = {
                'platform': platform.platform(),
                'python_version': platform.python_version(),
                'cpu_count': psutil.cpu_count(),
                'memory_total_gb': round(psutil.virtual_memory().total / (1024**3), 2),
                'disk_usage': {}
            }
            
            # 磁盘使用情况
            disk_usage = psutil.disk_usage(str(self.project_root))
            system_info['disk_usage'] = {
                'total_gb': round(disk_usage.total / (1024**3), 2),
                'used_gb': round(disk_usage.used / (1024**3), 2),
                'free_gb': round(disk_usage.free / (1024**3), 2),
                'usage_percent': round((disk_usage.used / disk_usage.total) * 100, 1)
            }
            
            # CPU和内存使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            memory_percent = psutil.virtual_memory().percent
            
            system_info['current_usage'] = {
                'cpu_percent': cpu_percent,
                'memory_percent': memory_percent
            }
            
            # 检查关键目录
            required_dirs = ['data', 'logs', 'config', 'src', 'scripts']
            missing_dirs = []
            
            for dir_name in required_dirs:
                dir_path = self.project_root / dir_name
                if not dir_path.exists():
                    missing_dirs.append(dir_name)
            
            status = 'healthy' if not missing_dirs else 'warning'
            
            result = {
                'status': status,
                'system_info': system_info,
                'missing_directories': missing_dirs,
                'timestamp': datetime.now().isoformat()
            }
            
            print(f"  ✅ 系统基础状态: {status}")
            if missing_dirs:
                print(f"  ⚠️  缺失目录: {', '.join(missing_dirs)}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"系统基础检查失败: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def _check_database_status(self) -> Dict[str, Any]:
        """检查数据库状态"""
        try:
            print("💾 检查数据库状态...")
            
            db_path = self.project_root / 'data' / 'fucai3d.db'
            
            if not db_path.exists():
                return {
                    'status': 'error',
                    'error': '数据库文件不存在',
                    'db_path': str(db_path)
                }
            
            # 连接数据库
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 检查表结构
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            # 检查关键表
            required_tables = [
                'lottery_data', 'fusion_predictions', 'system_performance_monitor'
            ]
            
            p9_tables = [table for table in tables if table.startswith('p9_')]
            
            missing_tables = [table for table in required_tables if table not in tables]
            
            # 获取数据统计
            data_stats = {}
            
            if 'lottery_data' in tables:
                cursor.execute("SELECT COUNT(*) FROM lottery_data")
                data_stats['lottery_data_count'] = cursor.fetchone()[0]
            
            if 'fusion_predictions' in tables:
                cursor.execute("SELECT COUNT(*) FROM fusion_predictions")
                data_stats['predictions_count'] = cursor.fetchone()[0]
                
                cursor.execute("""
                    SELECT COUNT(*) FROM fusion_predictions 
                    WHERE prediction_time > datetime('now', '-24 hours')
                """)
                data_stats['recent_predictions_24h'] = cursor.fetchone()[0]
            
            # 检查P9表数据
            for table in p9_tables:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    data_stats[f'{table}_count'] = cursor.fetchone()[0]
                except Exception:
                    pass
            
            conn.close()
            
            # 文件大小
            file_size_mb = round(db_path.stat().st_size / (1024*1024), 2)
            
            status = 'healthy' if not missing_tables else 'warning'
            
            result = {
                'status': status,
                'db_path': str(db_path),
                'file_size_mb': file_size_mb,
                'total_tables': len(tables),
                'p9_tables_count': len(p9_tables),
                'missing_tables': missing_tables,
                'data_statistics': data_stats,
                'timestamp': datetime.now().isoformat()
            }
            
            print(f"  ✅ 数据库状态: {status}")
            print(f"  📊 数据表数量: {len(tables)} (P9表: {len(p9_tables)})")
            print(f"  💾 文件大小: {file_size_mb} MB")
            
            if missing_tables:
                print(f"  ⚠️  缺失表: {', '.join(missing_tables)}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"数据库状态检查失败: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def _check_p9_components(self) -> Dict[str, Any]:
        """检查P9组件状态"""
        try:
            print("🔧 检查P9组件状态...")
            
            components_status = {}
            
            # 检查核心组件文件
            core_components = {
                'intelligent_optimization_manager': 'src/optimization/intelligent_optimization_manager.py',
                'enhanced_performance_monitor': 'src/optimization/enhanced_performance_monitor.py',
                'integrated_weight_adjuster': 'src/optimization/integrated_weight_adjuster.py',
                'task_queue_manager': 'src/optimization/task_queue_manager.py',
                'performance_analyzer': 'src/optimization/performance_analyzer.py',
                'p8_integration_layer': 'src/optimization/p8_integration_layer.py',
                'intelligent_decision_engine': 'src/optimization/intelligent_decision_engine.py',
                'exception_handler': 'src/optimization/exception_handler.py'
            }
            
            for component_name, file_path in core_components.items():
                component_path = self.project_root / file_path
                
                if component_path.exists():
                    # 尝试导入组件
                    try:
                        module_path = file_path.replace('/', '.').replace('.py', '')
                        __import__(module_path)
                        
                        components_status[component_name] = {
                            'status': 'available',
                            'file_exists': True,
                            'importable': True,
                            'file_size_kb': round(component_path.stat().st_size / 1024, 2)
                        }
                        
                    except ImportError as e:
                        components_status[component_name] = {
                            'status': 'import_error',
                            'file_exists': True,
                            'importable': False,
                            'error': str(e)
                        }
                else:
                    components_status[component_name] = {
                        'status': 'missing',
                        'file_exists': False,
                        'importable': False
                    }
            
            # 统计状态
            available_count = sum(1 for status in components_status.values() if status['status'] == 'available')
            total_count = len(components_status)
            
            overall_status = 'healthy' if available_count == total_count else 'warning'
            
            result = {
                'status': overall_status,
                'components': components_status,
                'available_count': available_count,
                'total_count': total_count,
                'availability_percent': round((available_count / total_count) * 100, 1),
                'timestamp': datetime.now().isoformat()
            }
            
            print(f"  ✅ P9组件状态: {overall_status}")
            print(f"  📦 可用组件: {available_count}/{total_count} ({result['availability_percent']}%)")
            
            return result
            
        except Exception as e:
            self.logger.error(f"P9组件检查失败: {e}")
            return {'status': 'error', 'error': str(e)}

    def _check_performance_metrics(self) -> Dict[str, Any]:
        """检查性能指标"""
        try:
            print("📈 检查性能指标...")

            db_path = self.project_root / 'data' / 'fucai3d.db'

            if not db_path.exists():
                return {'status': 'error', 'error': '数据库文件不存在'}

            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            performance_data = {}

            # 检查最近的预测准确率
            try:
                accuracy_query = """
                    SELECT
                        AVG(CASE WHEN predicted_hundreds = actual_hundreds THEN 1.0 ELSE 0.0 END) as hundreds_accuracy,
                        AVG(CASE WHEN predicted_tens = actual_tens THEN 1.0 ELSE 0.0 END) as tens_accuracy,
                        AVG(CASE WHEN predicted_units = actual_units THEN 1.0 ELSE 0.0 END) as units_accuracy,
                        COUNT(*) as total_predictions
                    FROM fusion_predictions
                    WHERE prediction_time > datetime('now', '-7 days')
                    AND actual_hundreds IS NOT NULL
                """

                cursor.execute(accuracy_query)
                accuracy_result = cursor.fetchone()

                if accuracy_result and accuracy_result[3] > 0:
                    performance_data['accuracy_metrics'] = {
                        'hundreds_accuracy': round(accuracy_result[0] or 0, 4),
                        'tens_accuracy': round(accuracy_result[1] or 0, 4),
                        'units_accuracy': round(accuracy_result[2] or 0, 4),
                        'sample_count': accuracy_result[3],
                        'period': '7 days'
                    }

            except Exception as e:
                performance_data['accuracy_metrics'] = {'error': str(e)}

            # 检查命中率
            try:
                hit_rate_query = """
                    SELECT
                        COUNT(*) as total,
                        SUM(CASE WHEN hit_status = 'hit' THEN 1 ELSE 0 END) as hits
                    FROM fusion_predictions
                    WHERE prediction_time > datetime('now', '-7 days')
                """

                cursor.execute(hit_rate_query)
                hit_result = cursor.fetchone()

                if hit_result and hit_result[0] > 0:
                    hit_rate = hit_result[1] / hit_result[0]
                    performance_data['hit_rate'] = {
                        'hit_rate': round(hit_rate, 4),
                        'total_predictions': hit_result[0],
                        'successful_hits': hit_result[1],
                        'period': '7 days'
                    }

            except Exception as e:
                performance_data['hit_rate'] = {'error': str(e)}

            # 检查系统性能监控数据
            try:
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='system_performance_monitor'")
                if cursor.fetchone():
                    perf_query = """
                        SELECT
                            component_name,
                            AVG(current_value) as avg_value,
                            COUNT(*) as sample_count
                        FROM system_performance_monitor
                        WHERE monitor_time > datetime('now', '-1 hour')
                        GROUP BY component_name
                    """

                    cursor.execute(perf_query)
                    perf_results = cursor.fetchall()

                    performance_data['system_performance'] = {}
                    for component, avg_value, count in perf_results:
                        performance_data['system_performance'][component] = {
                            'average_value': round(avg_value, 4),
                            'sample_count': count
                        }

            except Exception as e:
                performance_data['system_performance'] = {'error': str(e)}

            conn.close()

            # 评估整体性能状态
            status = 'healthy'
            issues = []

            # 检查准确率
            if 'accuracy_metrics' in performance_data:
                accuracy = performance_data['accuracy_metrics']
                if not isinstance(accuracy, dict) or 'error' in accuracy:
                    issues.append('准确率数据获取失败')
                    status = 'warning'
                else:
                    avg_accuracy = (accuracy.get('hundreds_accuracy', 0) +
                                  accuracy.get('tens_accuracy', 0) +
                                  accuracy.get('units_accuracy', 0)) / 3
                    if avg_accuracy < 0.2:
                        issues.append(f'平均准确率过低: {avg_accuracy:.3f}')
                        status = 'warning'

            # 检查命中率
            if 'hit_rate' in performance_data:
                hit_rate_data = performance_data['hit_rate']
                if not isinstance(hit_rate_data, dict) or 'error' in hit_rate_data:
                    issues.append('命中率数据获取失败')
                    status = 'warning'
                else:
                    hit_rate = hit_rate_data.get('hit_rate', 0)
                    if hit_rate < 0.1:
                        issues.append(f'命中率过低: {hit_rate:.3f}')
                        status = 'warning'

            result = {
                'status': status,
                'performance_data': performance_data,
                'issues': issues,
                'timestamp': datetime.now().isoformat()
            }

            print(f"  ✅ 性能指标状态: {status}")
            if issues:
                for issue in issues:
                    print(f"  ⚠️  {issue}")

            return result

        except Exception as e:
            self.logger.error(f"性能指标检查失败: {e}")
            return {'status': 'error', 'error': str(e)}

    def _check_configuration(self) -> Dict[str, Any]:
        """检查配置文件"""
        try:
            print("⚙️  检查配置文件...")

            config_files = {
                'p9_config': 'config/p9_config.json',
                'main_config': 'config/config.json'
            }

            config_status = {}

            for config_name, config_path in config_files.items():
                full_path = self.project_root / config_path

                if full_path.exists():
                    try:
                        with open(full_path, 'r', encoding='utf-8') as f:
                            config_data = json.load(f)

                        config_status[config_name] = {
                            'status': 'valid',
                            'file_exists': True,
                            'valid_json': True,
                            'file_size_kb': round(full_path.stat().st_size / 1024, 2),
                            'keys_count': len(config_data) if isinstance(config_data, dict) else 0
                        }

                    except json.JSONDecodeError as e:
                        config_status[config_name] = {
                            'status': 'invalid_json',
                            'file_exists': True,
                            'valid_json': False,
                            'error': str(e)
                        }

                    except Exception as e:
                        config_status[config_name] = {
                            'status': 'read_error',
                            'file_exists': True,
                            'valid_json': False,
                            'error': str(e)
                        }
                else:
                    config_status[config_name] = {
                        'status': 'missing',
                        'file_exists': False,
                        'valid_json': False
                    }

            # 检查环境变量
            env_vars = {
                'PYTHONPATH': os.environ.get('PYTHONPATH'),
                'PATH': len(os.environ.get('PATH', '').split(os.pathsep))
            }

            # 评估整体配置状态
            valid_configs = sum(1 for status in config_status.values() if status['status'] == 'valid')
            total_configs = len(config_status)

            overall_status = 'healthy' if valid_configs == total_configs else 'warning'

            result = {
                'status': overall_status,
                'config_files': config_status,
                'environment_variables': env_vars,
                'valid_configs': valid_configs,
                'total_configs': total_configs,
                'timestamp': datetime.now().isoformat()
            }

            print(f"  ✅ 配置文件状态: {overall_status}")
            print(f"  📄 有效配置: {valid_configs}/{total_configs}")

            return result

        except Exception as e:
            self.logger.error(f"配置文件检查失败: {e}")
            return {'status': 'error', 'error': str(e)}

    def _check_log_status(self) -> Dict[str, Any]:
        """检查日志状态"""
        try:
            print("📝 检查日志状态...")

            logs_dir = self.project_root / 'logs'

            if not logs_dir.exists():
                return {
                    'status': 'warning',
                    'logs_dir_exists': False,
                    'message': '日志目录不存在'
                }

            # 查找日志文件
            log_files = list(logs_dir.glob('*.log'))

            log_analysis = {}
            total_size_mb = 0
            recent_errors = 0

            for log_file in log_files:
                try:
                    file_size_mb = round(log_file.stat().st_size / (1024*1024), 2)
                    total_size_mb += file_size_mb

                    # 分析最近的日志内容
                    with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                        lines = f.readlines()

                    # 统计最近1小时的错误
                    recent_error_count = 0
                    cutoff_time = datetime.now() - timedelta(hours=1)

                    for line in lines[-1000:]:  # 检查最后1000行
                        if 'ERROR' in line or 'CRITICAL' in line:
                            recent_error_count += 1

                    recent_errors += recent_error_count

                    log_analysis[log_file.name] = {
                        'size_mb': file_size_mb,
                        'line_count': len(lines),
                        'recent_errors': recent_error_count,
                        'last_modified': datetime.fromtimestamp(log_file.stat().st_mtime).isoformat()
                    }

                except Exception as e:
                    log_analysis[log_file.name] = {
                        'error': f'读取失败: {e}'
                    }

            # 评估日志状态
            status = 'healthy'
            issues = []

            if not log_files:
                status = 'warning'
                issues.append('未找到日志文件')
            elif recent_errors > 10:
                status = 'warning'
                issues.append(f'最近1小时有 {recent_errors} 个错误')
            elif total_size_mb > 100:
                status = 'warning'
                issues.append(f'日志文件总大小过大: {total_size_mb} MB')

            result = {
                'status': status,
                'logs_dir_exists': True,
                'log_files_count': len(log_files),
                'total_size_mb': total_size_mb,
                'recent_errors_1h': recent_errors,
                'log_analysis': log_analysis,
                'issues': issues,
                'timestamp': datetime.now().isoformat()
            }

            print(f"  ✅ 日志状态: {status}")
            print(f"  📁 日志文件: {len(log_files)} 个，总大小: {total_size_mb} MB")

            if issues:
                for issue in issues:
                    print(f"  ⚠️  {issue}")

            return result

        except Exception as e:
            self.logger.error(f"日志状态检查失败: {e}")
            return {'status': 'error', 'error': str(e)}

    def _generate_overall_status(self) -> Dict[str, Any]:
        """生成总体状态"""
        try:
            # 收集各模块状态
            module_statuses = []

            for module_name, module_result in self.check_results.items():
                if module_name != 'overall' and isinstance(module_result, dict):
                    status = module_result.get('status', 'unknown')
                    module_statuses.append((module_name, status))

            # 统计状态
            healthy_count = sum(1 for _, status in module_statuses if status == 'healthy')
            warning_count = sum(1 for _, status in module_statuses if status == 'warning')
            error_count = sum(1 for _, status in module_statuses if status == 'error')
            total_count = len(module_statuses)

            # 确定总体状态
            if error_count > 0:
                overall_status = 'error'
                health_score = 0.0
            elif warning_count > 0:
                overall_status = 'warning'
                health_score = healthy_count / total_count
            else:
                overall_status = 'healthy'
                health_score = 1.0

            # 生成建议
            recommendations = []

            if error_count > 0:
                recommendations.append("立即检查错误模块并修复问题")

            if warning_count > 0:
                recommendations.append("关注警告模块，考虑优化配置")

            if health_score < 0.8:
                recommendations.append("系统健康度较低，建议全面检查")

            if not recommendations:
                recommendations.append("系统运行正常，继续监控")

            result = {
                'status': overall_status,
                'health_score': round(health_score, 2),
                'module_summary': {
                    'healthy': healthy_count,
                    'warning': warning_count,
                    'error': error_count,
                    'total': total_count
                },
                'module_details': dict(module_statuses),
                'recommendations': recommendations,
                'timestamp': datetime.now().isoformat()
            }

            return result

        except Exception as e:
            self.logger.error(f"生成总体状态失败: {e}")
            return {'status': 'error', 'error': str(e)}

    def _print_status_summary(self):
        """打印状态摘要"""
        print("\n" + "="*60)
        print("📊 P9系统状态检查摘要")
        print("="*60)

        overall = self.check_results.get('overall', {})

        # 总体状态
        status = overall.get('status', 'unknown')
        health_score = overall.get('health_score', 0.0)

        status_icon = {
            'healthy': '🟢',
            'warning': '🟡',
            'error': '🔴',
            'unknown': '⚪'
        }.get(status, '⚪')

        print(f"\n{status_icon} 总体状态: {status.upper()}")
        print(f"💯 健康评分: {health_score * 100:.1f}%")

        # 模块状态详情
        module_summary = overall.get('module_summary', {})
        print(f"\n📋 模块状态统计:")
        print(f"  🟢 健康: {module_summary.get('healthy', 0)}")
        print(f"  🟡 警告: {module_summary.get('warning', 0)}")
        print(f"  🔴 错误: {module_summary.get('error', 0)}")
        print(f"  📊 总计: {module_summary.get('total', 0)}")

        # 详细模块状态
        print(f"\n🔍 详细模块状态:")
        module_details = overall.get('module_details', {})
        for module_name, module_status in module_details.items():
            status_icon = {
                'healthy': '✅',
                'warning': '⚠️ ',
                'error': '❌',
                'unknown': '❓'
            }.get(module_status, '❓')

            module_display = module_name.replace('_', ' ').title()
            print(f"  {status_icon} {module_display}")

        # 建议
        recommendations = overall.get('recommendations', [])
        if recommendations:
            print(f"\n💡 建议:")
            for i, rec in enumerate(recommendations, 1):
                print(f"  {i}. {rec}")

        print("\n" + "="*60)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='P9系统状态检查工具')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    parser.add_argument('--json', '-j', action='store_true', help='JSON格式输出')
    parser.add_argument('--module', '-m', help='检查特定模块')

    args = parser.parse_args()

    try:
        checker = P9StatusChecker(verbose=args.verbose)

        if args.module:
            # 检查特定模块
            module_methods = {
                'system': checker._check_system_basic,
                'database': checker._check_database_status,
                'components': checker._check_p9_components,
                'performance': checker._check_performance_metrics,
                'config': checker._check_configuration,
                'logs': checker._check_log_status
            }

            if args.module in module_methods:
                result = module_methods[args.module]()
                if args.json:
                    print(json.dumps(result, indent=2, ensure_ascii=False))
                else:
                    print(f"\n{args.module.title()} 模块检查结果:")
                    print(json.dumps(result, indent=2, ensure_ascii=False))
            else:
                print(f"❌ 未知模块: {args.module}")
                print(f"可用模块: {', '.join(module_methods.keys())}")
                sys.exit(1)
        else:
            # 执行完整检查
            results = checker.check_all()

            if args.json:
                print(json.dumps(results, indent=2, ensure_ascii=False))

            # 根据状态设置退出码
            overall_status = results.get('overall', {}).get('status', 'unknown')
            if overall_status == 'error':
                sys.exit(1)
            elif overall_status == 'warning':
                sys.exit(2)
            else:
                sys.exit(0)

    except KeyboardInterrupt:
        print("\n⚠️  状态检查被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 状态检查过程中发生错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
