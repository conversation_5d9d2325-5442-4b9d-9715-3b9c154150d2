"""
P7跨度预测器 - 集成融合模型

实现集成融合模型，组合多模型预测
实现加权平均和动态权重调整

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import numpy as np
import pandas as pd
import pickle
import logging
from typing import Dict, List, Tuple, Optional, Any
from pathlib import Path

from .base_span_model import BaseSpanModel

class EnsembleSpanModel(BaseSpanModel):
    """集成跨度预测模型"""
    
    def __init__(self, db_path: str, config_path: Optional[str] = None):
        """
        初始化集成跨度模型
        
        Args:
            db_path: 数据库路径
            config_path: 配置文件路径
        """
        super().__init__(db_path, config_path)
        
        self.model_type = 'ensemble'
        self.base_models = {}
        self.model_weights = {}
        self.is_trained = False
        
        # 加载集成配置
        self.ensemble_config = self.config.get('span_predictor', {}).get('models', {}).get('ensemble', {})
        
        # 默认参数
        self.default_params = {
            'base_models': ['xgb', 'lgb', 'lstm', 'classification'],
            'weights': {
                'xgb': 0.3,
                'lgb': 0.3,
                'lstm': 0.2,
                'classification': 0.2
            },
            'dynamic_weights': True,
            'weight_update_frequency': 10,
            'performance_window': 50
        }
        
        # 合并配置参数
        self.params = {**self.default_params, **self.ensemble_config}
        
        # 初始化权重
        self.model_weights = self.params['weights'].copy()
        self.dynamic_weights_enabled = self.params['dynamic_weights']
        self.weight_update_frequency = self.params['weight_update_frequency']
        self.performance_window = self.params['performance_window']
        
        # 性能历史
        self.performance_history = {}
        self.prediction_count = 0
        
        self.logger.info(f"集成跨度模型初始化完成，参数: {self.params}")
    
    def build_model(self) -> bool:
        """
        构建集成模型
        
        Returns:
            是否构建成功
        """
        try:
            # 集成模型不需要单独构建，依赖于基础模型
            self.logger.info("集成跨度模型构建成功")
            return True
            
        except Exception as e:
            self.logger.error(f"集成跨度模型构建失败: {e}")
            return False
    
    def set_base_models(self, models: Dict[str, Any]):
        """
        设置基础模型
        
        Args:
            models: 基础模型字典
        """
        self.base_models = models
        
        # 更新权重（只保留存在的模型）
        available_models = set(models.keys())
        configured_models = set(self.model_weights.keys())
        
        # 移除不存在的模型权重
        for model_name in configured_models - available_models:
            if model_name in self.model_weights:
                del self.model_weights[model_name]
        
        # 为新模型添加默认权重
        for model_name in available_models - configured_models:
            self.model_weights[model_name] = 1.0 / len(available_models)
        
        # 归一化权重
        self._normalize_weights()
        
        self.logger.info(f"设置基础模型: {list(models.keys())}, 权重: {self.model_weights}")
    
    def _normalize_weights(self):
        """归一化权重"""
        total_weight = sum(self.model_weights.values())
        if total_weight > 0:
            for model_name in self.model_weights:
                self.model_weights[model_name] /= total_weight
    
    def load_data(self) -> Tuple[np.ndarray, np.ndarray]:
        """
        加载训练数据
        
        Returns:
            特征矩阵和目标向量
        """
        try:
            # 集成模型使用基础模型的数据
            if self.base_models and 'xgb' in self.base_models:
                return self.base_models['xgb'].load_data()
            else:
                # 从数据库加载数据
                data = self.data_access.load_lottery_data()
                
                if data.empty:
                    raise ValueError("数据库中没有数据")
                
                # 计算跨度
                data['span'] = data[['hundreds', 'tens', 'units']].max(axis=1) - data[['hundreds', 'tens', 'units']].min(axis=1)
                
                # 简单特征
                X = data[['hundreds', 'tens', 'units']].values
                y = data['span'].values
                
                self.logger.info(f"加载集成跨度数据: {len(X)} 个样本")
                return X, y
            
        except Exception as e:
            self.logger.error(f"加载集成跨度数据失败: {e}")
            raise
    
    def train(self, X: Optional[np.ndarray] = None, y: Optional[np.ndarray] = None) -> Dict[str, Any]:
        """
        训练集成模型
        
        Args:
            X: 特征矩阵（可选）
            y: 目标向量（可选）
            
        Returns:
            训练性能指标
        """
        try:
            if not self.base_models:
                raise ValueError("未设置基础模型")
            
            # 如果没有提供数据，从数据库加载
            if X is None or y is None:
                X, y = self.load_data()
            
            # 训练所有基础模型
            training_results = {}
            for model_name, model in self.base_models.items():
                if hasattr(model, 'train'):
                    try:
                        self.logger.info(f"训练基础模型: {model_name}")
                        result = model.train(X, y)
                        training_results[model_name] = result
                    except Exception as e:
                        self.logger.warning(f"训练基础模型 {model_name} 失败: {e}")
                        training_results[model_name] = {'error': str(e)}
            
            # 构建模型
            if not self.build_model():
                raise ValueError("集成模型构建失败")
            
            self.is_trained = True
            
            # 评估集成性能
            performance = self.evaluate(X, y)
            performance['base_model_results'] = training_results
            
            self.logger.info(f"集成跨度模型训练完成: {performance}")
            return performance
            
        except Exception as e:
            self.logger.error(f"集成跨度模型训练失败: {e}")
            raise
    
    def predict(self, X: np.ndarray) -> np.ndarray:
        """
        集成预测
        
        Args:
            X: 特征矩阵
            
        Returns:
            预测结果
        """
        if not self.is_trained or not self.base_models:
            raise ValueError("模型尚未训练或未设置基础模型")
        
        try:
            predictions = {}
            
            # 获取各基础模型的预测
            for model_name, model in self.base_models.items():
                if hasattr(model, 'predict') and model.is_trained:
                    try:
                        pred = model.predict(X)
                        predictions[model_name] = pred
                    except Exception as e:
                        self.logger.warning(f"模型 {model_name} 预测失败: {e}")
            
            if not predictions:
                raise ValueError("没有可用的基础模型预测")
            
            # 加权平均
            ensemble_predictions = self._weighted_average(predictions)
            
            # 更新预测计数
            self.prediction_count += len(X)
            
            # 动态权重更新
            if (self.dynamic_weights_enabled and 
                self.prediction_count % self.weight_update_frequency == 0):
                self._update_dynamic_weights(X, predictions)
            
            return ensemble_predictions
            
        except Exception as e:
            self.logger.error(f"集成预测失败: {e}")
            raise
    
    def _weighted_average(self, predictions: Dict[str, np.ndarray]) -> np.ndarray:
        """加权平均预测"""
        if not predictions:
            raise ValueError("没有预测结果")
        
        # 获取样本数量
        sample_count = len(next(iter(predictions.values())))
        ensemble_pred = np.zeros(sample_count)
        total_weight = 0
        
        for model_name, pred in predictions.items():
            weight = self.model_weights.get(model_name, 0)
            if weight > 0:
                ensemble_pred += weight * pred
                total_weight += weight
        
        if total_weight > 0:
            ensemble_pred /= total_weight
        
        # 跨度范围约束
        ensemble_pred = np.clip(ensemble_pred, 0, 9)
        
        return ensemble_pred
    
    def _update_dynamic_weights(self, X: np.ndarray, predictions: Dict[str, np.ndarray]):
        """动态更新权重"""
        try:
            # 这里需要真实标签来计算性能，暂时跳过
            # 在实际应用中，可以使用滑动窗口的历史性能来更新权重
            self.logger.debug("动态权重更新（需要真实标签）")
            
        except Exception as e:
            self.logger.warning(f"动态权重更新失败: {e}")
    
    def predict_with_confidence(self, X: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        带置信度的集成预测
        
        Args:
            X: 特征矩阵
            
        Returns:
            预测结果和置信度
        """
        if not self.is_trained or not self.base_models:
            raise ValueError("模型尚未训练或未设置基础模型")
        
        try:
            predictions = {}
            confidences = {}
            
            # 获取各基础模型的预测和置信度
            for model_name, model in self.base_models.items():
                if hasattr(model, 'predict_with_confidence') and model.is_trained:
                    try:
                        pred, conf = model.predict_with_confidence(X)
                        predictions[model_name] = pred
                        confidences[model_name] = conf
                    except Exception as e:
                        self.logger.warning(f"模型 {model_name} 置信度预测失败: {e}")
                elif hasattr(model, 'predict') and model.is_trained:
                    try:
                        pred = model.predict(X)
                        predictions[model_name] = pred
                        confidences[model_name] = np.full(len(pred), 0.7)  # 默认置信度
                    except Exception as e:
                        self.logger.warning(f"模型 {model_name} 预测失败: {e}")
            
            if not predictions:
                raise ValueError("没有可用的基础模型预测")
            
            # 加权平均预测
            ensemble_predictions = self._weighted_average(predictions)
            
            # 计算集成置信度
            ensemble_confidences = self._calculate_ensemble_confidence(predictions, confidences)
            
            return ensemble_predictions, ensemble_confidences
            
        except Exception as e:
            self.logger.error(f"集成置信度预测失败: {e}")
            raise
    
    def _calculate_ensemble_confidence(self, predictions: Dict[str, np.ndarray], 
                                     confidences: Dict[str, np.ndarray]) -> np.ndarray:
        """计算集成置信度"""
        if not predictions:
            return np.array([])
        
        sample_count = len(next(iter(predictions.values())))
        ensemble_confidence = np.zeros(sample_count)
        
        # 方法1：加权平均置信度
        total_weight = 0
        for model_name in predictions.keys():
            weight = self.model_weights.get(model_name, 0)
            if weight > 0 and model_name in confidences:
                ensemble_confidence += weight * confidences[model_name]
                total_weight += weight
        
        if total_weight > 0:
            ensemble_confidence /= total_weight
        
        # 方法2：基于预测一致性调整置信度
        pred_values = list(predictions.values())
        if len(pred_values) > 1:
            pred_matrix = np.array(pred_values)
            pred_std = np.std(pred_matrix, axis=0)
            
            # 预测越一致，置信度越高
            consistency_factor = 1.0 / (1.0 + pred_std)
            ensemble_confidence *= consistency_factor
        
        return np.clip(ensemble_confidence, 0.1, 0.95)
    
    def get_model_contributions(self, X: np.ndarray) -> Dict[str, Any]:
        """
        获取各模型对预测的贡献
        
        Args:
            X: 特征矩阵
            
        Returns:
            模型贡献信息
        """
        try:
            predictions = {}
            contributions = {}
            
            # 获取各模型预测
            for model_name, model in self.base_models.items():
                if hasattr(model, 'predict') and model.is_trained:
                    try:
                        pred = model.predict(X)
                        predictions[model_name] = pred
                    except Exception as e:
                        self.logger.warning(f"模型 {model_name} 预测失败: {e}")
            
            # 计算贡献
            ensemble_pred = self._weighted_average(predictions)
            
            for model_name, pred in predictions.items():
                weight = self.model_weights.get(model_name, 0)
                contribution = weight * pred
                contributions[model_name] = {
                    'weight': weight,
                    'prediction': pred.tolist() if hasattr(pred, 'tolist') else pred,
                    'contribution': contribution.tolist() if hasattr(contribution, 'tolist') else contribution
                }
            
            return {
                'ensemble_prediction': ensemble_pred.tolist() if hasattr(ensemble_pred, 'tolist') else ensemble_pred,
                'model_contributions': contributions,
                'model_weights': self.model_weights.copy()
            }
            
        except Exception as e:
            self.logger.error(f"获取模型贡献失败: {e}")
            return {}
    
    def set_model_weights(self, weights: Dict[str, float]):
        """
        设置模型权重
        
        Args:
            weights: 模型权重字典
        """
        # 只设置存在的模型权重
        for model_name, weight in weights.items():
            if model_name in self.base_models:
                self.model_weights[model_name] = weight
        
        # 归一化权重
        self._normalize_weights()
        
        self.logger.info(f"更新模型权重: {self.model_weights}")
    
    def enable_dynamic_weights(self, enable: bool = True):
        """
        启用/禁用动态权重调整
        
        Args:
            enable: 是否启用
        """
        self.dynamic_weights_enabled = enable
        self.logger.info(f"动态权重调整: {'启用' if enable else '禁用'}")
    
    def get_model_weights(self) -> Dict[str, float]:
        """获取当前模型权重"""
        return self.model_weights.copy()
    
    def save_model(self, filepath: str) -> bool:
        """
        保存集成模型
        
        Args:
            filepath: 保存路径
            
        Returns:
            是否保存成功
        """
        try:
            # 确保目录存在
            Path(filepath).parent.mkdir(parents=True, exist_ok=True)
            
            # 保存集成模型配置
            model_data = {
                'model_type': self.model_type,
                'params': self.params,
                'model_weights': self.model_weights,
                'dynamic_weights_enabled': self.dynamic_weights_enabled,
                'weight_update_frequency': self.weight_update_frequency,
                'performance_window': self.performance_window,
                'performance_history': self.performance_history,
                'prediction_count': self.prediction_count,
                'is_trained': self.is_trained
            }
            
            with open(filepath, 'wb') as f:
                pickle.dump(model_data, f)
            
            self.logger.info(f"集成跨度模型已保存到: {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存集成跨度模型失败: {e}")
            return False
    
    def load_model(self, filepath: str) -> bool:
        """
        加载集成模型
        
        Args:
            filepath: 模型文件路径
            
        Returns:
            是否加载成功
        """
        try:
            if not Path(filepath).exists():
                self.logger.error(f"模型文件不存在: {filepath}")
                return False
            
            with open(filepath, 'rb') as f:
                model_data = pickle.load(f)
            
            self.params = model_data.get('params', self.params)
            self.model_weights = model_data.get('model_weights', {})
            self.dynamic_weights_enabled = model_data.get('dynamic_weights_enabled', True)
            self.weight_update_frequency = model_data.get('weight_update_frequency', 10)
            self.performance_window = model_data.get('performance_window', 50)
            self.performance_history = model_data.get('performance_history', {})
            self.prediction_count = model_data.get('prediction_count', 0)
            self.is_trained = model_data.get('is_trained', True)
            
            self.logger.info(f"集成跨度模型已从 {filepath} 加载")
            return True
            
        except Exception as e:
            self.logger.error(f"加载集成跨度模型失败: {e}")
            return False
