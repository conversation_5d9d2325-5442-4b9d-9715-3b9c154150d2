#!/usr/bin/env python3
"""
分析现有数据库架构，避免破坏性修改
"""

import sqlite3
import os
import json

def analyze_database_structure():
    """分析所有数据库的结构"""
    
    db_files = [
        "data/alerts.db",
        "data/fucai3d.db", 
        "data/lottery.db"
    ]
    
    analysis_result = {}
    
    for db_file in db_files:
        if not os.path.exists(db_file):
            print(f"❌ 数据库文件不存在: {db_file}")
            continue
            
        try:
            print(f"\n📊 分析数据库: {db_file}")
            print("=" * 50)
            
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()
            
            # 获取所有表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            
            db_info = {
                "file_path": db_file,
                "table_count": len(tables),
                "tables": {}
            }
            
            print(f"表数量: {len(tables)}")
            
            for table in tables:
                table_name = table[0]
                print(f"\n📋 表: {table_name}")
                
                # 获取表结构
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = cursor.fetchall()
                
                # 获取记录数
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                record_count = cursor.fetchone()[0]
                
                table_info = {
                    "columns": [],
                    "record_count": record_count
                }
                
                print(f"  记录数: {record_count}")
                print(f"  字段:")
                
                for col in columns:
                    col_info = {
                        "name": col[1],
                        "type": col[2],
                        "not_null": bool(col[3]),
                        "primary_key": bool(col[5])
                    }
                    table_info["columns"].append(col_info)
                    print(f"    - {col[1]} ({col[2]})")
                
                db_info["tables"][table_name] = table_info
            
            analysis_result[db_file] = db_info
            conn.close()
            
        except Exception as e:
            print(f"❌ 分析数据库 {db_file} 时出错: {e}")
    
    return analysis_result

def check_performance_monitoring_references():
    """检查代码中对性能监控表的引用"""
    
    print(f"\n🔍 检查性能监控相关的代码引用")
    print("=" * 50)
    
    # 需要检查的关键词
    keywords = [
        "enhanced_performance_monitor",
        "performance_monitor", 
        "performance_metrics",
        "monitoring"
    ]
    
    # 需要检查的文件
    files_to_check = [
        "src/web/api_adapter.py",
        "src/optimization/intelligent_closed_loop_optimizer.py",
        "config/p9_config.yaml",
        "config/fusion_config.yaml"
    ]
    
    references = {}
    
    for file_path in files_to_check:
        if not os.path.exists(file_path):
            continue
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            file_refs = []
            for keyword in keywords:
                if keyword in content:
                    # 找到关键词所在的行
                    lines = content.split('\n')
                    for i, line in enumerate(lines, 1):
                        if keyword in line:
                            file_refs.append({
                                "keyword": keyword,
                                "line_number": i,
                                "line_content": line.strip()
                            })
            
            if file_refs:
                references[file_path] = file_refs
                print(f"\n📄 文件: {file_path}")
                for ref in file_refs:
                    print(f"  第{ref['line_number']}行: {ref['keyword']}")
                    print(f"    {ref['line_content']}")
        
        except Exception as e:
            print(f"❌ 检查文件 {file_path} 时出错: {e}")
    
    return references

def analyze_database_connections():
    """分析代码中的数据库连接配置"""
    
    print(f"\n🔗 分析数据库连接配置")
    print("=" * 50)
    
    connection_patterns = [
        "sqlite3.connect",
        "lottery.db",
        "fucai3d.db", 
        "alerts.db",
        "DATABASE_URL",
        "DB_PATH"
    ]
    
    files_to_check = [
        "src/web/api_adapter.py",
        "src/web/app.py",
        "src/data/database_manager.py",
        "config/database_config.py"
    ]
    
    connections = {}
    
    for file_path in files_to_check:
        if not os.path.exists(file_path):
            continue
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            file_connections = []
            for pattern in connection_patterns:
                if pattern in content:
                    lines = content.split('\n')
                    for i, line in enumerate(lines, 1):
                        if pattern in line:
                            file_connections.append({
                                "pattern": pattern,
                                "line_number": i,
                                "line_content": line.strip()
                            })
            
            if file_connections:
                connections[file_path] = file_connections
                print(f"\n📄 文件: {file_path}")
                for conn in file_connections:
                    print(f"  第{conn['line_number']}行: {conn['pattern']}")
                    print(f"    {conn['line_content']}")
        
        except Exception as e:
            print(f"❌ 检查文件 {file_path} 时出错: {e}")
    
    return connections

def main():
    """主函数"""
    print("🔍 福彩3D系统数据库架构分析")
    print("=" * 60)
    
    # 分析数据库结构
    db_analysis = analyze_database_structure()
    
    # 检查性能监控引用
    perf_refs = check_performance_monitoring_references()
    
    # 分析数据库连接
    db_connections = analyze_database_connections()
    
    # 生成分析报告
    report = {
        "database_analysis": db_analysis,
        "performance_references": perf_refs,
        "database_connections": db_connections,
        "recommendations": []
    }
    
    # 生成建议
    print(f"\n💡 分析建议")
    print("=" * 50)
    
    # 检查是否有性能监控表
    has_perf_tables = False
    for db_file, db_info in db_analysis.items():
        for table_name in db_info["tables"]:
            if "performance" in table_name or "monitor" in table_name:
                has_perf_tables = True
                print(f"✅ 发现性能监控表: {table_name} (在 {db_file})")
    
    if not has_perf_tables:
        print("❌ 未发现性能监控相关表")
        report["recommendations"].append("需要创建性能监控表或修改API逻辑")
    
    # 检查主数据库
    main_db = None
    max_tables = 0
    for db_file, db_info in db_analysis.items():
        if db_info["table_count"] > max_tables:
            max_tables = db_info["table_count"]
            main_db = db_file
    
    if main_db:
        print(f"📊 主数据库可能是: {main_db} ({max_tables}个表)")
        report["recommendations"].append(f"建议在主数据库 {main_db} 中进行修改")
    
    # 保存分析报告
    with open("database_analysis_report.json", "w", encoding="utf-8") as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n📋 分析报告已保存到: database_analysis_report.json")
    
    return report

if __name__ == '__main__':
    main()
