# 福彩3D特征工程本地化转换实施计划

## 项目概述

**项目名称**: 福彩3D特征工程本地化转换  
**项目目标**: 将福彩3D开奖号码的各种特征工程指标本地化转换并保存到数据库  
**技术方案**: 不修改数据库结构，基于现有字段实时计算特征  
**预计工期**: 2-3天  
**风险等级**: 低（无数据库迁移风险）

## 核心决策

### ✅ 采用方案：基于现有数据实时计算特征
- **优势**: 保持现有系统完全稳定，无数据迁移风险
- **原理**: 基于现有的 `hundreds`, `tens`, `units` 字段实时计算所有特征
- **性能**: 对于8,359条数据，实时计算性能完全可接受

### ❌ 拒绝方案：数据库迁移
- **原因**: 用户质疑合理，当前数据库结构已经足够
- **风险**: 数据迁移可能引入不必要的复杂性和风险

## 需求分析

### 用户提到的特征清单
1. **和尾**: 和值的个位数 (sum_value % 10)
2. **连号**: 是否存在相邻数字
3. **奇偶**: 奇数偶数分布
4. **大小**: 大数小数分布 (>=5为大)
5. **大中小**: 数字范围分类
6. **质合**: 质数合数分布
7. **012路**: 除3余数分布
8. **形态**: 凸起、凹下、上升、下降、平行
9. **走势**: 与上期对比的变化趋势

### 现有数据库字段
- ✅ `hundreds`, `tens`, `units` - 开奖号码
- ✅ `sum_value` - 和值（已计算）
- ✅ `span` - 跨距（已计算）
- ✅ `number_type` - 号码类型（已计算）

## 实施清单

### 1. 创建特征计算器模块
**文件路径**: `src/data/feature_calculator.py`  
**代码行数**: ~200行  
**涉及类/方法**:
- `LotteryFeatureCalculator` 类
- `calculate_basic_features()` 方法
- `calculate_pattern_features()` 方法
- `calculate_trend_features()` 方法

**功能结果**:
- 实现所有9种特征的计算逻辑
- 提供纯函数式计算接口
- 无外部依赖，仅使用Python标准库

**依赖库**: 无（仅Python标准库）

### 2. 扩展LotteryData数据模型
**文件路径**: `src/database/models.py`  
**修改行数**: 第42-75行（扩展现有类）  
**涉及类/方法**:
- `LotteryData` 类
- 新增 `get_features()` 方法
- 新增 `get_feature_by_name()` 方法

**功能结果**:
- LotteryData实例可动态计算所有特征
- 保持向后兼容性
- 不修改数据库结构

**依赖库**: feature_calculator.py

### 3. 创建特征服务接口
**文件路径**: `src/data/feature_service.py`  
**代码行数**: ~150行  
**涉及类/方法**:
- `FeatureService` 类
- `get_features_for_issue()` 方法
- `get_batch_features()` 方法
- `get_historical_features()` 方法

**功能结果**:
- 提供统一的特征获取API
- 支持单期查询、批量查询
- 支持历史特征计算

**依赖库**: sqlite3, feature_calculator.py, models.py

### 4. 集成P2特征工程系统
**文件路径**: 修改现有P2设计  
**修改范围**: FeatureEngineer类实现  
**涉及类/方法**:
- 修改 `FeatureEngineer.generate_all_features()` 方法
- 保持 `save_features_to_db()` 接口不变

**功能结果**:
- 与P2-特征工程系统.md设计保持兼容
- 使用新的特征计算器
- 保持feature_data表结构不变

**依赖库**: 前面所有模块

### 5. 创建测试验证脚本
**文件路径**: `tests/test_features.py`  
**代码行数**: ~100行  
**涉及类/方法**:
- `TestFeatureCalculator` 类
- 各种特征的单元测试方法

**功能结果**:
- 验证所有特征计算的正确性
- 确保与用户需求一致
- 提供回归测试保障

**依赖库**: unittest, 所有特征模块

### 6. 创建使用示例和文档
**文件路径**: `examples/feature_usage.py`  
**代码行数**: ~80行  
**涉及类/方法**:
- 完整的使用示例
- 性能测试代码

**功能结果**:
- 展示如何使用新的特征系统
- 提供最佳实践指导
- 更新相关文档

**依赖库**: 完整的特征系统

## 技术架构

### 模块依赖关系
```
feature_calculator.py (核心计算逻辑)
    ↓
models.py (数据模型扩展)
    ↓
feature_service.py (服务接口)
    ↓
P2 FeatureEngineer (系统集成)
    ↓
tests + examples (验证和文档)
```

### 特征计算示例
```python
# 基于现有字段的实时特征计算
def calculate_features(hundreds, tens, units):
    features = {}
    
    # 和尾：和值的个位数
    sum_value = hundreds + tens + units
    features['sum_tail'] = sum_value % 10
    
    # 奇偶特征
    features['odd_count'] = sum((hundreds % 2, tens % 2, units % 2))
    
    # 大小特征
    features['big_count'] = sum((hundreds >= 5, tens >= 5, units >= 5))
    
    # 形态特征
    features['is_convex'] = tens == max(hundreds, tens, units)  # 凸起形
    features['is_ascending'] = hundreds <= tens <= units  # 上升形
    
    return features
```

## 成功标准

### 功能完整性
- [ ] 实现所有9种用户提到的特征
- [ ] 特征计算结果准确无误
- [ ] 与现有系统完全兼容

### 性能要求
- [ ] 单期特征计算时间 < 1ms
- [ ] 8,359期历史数据批量计算 < 5秒
- [ ] 内存占用增长 < 10MB

### 质量保证
- [ ] 所有特征通过单元测试
- [ ] 代码覆盖率 > 90%
- [ ] 无破坏性变更

## 风险评估

### 技术风险: 🟢 低
- 不涉及数据库结构修改
- 基于成熟的计算逻辑
- 有完整的测试覆盖

### 兼容性风险: 🟢 低  
- 保持现有API不变
- 向后兼容设计
- 渐进式集成

### 性能风险: 🟢 低
- 计算复杂度低
- 数据量适中
- 可选择性缓存

## 下一步行动

1. **立即开始**: 创建特征计算器模块
2. **并行开发**: 扩展数据模型和服务接口  
3. **集成测试**: 验证与P2系统的兼容性
4. **用户验收**: 确认特征计算结果符合预期

---

**备注**: 本项目严格区分于3dyuce项目，专注于fucai3d项目的特征工程优化。
