#!/usr/bin/env python3
"""
集成个位预测模型

基于多模型融合的个位数字预测模型，专注于独立位置预测。

特点：
- 融合XGBoost、LightGBM、LSTM三个模型
- 支持多种融合策略（简单平均、加权平均、Stacking）
- 动态权重调整
- 交叉验证优化
- 性能监控集成

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import sys
from pathlib import Path
import numpy as np
import logging
import pickle
import time
from typing import Dict, List, Tuple, Optional, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from sklearn.model_selection import cross_val_score, train_test_split
    from sklearn.linear_model import LogisticRegression
    from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
    from sklearn.preprocessing import LabelEncoder
except ImportError as e:
    print(f"警告: 缺少必要的机器学习库: {e}")
    print("请安装: pip install scikit-learn")

# 导入基类和模型
try:
    from src.predictors.base_independent_predictor import BaseIndependentPredictor
    from src.predictors.models.xgb_units_model import XGBUnitsModel
    from src.predictors.models.lgb_units_model import LGBUnitsModel
    from src.predictors.models.lstm_units_model import LSTMUnitsModel
    from config.config_loader import get_config
except ImportError as e:
    print(f"警告: 无法导入基类或模型: {e}")

class EnsembleUnitsModel(BaseIndependentPredictor):
    """集成个位预测模型"""
    
    def __init__(self, db_path: str):
        """
        初始化集成个位预测模型
        
        Args:
            db_path: 数据库路径
        """
        super().__init__("units", db_path)
        
        # 加载集成配置
        self._load_ensemble_config()
        
        # 初始化基础模型
        self.base_models = {}
        self.model_weights = {}
        self.meta_model = None
        self.label_encoder = LabelEncoder()
        self.training_metrics = {}
        
        # 初始化基础模型
        self._init_base_models()
        
        self.logger.info("集成个位预测模型初始化完成")
    
    def _load_ensemble_config(self):
        """加载集成配置"""
        try:
            config_loader = get_config()
            self.ensemble_config = config_loader.get_model_config('ensemble')
        except Exception as e:
            self.logger.warning(f"配置加载失败，使用默认集成配置: {e}")
            self.ensemble_config = {
                'method': 'weighted_average',
                'weights': {
                    'xgb': 0.4,
                    'lgb': 0.4,
                    'lstm': 0.2
                },
                'stacking_meta_model': 'logistic_regression',
                'cross_validation_folds': 5
            }
    
    def _init_base_models(self):
        """初始化基础模型"""
        try:
            self.base_models = {
                'xgb': XGBUnitsModel(self.db_path),
                'lgb': LGBUnitsModel(self.db_path),
                'lstm': LSTMUnitsModel(self.db_path)
            }
            
            # 设置权重
            self.model_weights = self.ensemble_config.get('weights', {
                'xgb': 0.4,
                'lgb': 0.4,
                'lstm': 0.2
            })
            
            self.logger.info("基础模型初始化完成")
            
        except Exception as e:
            self.logger.error(f"基础模型初始化失败: {e}")
            raise
    
    def build_model(self) -> Dict[str, Any]:
        """
        构建集成模型
        
        Returns:
            模型信息字典
        """
        try:
            # 构建所有基础模型
            for name, model in self.base_models.items():
                model.build_model()
                self.logger.info(f"基础模型 {name} 构建完成")
            
            # 如果使用Stacking，初始化元模型
            if self.ensemble_config.get('method') == 'stacking':
                meta_model_type = self.ensemble_config.get('stacking_meta_model', 'logistic_regression')
                if meta_model_type == 'logistic_regression':
                    self.meta_model = LogisticRegression(random_state=42, max_iter=1000)
                
                self.logger.info(f"元模型 {meta_model_type} 初始化完成")
            
            return {
                'base_models': list(self.base_models.keys()),
                'ensemble_method': self.ensemble_config.get('method'),
                'weights': self.model_weights
            }
            
        except Exception as e:
            self.logger.error(f"集成模型构建失败: {e}")
            raise
    
    def train(self, X: np.ndarray, y: np.ndarray) -> Dict[str, Any]:
        """
        训练集成模型
        
        Args:
            X: 特征矩阵
            y: 目标向量
            
        Returns:
            训练结果字典
        """
        try:
            start_time = time.time()
            self.logger.info(f"开始训练集成模型，数据形状: X{X.shape}, y{y.shape}")
            
            # 构建模型
            if not self.base_models:
                self.build_model()
            
            # 数据预处理
            X_processed, y_processed = self._preprocess_data(X, y)
            
            # 训练所有基础模型
            base_model_metrics = {}
            for name, model in self.base_models.items():
                self.logger.info(f"训练基础模型: {name}")
                try:
                    metrics = model.train(X_processed, y_processed)
                    base_model_metrics[name] = metrics
                    self.logger.info(f"基础模型 {name} 训练完成")
                except Exception as e:
                    self.logger.error(f"基础模型 {name} 训练失败: {e}")
                    # 如果某个模型训练失败，从集成中移除
                    self.model_weights.pop(name, None)
            
            # 如果使用Stacking，训练元模型
            if self.ensemble_config.get('method') == 'stacking' and self.meta_model is not None:
                self._train_meta_model(X_processed, y_processed)
            
            # 计算集成模型性能
            ensemble_metrics = self._evaluate_ensemble(X_processed, y_processed)
            
            training_time = time.time() - start_time
            
            # 保存训练指标
            self.training_metrics = {
                'ensemble_method': self.ensemble_config.get('method'),
                'base_model_metrics': base_model_metrics,
                'ensemble_metrics': ensemble_metrics,
                'model_weights': self.model_weights,
                'training_time': training_time,
                'active_models': list(self.model_weights.keys())
            }
            
            self.is_trained = True
            self.training_history['ensemble'] = self.training_metrics
            
            self.logger.info(f"集成模型训练完成: 集成准确率={ensemble_metrics.get('accuracy', 0):.4f}, 耗时={training_time:.2f}s")
            
            return self.training_metrics
            
        except Exception as e:
            self.logger.error(f"集成模型训练失败: {e}")
            raise
    
    def predict_probability(self, X: np.ndarray) -> np.ndarray:
        """
        预测概率分布
        
        Args:
            X: 特征矩阵
            
        Returns:
            概率分布数组，shape: (n_samples, 10)
        """
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        try:
            # 数据预处理
            X_processed = self._preprocess_features(X)
            
            # 获取所有基础模型的预测
            base_predictions = {}
            for name, model in self.base_models.items():
                if name in self.model_weights and model.is_trained:
                    try:
                        pred = model.predict_probability(X_processed)
                        base_predictions[name] = pred
                    except Exception as e:
                        self.logger.warning(f"基础模型 {name} 预测失败: {e}")
            
            if not base_predictions:
                raise ValueError("没有可用的基础模型进行预测")
            
            # 根据融合方法进行预测
            method = self.ensemble_config.get('method', 'weighted_average')
            
            if method == 'simple_average':
                probabilities = self._simple_average_predict(base_predictions)
            elif method == 'weighted_average':
                probabilities = self._weighted_average_predict(base_predictions)
            elif method == 'stacking':
                probabilities = self._stacking_predict(base_predictions, X_processed)
            else:
                # 默认使用加权平均
                probabilities = self._weighted_average_predict(base_predictions)
            
            return probabilities
            
        except Exception as e:
            self.logger.error(f"集成模型预测失败: {e}")
            raise
    
    def _train_meta_model(self, X: np.ndarray, y: np.ndarray):
        """训练元模型（用于Stacking）"""
        try:
            # 使用交叉验证生成元特征
            cv_folds = self.ensemble_config.get('cross_validation_folds', 5)
            meta_features = []
            
            # 这里简化实现，实际应该使用交叉验证
            X_train, X_val, y_train, y_val = train_test_split(
                X, y, test_size=0.2, random_state=42, stratify=y
            )
            
            # 获取验证集上的预测作为元特征
            val_predictions = []
            for name, model in self.base_models.items():
                if name in self.model_weights and model.is_trained:
                    pred = model.predict_probability(X_val)
                    val_predictions.append(pred)
            
            if val_predictions:
                meta_features = np.hstack(val_predictions)
                self.meta_model.fit(meta_features, y_val)
                self.logger.info("元模型训练完成")
            
        except Exception as e:
            self.logger.error(f"元模型训练失败: {e}")
    
    def _simple_average_predict(self, base_predictions: Dict[str, np.ndarray]) -> np.ndarray:
        """简单平均融合"""
        predictions = list(base_predictions.values())
        return np.mean(predictions, axis=0)
    
    def _weighted_average_predict(self, base_predictions: Dict[str, np.ndarray]) -> np.ndarray:
        """加权平均融合"""
        weighted_sum = None
        total_weight = 0
        
        for name, pred in base_predictions.items():
            weight = self.model_weights.get(name, 0)
            if weight > 0:
                if weighted_sum is None:
                    weighted_sum = weight * pred
                else:
                    weighted_sum += weight * pred
                total_weight += weight
        
        if total_weight > 0:
            return weighted_sum / total_weight
        else:
            return self._simple_average_predict(base_predictions)
    
    def _stacking_predict(self, base_predictions: Dict[str, np.ndarray], X: np.ndarray) -> np.ndarray:
        """Stacking融合"""
        if self.meta_model is None:
            return self._weighted_average_predict(base_predictions)
        
        try:
            # 构建元特征
            meta_features = np.hstack(list(base_predictions.values()))
            
            # 使用元模型预测
            meta_pred = self.meta_model.predict_proba(meta_features)
            return meta_pred
            
        except Exception as e:
            self.logger.warning(f"Stacking预测失败，回退到加权平均: {e}")
            return self._weighted_average_predict(base_predictions)
    
    def _evaluate_ensemble(self, X: np.ndarray, y: np.ndarray) -> Dict[str, float]:
        """评估集成模型性能"""
        try:
            # 分割数据用于评估
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, stratify=y
            )
            
            # 预测
            y_pred_proba = self.predict_probability(X_test)
            y_pred = np.argmax(y_pred_proba, axis=1)
            
            # 计算指标
            accuracy = accuracy_score(y_test, y_pred)
            
            # Top3准确率
            top3_accuracy = self._calculate_top3_accuracy(y_test, y_pred_proba)
            
            # 平均置信度
            max_probabilities = np.max(y_pred_proba, axis=1)
            avg_confidence = np.mean(max_probabilities)
            
            return {
                'accuracy': accuracy,
                'top3_accuracy': top3_accuracy,
                'avg_confidence': avg_confidence
            }
            
        except Exception as e:
            self.logger.error(f"集成模型评估失败: {e}")
            return {'accuracy': 0.0, 'top3_accuracy': 0.0, 'avg_confidence': 0.0}
    
    def _calculate_top3_accuracy(self, y_true: np.ndarray, y_pred_proba: np.ndarray) -> float:
        """计算Top3准确率"""
        top3_predictions = np.argsort(y_pred_proba, axis=1)[:, -3:]
        correct = 0
        
        for i, true_label in enumerate(y_true):
            if true_label in top3_predictions[i]:
                correct += 1
        
        return correct / len(y_true)
    
    def _preprocess_data(self, X: np.ndarray, y: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """数据预处理"""
        # 处理缺失值
        X_processed = np.nan_to_num(X, nan=0.0, posinf=0.0, neginf=0.0)
        
        # 标签编码
        y_processed = self.label_encoder.fit_transform(y)
        
        return X_processed, y_processed
    
    def _preprocess_features(self, X: np.ndarray) -> np.ndarray:
        """特征预处理（仅特征）"""
        return np.nan_to_num(X, nan=0.0, posinf=0.0, neginf=0.0)
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        base_info = super().get_model_info()
        base_info.update({
            'ensemble_method': self.ensemble_config.get('method'),
            'base_models': list(self.base_models.keys()),
            'model_weights': self.model_weights,
            'active_models': [name for name, model in self.base_models.items() 
                            if model.is_trained and name in self.model_weights]
        })
        return base_info
    
    def save_model(self, filepath: Optional[str] = None) -> str:
        """保存集成模型"""
        if not self.is_trained:
            raise ValueError("模型尚未训练，无法保存")
        
        if filepath is None:
            model_dir = Path(self.training_config.get('model_save_path', 'models/units/'))
            model_dir.mkdir(parents=True, exist_ok=True)
            filepath = model_dir / "ensemble_units_model"
        
        try:
            # 保存基础模型
            base_model_paths = {}
            for name, model in self.base_models.items():
                if model.is_trained:
                    model_path = model.save_model(f"{filepath}_{name}")
                    base_model_paths[name] = model_path
            
            # 保存集成模型信息
            ensemble_data = {
                'ensemble_config': self.ensemble_config,
                'model_weights': self.model_weights,
                'label_encoder': self.label_encoder,
                'training_metrics': self.training_metrics,
                'base_model_paths': base_model_paths,
                'meta_model': self.meta_model
            }
            
            with open(f"{filepath}_ensemble.pkl", 'wb') as f:
                pickle.dump(ensemble_data, f)
            
            self.logger.info(f"集成模型保存成功: {filepath}")
            return str(filepath)
            
        except Exception as e:
            self.logger.error(f"集成模型保存失败: {e}")
            raise
    
    def load_model(self, filepath: str) -> bool:
        """加载集成模型"""
        try:
            # 加载集成模型信息
            with open(f"{filepath}_ensemble.pkl", 'rb') as f:
                ensemble_data = pickle.load(f)
            
            self.ensemble_config = ensemble_data['ensemble_config']
            self.model_weights = ensemble_data['model_weights']
            self.label_encoder = ensemble_data['label_encoder']
            self.training_metrics = ensemble_data['training_metrics']
            self.meta_model = ensemble_data.get('meta_model')
            
            # 加载基础模型
            base_model_paths = ensemble_data['base_model_paths']
            for name, model_path in base_model_paths.items():
                if name in self.base_models:
                    self.base_models[name].load_model(model_path)
            
            self.is_trained = True
            
            self.logger.info(f"集成模型加载成功: {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"集成模型加载失败: {e}")
            return False
