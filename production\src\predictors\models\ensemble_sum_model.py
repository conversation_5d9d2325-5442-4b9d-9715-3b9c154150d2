#!/usr/bin/env python3
"""
集成融合和值模型

实现集成融合模型，组合多模型预测
实现加权平均和动态权重调整

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import numpy as np
import pandas as pd
import pickle
import logging
from typing import Dict, List, Tuple, Optional, Any
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import sqlite3
import json
from pathlib import Path

class EnsembleSumModel:
    """集成融合和值模型"""
    
    def __init__(self, db_path: str, config: Optional[Dict] = None):
        """
        初始化集成融合和值模型
        
        Args:
            db_path: 数据库路径
            config: 模型配置参数
        """
        self.db_path = db_path
        self.models = {}
        self.is_trained = False
        self.logger = logging.getLogger("EnsembleSumModel")
        
        # 默认模型权重
        self.model_weights = {
            'xgb': 0.3,
            'lgb': 0.3,
            'lstm': 0.2,
            'distribution': 0.1,
            'constraint': 0.1
        }
        
        # 更新配置参数
        if config and 'ensemble' in config and 'weights' in config['ensemble']:
            self.model_weights.update(config['ensemble']['weights'])
        
        # 动态权重调整参数
        self.dynamic_weights = False
        self.performance_history = {}
        self.weight_update_frequency = 10  # 每10次预测更新一次权重
        self.prediction_count = 0
    
    def add_model(self, model_name: str, model_instance):
        """
        添加子模型
        
        Args:
            model_name: 模型名称
            model_instance: 模型实例
        """
        self.models[model_name] = model_instance
        self.logger.info(f"添加子模型: {model_name}")
    
    def set_model_weights(self, weights: Dict[str, float]):
        """
        设置模型权重
        
        Args:
            weights: 权重字典
        """
        # 归一化权重
        total_weight = sum(weights.values())
        if total_weight > 0:
            self.model_weights = {k: v/total_weight for k, v in weights.items()}
        else:
            self.model_weights = weights
        
        self.logger.info(f"更新模型权重: {self.model_weights}")
    
    def enable_dynamic_weights(self, enable: bool = True):
        """启用动态权重调整"""
        self.dynamic_weights = enable
        self.logger.info(f"动态权重调整: {'启用' if enable else '禁用'}")
    
    def train(self, X: Optional[np.ndarray] = None, y: Optional[np.ndarray] = None) -> Dict[str, float]:
        """
        训练集成模型
        
        Args:
            X: 特征矩阵，如果为None则从数据库加载
            y: 目标向量，如果为None则从数据库加载
            
        Returns:
            训练性能指标
        """
        try:
            # 训练所有子模型
            performances = {}
            
            for model_name, model in self.models.items():
                if hasattr(model, 'train'):
                    self.logger.info(f"训练子模型: {model_name}")
                    performance = model.train(X, y)
                    performances[model_name] = performance
                    
                    # 记录性能历史
                    if model_name not in self.performance_history:
                        self.performance_history[model_name] = []
                    self.performance_history[model_name].append(performance)
                else:
                    self.logger.warning(f"模型 {model_name} 没有train方法")
            
            self.is_trained = True
            
            # 如果启用动态权重，根据性能调整权重
            if self.dynamic_weights:
                self.update_weights_by_performance(performances)
            
            # 评估集成模型
            if X is not None and y is not None:
                X_train, X_val, y_train, y_val = train_test_split(
                    X, y, test_size=0.2, random_state=42
                )
                ensemble_performance = self.evaluate(X_val, y_val)
            else:
                ensemble_performance = self.calculate_ensemble_performance(performances)
            
            self.logger.info(f"集成和值模型训练完成，性能: {ensemble_performance}")
            
            return ensemble_performance
            
        except Exception as e:
            self.logger.error(f"集成模型训练失败: {e}")
            raise
    
    def predict(self, X: np.ndarray) -> np.ndarray:
        """
        集成预测
        
        Args:
            X: 特征矩阵
            
        Returns:
            预测的和值数组
        """
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        try:
            predictions = {}
            
            # 获取所有子模型的预测
            for model_name, model in self.models.items():
                if hasattr(model, 'predict') and model.is_trained:
                    try:
                        pred = model.predict(X)
                        predictions[model_name] = pred
                    except Exception as e:
                        self.logger.warning(f"模型 {model_name} 预测失败: {e}")
                        # 使用默认预测值
                        predictions[model_name] = np.full(len(X), 13.5)
            
            if not predictions:
                raise ValueError("没有可用的子模型进行预测")
            
            # 加权融合
            ensemble_predictions = self.weighted_average(predictions)
            
            # 更新预测计数
            self.prediction_count += len(X)
            
            # 动态权重调整
            if (self.dynamic_weights and 
                self.prediction_count % self.weight_update_frequency == 0):
                self.update_weights_dynamically(predictions, X)
            
            return ensemble_predictions
            
        except Exception as e:
            self.logger.error(f"集成预测失败: {e}")
            raise
    
    def weighted_average(self, predictions: Dict[str, np.ndarray]) -> np.ndarray:
        """
        加权平均融合
        
        Args:
            predictions: 各模型预测结果
            
        Returns:
            融合后的预测结果
        """
        if not predictions:
            raise ValueError("没有预测结果可以融合")
        
        # 获取样本数量
        sample_count = len(next(iter(predictions.values())))
        ensemble_pred = np.zeros(sample_count)
        total_weight = 0
        
        for model_name, pred in predictions.items():
            weight = self.model_weights.get(model_name, 0)
            if weight > 0:
                ensemble_pred += weight * pred
                total_weight += weight
        
        # 归一化
        if total_weight > 0:
            ensemble_pred /= total_weight
        
        # 应用约束
        ensemble_pred = np.clip(ensemble_pred, 0, 27)
        
        return ensemble_pred
    
    def predict_with_confidence(self, X: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        预测和值及置信度
        
        Args:
            X: 特征矩阵
            
        Returns:
            预测值和置信度数组
        """
        predictions = self.predict(X)
        
        # 获取各模型的预测和置信度
        model_predictions = {}
        model_confidences = {}
        
        for model_name, model in self.models.items():
            if hasattr(model, 'predict_with_confidence') and model.is_trained:
                try:
                    pred, conf = model.predict_with_confidence(X)
                    model_predictions[model_name] = pred
                    model_confidences[model_name] = conf
                except Exception as e:
                    self.logger.warning(f"模型 {model_name} 置信度预测失败: {e}")
        
        # 计算集成置信度
        confidences = []
        for i in range(len(predictions)):
            # 基于模型一致性的置信度
            model_preds = [model_predictions[name][i] for name in model_predictions]
            if len(model_preds) > 1:
                pred_std = np.std(model_preds)
                consistency_conf = max(0.1, 1.0 - pred_std / 5.0)  # 标准差越小置信度越高
            else:
                consistency_conf = 0.5
            
            # 基于加权平均置信度
            weighted_conf = 0
            total_weight = 0
            for model_name in model_confidences:
                weight = self.model_weights.get(model_name, 0)
                if weight > 0:
                    weighted_conf += weight * model_confidences[model_name][i]
                    total_weight += weight
            
            if total_weight > 0:
                weighted_conf /= total_weight
            else:
                weighted_conf = 0.5
            
            # 综合置信度
            final_conf = 0.6 * weighted_conf + 0.4 * consistency_conf
            final_conf = max(0.1, min(0.9, final_conf))
            
            confidences.append(final_conf)
        
        return predictions, np.array(confidences)
    
    def update_weights_by_performance(self, performances: Dict[str, Dict]):
        """根据性能更新权重"""
        if not performances:
            return
        
        # 基于准确率调整权重
        accuracies = {}
        for model_name, perf in performances.items():
            accuracy = perf.get('accuracy', 0)
            accuracies[model_name] = accuracy
        
        if accuracies:
            # 归一化准确率作为权重
            total_accuracy = sum(accuracies.values())
            if total_accuracy > 0:
                new_weights = {name: acc/total_accuracy for name, acc in accuracies.items()}
                self.set_model_weights(new_weights)
    
    def update_weights_dynamically(self, predictions: Dict[str, np.ndarray], X: np.ndarray):
        """动态更新权重（基于最近的预测表现）"""
        # 这里可以实现更复杂的动态权重调整逻辑
        # 例如基于预测的稳定性、一致性等
        pass
    
    def calculate_ensemble_performance(self, performances: Dict[str, Dict]) -> Dict[str, float]:
        """计算集成性能"""
        if not performances:
            return {}
        
        # 加权平均各项指标
        metrics = ['accuracy', 'mae', 'rmse', 'r2_score', 'accuracy_1', 'accuracy_2']
        ensemble_perf = {}
        
        for metric in metrics:
            weighted_metric = 0
            total_weight = 0
            
            for model_name, perf in performances.items():
                if metric in perf:
                    weight = self.model_weights.get(model_name, 0)
                    weighted_metric += weight * perf[metric]
                    total_weight += weight
            
            if total_weight > 0:
                ensemble_perf[metric] = weighted_metric / total_weight
        
        return ensemble_perf
    
    def evaluate(self, X_test: np.ndarray, y_test: np.ndarray) -> Dict[str, float]:
        """评估集成模型性能"""
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        try:
            y_pred = self.predict(X_test)
            
            # 计算各种误差指标
            mae = mean_absolute_error(y_test, y_pred)
            rmse = np.sqrt(mean_squared_error(y_test, y_pred))
            r2 = r2_score(y_test, y_pred)
            
            # 计算不同精度的准确率
            accuracy_1 = np.mean(np.abs(y_test - y_pred) <= 1)
            accuracy_2 = np.mean(np.abs(y_test - y_pred) <= 2)
            
            return {
                'mae': mae,
                'rmse': rmse,
                'r2_score': r2,
                'accuracy_1': accuracy_1,
                'accuracy_2': accuracy_2,
                'accuracy': accuracy_1  # 主要准确率指标
            }
            
        except Exception as e:
            self.logger.error(f"集成模型评估失败: {e}")
            raise
    
    def get_model_contributions(self, X: np.ndarray) -> Dict[str, np.ndarray]:
        """获取各模型的贡献"""
        contributions = {}
        
        for model_name, model in self.models.items():
            if hasattr(model, 'predict') and model.is_trained:
                try:
                    pred = model.predict(X)
                    weight = self.model_weights.get(model_name, 0)
                    contributions[model_name] = {
                        'prediction': pred,
                        'weight': weight,
                        'contribution': weight * pred
                    }
                except Exception as e:
                    self.logger.warning(f"获取模型 {model_name} 贡献失败: {e}")
        
        return contributions
    
    def save_model(self, filepath: str) -> bool:
        """保存集成模型"""
        if not self.is_trained:
            self.logger.warning("模型尚未训练，无法保存")
            return False
        
        try:
            Path(filepath).parent.mkdir(parents=True, exist_ok=True)
            
            # 保存集成模型配置
            ensemble_data = {
                'model_weights': self.model_weights,
                'dynamic_weights': self.dynamic_weights,
                'performance_history': self.performance_history,
                'is_trained': self.is_trained
            }
            
            with open(filepath, 'wb') as f:
                pickle.dump(ensemble_data, f)
            
            # 保存各子模型
            model_dir = Path(filepath).parent / "sub_models"
            model_dir.mkdir(exist_ok=True)
            
            for model_name, model in self.models.items():
                if hasattr(model, 'save_model'):
                    model_path = model_dir / f"{model_name}_model.pkl"
                    model.save_model(str(model_path))
            
            self.logger.info(f"集成模型保存成功: {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"集成模型保存失败: {e}")
            return False
    
    def load_model(self, filepath: str) -> bool:
        """加载集成模型"""
        try:
            with open(filepath, 'rb') as f:
                ensemble_data = pickle.load(f)
            
            self.model_weights = ensemble_data['model_weights']
            self.dynamic_weights = ensemble_data['dynamic_weights']
            self.performance_history = ensemble_data['performance_history']
            self.is_trained = ensemble_data['is_trained']
            
            self.logger.info(f"集成模型加载成功: {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"集成模型加载失败: {e}")
            return False
