#!/usr/bin/env python3
"""
小规模预测测试脚本

执行5-10次预测测试，验证P8融合系统的基本功能
收集性能数据和准确率信息

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import os
import sys
import time
import json
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

try:
    from src.fusion.fusion_predictor import FusionPredictor
    from src.data.fusion_data_access import FusionDataAccess
    FUSION_AVAILABLE = True
except ImportError as e:
    print(f"警告: 无法导入融合系统: {e}")
    FUSION_AVAILABLE = False

class PredictionTester:
    """预测测试器"""
    
    def __init__(self, db_path: str = "data/lottery.db"):
        """
        初始化预测测试器
        
        Args:
            db_path: 数据库路径
        """
        self.project_root = Path(__file__).parent.parent
        self.db_path = self.project_root / db_path
        self.reports_dir = self.project_root / "reports"
        self.logs_dir = self.project_root / "logs"
        
        # 确保目录存在
        self.reports_dir.mkdir(exist_ok=True)
        self.logs_dir.mkdir(exist_ok=True)
        
        # 设置日志
        self._setup_logging()
        self.logger = logging.getLogger(__name__)
        
        # 测试结果
        self.test_results = []
        
    def _setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler(self.logs_dir / 'prediction_test.log')
            ]
        )
    
    def test_database_connection(self) -> bool:
        """测试数据库连接"""
        try:
            import sqlite3
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM lottery_data")
                count = cursor.fetchone()[0]
                
                self.logger.info(f"数据库连接成功，历史数据: {count} 条")
                return count > 0
                
        except Exception as e:
            self.logger.error(f"数据库连接失败: {e}")
            return False
    
    def test_fusion_predictor(self) -> Dict[str, Any]:
        """测试P8融合预测器"""
        test_result = {
            'test_name': 'P8融合预测器测试',
            'start_time': datetime.now().isoformat(),
            'success': False,
            'error': None,
            'predictions': [],
            'performance': {}
        }
        
        try:
            if not FUSION_AVAILABLE:
                test_result['error'] = "融合系统模块不可用"
                return test_result
            
            # 创建融合预测器
            start_time = time.time()
            fusion_predictor = FusionPredictor(str(self.db_path))
            init_time = time.time() - start_time
            
            self.logger.info(f"融合预测器初始化完成，耗时: {init_time:.2f}秒")
            
            # 执行预测测试
            test_issues = ['2024095', '2024096', '2024097', '2024098', '2024099']
            
            for issue in test_issues:
                try:
                    pred_start = time.time()
                    
                    # 执行预测
                    result = fusion_predictor.predict_next_period(issue)
                    
                    pred_time = time.time() - pred_start
                    
                    prediction_result = {
                        'issue': issue,
                        'prediction_time': pred_time,
                        'success': True,
                        'top_predictions': result.get('top_predictions', [])[:5] if result else [],
                        'confidence_score': result.get('confidence_score', 0) if result else 0
                    }
                    
                    test_result['predictions'].append(prediction_result)
                    self.logger.info(f"期号 {issue} 预测完成，耗时: {pred_time:.2f}秒")
                    
                except Exception as e:
                    self.logger.error(f"期号 {issue} 预测失败: {e}")
                    test_result['predictions'].append({
                        'issue': issue,
                        'success': False,
                        'error': str(e)
                    })
            
            # 计算性能指标
            successful_predictions = [p for p in test_result['predictions'] if p.get('success', False)]
            
            if successful_predictions:
                avg_time = sum(p['prediction_time'] for p in successful_predictions) / len(successful_predictions)
                test_result['performance'] = {
                    'total_tests': len(test_issues),
                    'successful_tests': len(successful_predictions),
                    'success_rate': len(successful_predictions) / len(test_issues),
                    'average_prediction_time': avg_time,
                    'initialization_time': init_time
                }
                test_result['success'] = True
            
        except Exception as e:
            test_result['error'] = str(e)
            self.logger.error(f"融合预测器测试失败: {e}")
        
        test_result['end_time'] = datetime.now().isoformat()
        return test_result
    
    def test_individual_predictors(self) -> Dict[str, Any]:
        """测试独立预测器"""
        test_result = {
            'test_name': '独立预测器测试',
            'start_time': datetime.now().isoformat(),
            'success': False,
            'predictors': {}
        }
        
        # 测试预测器列表
        predictors_to_test = [
            ('sum_predictor', 'src.predictors.sum_predictor', 'SumPredictor'),
            ('span_predictor', 'src.predictors.span_predictor', 'SpanPredictor'),
        ]
        
        for predictor_name, module_path, class_name in predictors_to_test:
            predictor_result = {
                'name': predictor_name,
                'success': False,
                'error': None,
                'test_predictions': []
            }
            
            try:
                # 动态导入预测器
                module = __import__(module_path, fromlist=[class_name])
                predictor_class = getattr(module, class_name)
                
                # 创建预测器实例
                predictor = predictor_class(str(self.db_path))
                
                # 执行简单预测测试
                test_result_pred = predictor.predict('2024099')
                
                predictor_result['success'] = True
                predictor_result['test_predictions'] = [test_result_pred] if test_result_pred else []
                
                self.logger.info(f"{predictor_name} 测试成功")
                
            except Exception as e:
                predictor_result['error'] = str(e)
                self.logger.warning(f"{predictor_name} 测试失败: {e}")
            
            test_result['predictors'][predictor_name] = predictor_result
        
        # 检查是否有成功的预测器
        successful_predictors = sum(1 for p in test_result['predictors'].values() if p['success'])
        test_result['success'] = successful_predictors > 0
        
        test_result['end_time'] = datetime.now().isoformat()
        return test_result
    
    def test_system_performance(self) -> Dict[str, Any]:
        """测试系统性能"""
        test_result = {
            'test_name': '系统性能测试',
            'start_time': datetime.now().isoformat(),
            'success': False,
            'metrics': {}
        }
        
        try:
            import psutil
            
            # 获取系统性能指标
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            test_result['metrics'] = {
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'memory_available_gb': memory.available / (1024**3),
                'disk_percent': disk.percent,
                'disk_free_gb': disk.free / (1024**3),
                'process_count': len(psutil.pids())
            }
            
            # 检查性能阈值
            performance_ok = (
                cpu_percent < 90 and
                memory.percent < 90 and
                disk.percent < 95
            )
            
            test_result['success'] = performance_ok
            test_result['performance_status'] = 'good' if performance_ok else 'warning'
            
            self.logger.info(f"系统性能测试完成: CPU={cpu_percent:.1f}%, 内存={memory.percent:.1f}%")
            
        except Exception as e:
            test_result['error'] = str(e)
            self.logger.error(f"系统性能测试失败: {e}")
        
        test_result['end_time'] = datetime.now().isoformat()
        return test_result
    
    def run_all_tests(self) -> Dict[str, Any]:
        """运行所有测试"""
        self.logger.info("开始小规模预测测试")
        
        overall_result = {
            'test_suite': '小规模预测测试',
            'start_time': datetime.now().isoformat(),
            'tests': [],
            'summary': {}
        }
        
        # 1. 测试数据库连接
        self.logger.info("1. 测试数据库连接...")
        db_ok = self.test_database_connection()
        overall_result['tests'].append({
            'name': '数据库连接测试',
            'success': db_ok
        })
        
        if not db_ok:
            overall_result['summary'] = {
                'total_tests': 1,
                'passed_tests': 0,
                'success_rate': 0.0,
                'overall_status': 'failed',
                'critical_error': '数据库连接失败'
            }
            return overall_result
        
        # 2. 测试融合预测器
        self.logger.info("2. 测试P8融合预测器...")
        fusion_result = self.test_fusion_predictor()
        overall_result['tests'].append(fusion_result)
        
        # 3. 测试独立预测器
        self.logger.info("3. 测试独立预测器...")
        individual_result = self.test_individual_predictors()
        overall_result['tests'].append(individual_result)
        
        # 4. 测试系统性能
        self.logger.info("4. 测试系统性能...")
        performance_result = self.test_system_performance()
        overall_result['tests'].append(performance_result)
        
        # 计算总体结果
        total_tests = len(overall_result['tests'])
        passed_tests = sum(1 for test in overall_result['tests'] if test.get('success', False))
        
        overall_result['summary'] = {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'success_rate': passed_tests / total_tests if total_tests > 0 else 0,
            'overall_status': 'passed' if passed_tests >= total_tests * 0.75 else 'failed'
        }
        
        overall_result['end_time'] = datetime.now().isoformat()
        
        # 保存测试结果
        self._save_test_results(overall_result)
        
        return overall_result
    
    def _save_test_results(self, results: Dict[str, Any]):
        """保存测试结果"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = self.reports_dir / f"prediction_test_{timestamp}.json"
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"测试结果已保存到: {filename}")
            
        except Exception as e:
            self.logger.error(f"保存测试结果失败: {e}")


def main():
    """主函数"""
    print("🚀 开始小规模预测测试...")
    print("=" * 50)
    
    tester = PredictionTester()
    results = tester.run_all_tests()
    
    # 输出测试结果
    print("\n📊 测试结果摘要:")
    print(f"总测试数: {results['summary']['total_tests']}")
    print(f"通过测试: {results['summary']['passed_tests']}")
    print(f"成功率: {results['summary']['success_rate']:.1%}")
    print(f"总体状态: {results['summary']['overall_status']}")
    
    if results['summary']['overall_status'] == 'passed':
        print("\n✅ 小规模预测测试通过！")
        print("系统基本功能正常，可以进入下一阶段。")
    else:
        print("\n❌ 小规模预测测试失败！")
        print("请检查错误信息并修复问题。")
    
    return results['summary']['overall_status'] == 'passed'


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
