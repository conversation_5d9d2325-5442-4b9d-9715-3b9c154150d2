# P7跨度预测器 - 福彩3D跨度预测系统

## 🎯 项目概述

P7跨度预测器是福彩3D预测系统的核心组件之一，专门用于预测福彩3D开奖号码的跨度值（最大数字与最小数字的差值）。该预测器基于BaseIndependentPredictor架构开发，集成了6种先进的机器学习模型，并具备独特的双重约束优化和模式分析功能。

### 🌟 核心特色

- **6种预测模型**：XGBoost、LightGBM、LSTM、分类预测、约束优化、集成融合
- **双重约束优化**：与P3-P5位置预测器和P6和值预测器协同工作
- **模式分析功能**：识别升序、降序、相同数字、连续数字等模式
- **分类预测支持**：10分类问题，支持Top-K预测和概率分布
- **约束一致性评分**：评估预测结果与位置、和值预测的一致性

## 📋 系统要求

### 基础环境
- Python 3.8+
- SQLite 3
- 8GB+ RAM（推荐）

### 必需依赖
```bash
pip install numpy pandas scikit-learn pyyaml
```

### 可选依赖（用于特定模型）
```bash
# XGBoost和LightGBM模型
pip install xgboost lightgbm

# LSTM模型
pip install tensorflow

# 数据可视化
pip install matplotlib seaborn
```

## 🚀 快速开始

### 1. 基础使用

```python
from src.predictors.span_predictor import SpanPredictor

# 初始化预测器
predictor = SpanPredictor(
    db_path="data/lottery.db",
    config_path="config/span_predictor_config.yaml"
)

# 构建模型
predictor.build_model()

# 训练所有模型
training_results = predictor.train_all_models()
print(f"训练完成: {training_results}")

# 预测下一期跨度
result = predictor.predict_next_period_with_constraints("2025001")
print(f"预测跨度: {result['optimized_prediction']:.2f}")
print(f"置信度: {result['base_confidence']:.3f}")
```

### 2. 命令行使用

```bash
# 训练所有模型
python scripts/train_span_predictor.py --db-path data/lottery.db --save-models

# 单期预测
python scripts/predict_span.py --db-path data/lottery.db --issue 2025001 --enable-constraints

# 批量预测
python scripts/predict_span.py --db-path data/lottery.db --batch --start-issue 2025001 --count 10

# 模型评估
python scripts/evaluate_span_predictor.py --db-path data/lottery.db --evaluate-constraints

# 统一命令行工具
python scripts/span_predictor_cli.py train --db-path data/lottery.db
python scripts/span_predictor_cli.py predict --db-path data/lottery.db --issue 2025001
```

## 🔧 配置说明

### 主要配置文件：`config/span_predictor_config.yaml`

```yaml
span_predictor:
  # 跨度专属配置
  span_config:
    enable_classification: true      # 启用分类预测
    enable_pattern_analysis: true    # 启用模式分析
    enable_dual_constraints: true    # 启用双重约束
    
    # 双重约束配置
    dual_constraints:
      position_weight: 0.4           # 位置约束权重
      sum_weight: 0.4                # 和值约束权重
      span_weight: 0.2               # 跨度自身权重
  
  # 模型配置
  models:
    xgb:
      n_estimators: 200
      max_depth: 6
      learning_rate: 0.1
    
    ensemble:
      weights:
        xgb: 0.3
        lgb: 0.3
        lstm: 0.2
        classification: 0.2
```

## 🎯 核心功能详解

### 1. 双重约束优化（专属特征）

P7跨度预测器的最大特色是双重约束优化功能，它能够与P3-P5位置预测器和P6和值预测器协同工作：

```python
# 启用双重约束
predictor.enable_dual_constraints(True)

# 设置位置预测器
predictor.set_position_predictors(hundreds_predictor, tens_predictor, units_predictor)

# 设置和值预测器
predictor.set_sum_predictor(sum_predictor)

# 使用约束优化预测
result = predictor.predict_next_period_with_constraints("2025001")

# 查看约束一致性评分
constraint_info = result['constraint_info']
print(f"位置一致性: {constraint_info['position_consistency']:.3f}")
print(f"和值一致性: {constraint_info['sum_consistency']:.3f}")
print(f"双重一致性: {constraint_info['dual_consistency']:.3f}")
```

### 2. 模式分析功能（专属特征）

跨度预测器能够识别和分析多种数字模式：

```python
# 启用模式分析
predictor.enable_pattern_analysis(True)

# 分析数字组合的模式
pattern_result = predictor.predict_pattern_probability([1, 2, 3])
print(f"升序模式: {pattern_result['ascending']}")
print(f"预测跨度: {pattern_result['predicted_span']}")

# 全面模式分析
comprehensive_analysis = predictor.analyze_comprehensive_patterns("2025001", 50)
print(f"历史平均跨度: {comprehensive_analysis['basic_statistics']['mean']:.2f}")
print(f"跨度分布熵: {comprehensive_analysis['basic_statistics']['entropy']:.3f}")
```

### 3. 分类预测模型（专属特征）

支持10分类预测和Top-K预测：

```python
# 切换到分类模型
predictor.switch_model('classification')

# 获取概率分布
X = predictor._get_prediction_features("2025001")
probabilities = predictor.predict_probability(X.reshape(1, -1))
print(f"跨度概率分布: {probabilities[0]}")

# Top-3预测
classification_model = predictor.models['classification']
top_k_indices, top_k_probs = classification_model.predict_top_k(X.reshape(1, -1), k=3)
print("Top-3预测:")
for i, (idx, prob) in enumerate(zip(top_k_indices[0], top_k_probs[0])):
    print(f"  {i+1}. 跨度{idx}: {prob:.3f}")
```

### 4. 集成模型管理

```python
# 获取所有模型的预测
all_predictions = predictor.predict_with_all_models(X)
print("所有模型预测:")
for model_name, pred in all_predictions.items():
    print(f"  {model_name}: {pred[0]:.2f}")

# 获取集成预测详情
ensemble_details = predictor.get_ensemble_prediction_details(X)
print(f"集成预测: {ensemble_details['ensemble_prediction'][0]:.2f}")
print(f"模型权重: {ensemble_details['model_weights']}")

# 自动选择最佳模型
predictor.auto_select_best_model(metric='accuracy')
print(f"最佳模型: {predictor.current_model}")
```

## 📊 性能评估

### 1. 模型性能比较

```python
# 获取所有模型性能比较
performance = predictor.get_model_performance_comparison()
for model_name, metrics in performance.items():
    if 'error' not in metrics:
        print(f"{model_name}: MAE={metrics['mae']:.4f}, 准确率={metrics['accuracy']:.4f}")

# 评估约束优化有效性
constraint_eval = predictor.evaluate_constraint_effectiveness()
improvement = constraint_eval['improvement']
print(f"约束优化改进: MAE改进{improvement['mae_improvement']:.4f}")
```

### 2. 交叉验证

```bash
# 执行5折交叉验证
python scripts/evaluate_span_predictor.py \
    --db-path data/lottery.db \
    --cross-validation \
    --cv-folds 5 \
    --output cv_results.json
```

## 🔍 高级功能

### 1. 约束一致性分析

```python
# 计算约束一致性评分
position_preds = {'hundreds': 1, 'tens': 2, 'units': 3}
sum_pred = 6.0
span_pred = 2.0

consistency_scores = predictor.calculate_constraint_consistency_score(
    span_pred, position_preds, sum_pred
)
print(f"约束一致性评分: {consistency_scores}")
```

### 2. 动态权重调整

```python
# 获取集成模型
ensemble_model = predictor.models['ensemble']

# 查看当前权重
current_weights = ensemble_model.get_model_weights()
print(f"当前权重: {current_weights}")

# 手动调整权重
new_weights = {'xgb': 0.4, 'lgb': 0.3, 'lstm': 0.2, 'classification': 0.1}
ensemble_model.set_model_weights(new_weights)

# 启用动态权重调整
ensemble_model.enable_dynamic_weights(True)
```

### 3. 模型保存和加载

```python
# 保存所有模型
saved_paths = predictor.save_all_models("models/span_predictor")
print(f"已保存模型: {list(saved_paths.keys())}")

# 加载所有模型
load_results = predictor.load_all_models("models/span_predictor")
print(f"加载结果: {load_results}")

# 保存单个模型
model_path = predictor.save_model('ensemble', 'models/ensemble_span.pkl')
print(f"集成模型已保存到: {model_path}")
```

## 🧪 测试和验证

### 运行测试套件

```bash
# 运行完整测试
python tests/test_span_predictor.py

# 运行特定测试类
python -m unittest tests.test_span_predictor.TestSpanPredictor

# 运行性能测试
python tests/test_span_predictor.py --performance-only
```

### 测试覆盖范围

- ✅ 数据访问层测试
- ✅ 预测器主类测试
- ✅ 所有6种模型接口测试
- ✅ 双重约束功能测试
- ✅ 模式分析功能测试
- ✅ 集成测试
- ✅ 性能测试

## 📈 使用示例

### 完整预测流程示例

```python
import numpy as np
from src.predictors.span_predictor import SpanPredictor

def complete_prediction_example():
    """完整的跨度预测示例"""
    
    # 1. 初始化预测器
    predictor = SpanPredictor("data/lottery.db")
    
    # 2. 配置预测器
    predictor.enable_dual_constraints(True)
    predictor.enable_pattern_analysis(True)
    
    # 3. 构建和训练模型
    predictor.build_model()
    training_results = predictor.train_all_models()
    
    # 4. 模型性能评估
    performance = predictor.get_model_performance_comparison()
    best_model = predictor.get_best_model('accuracy')
    predictor.switch_model(best_model)
    
    # 5. 执行预测
    issue = "2025001"
    result = predictor.predict_next_period_with_constraints(issue)
    
    # 6. 输出结果
    print(f"期号: {result['issue']}")
    print(f"预测跨度: {result['optimized_prediction']:.2f}")
    print(f"置信度: {result['base_confidence']:.3f}")
    print(f"使用模型: {result['model_type']}")
    
    if result.get('dual_constraint_enabled'):
        constraint_info = result['constraint_info']
        print(f"约束一致性: {constraint_info['dual_consistency']:.3f}")
    
    if 'pattern_analysis' in result:
        pattern = result['pattern_analysis']
        print(f"历史平均跨度: {pattern['avg_span']:.2f}")
    
    if 'top_3_predictions' in result:
        print("Top-3预测:")
        for i, pred in enumerate(result['top_3_predictions'], 1):
            print(f"  {i}. 跨度{pred['span']}: {pred['probability']:.3f}")
    
    return result

# 运行示例
if __name__ == "__main__":
    result = complete_prediction_example()
```

## 🔧 故障排除

### 常见问题

1. **模型训练失败**
   ```bash
   # 检查数据是否充足
   python scripts/span_predictor_cli.py info --db-path data/lottery.db --show-data
   
   # 使用较少的数据进行测试
   python scripts/train_span_predictor.py --db-path data/lottery.db --data-limit 100
   ```

2. **依赖包缺失**
   ```bash
   # 安装所有可选依赖
   pip install xgboost lightgbm tensorflow matplotlib seaborn
   ```

3. **内存不足**
   ```yaml
   # 在配置文件中减少模型复杂度
   span_predictor:
     models:
       xgb:
         n_estimators: 50  # 减少树的数量
         max_depth: 4      # 减少树的深度
   ```

### 性能优化建议

1. **数据预处理优化**：使用数据缓存减少重复加载
2. **模型选择**：根据数据量选择合适的模型
3. **并行训练**：利用多核CPU加速训练
4. **内存管理**：及时释放不需要的模型

## 📚 API参考

详细的API文档请参考：
- [SpanPredictor主类API](api/span_predictor.md)
- [模型接口API](api/span_models.md)
- [数据访问API](api/span_data_access.md)
- [配置参数说明](api/configuration.md)

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证 - 详见 [LICENSE](LICENSE) 文件

## 📞 支持

如有问题或建议，请：
1. 查看[常见问题](FAQ.md)
2. 提交[Issue](https://github.com/your-repo/issues)
3. 联系开发团队

---

**P7跨度预测器** - 让福彩3D跨度预测更智能、更准确！
