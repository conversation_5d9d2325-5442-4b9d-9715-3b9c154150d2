#!/usr/bin/env python3
"""
P7跨度预测器数据库表创建脚本

创建4个专用数据库表：
- span_predictions: 跨度预测结果表
- span_model_performance: 跨度模型性能表
- span_distribution_stats: 跨度分布统计表
- span_constraint_rules: 跨度约束规则表

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import sqlite3
import os
import sys
from pathlib import Path

def create_span_predictor_tables():
    """创建P7跨度预测器数据库表"""
    
    # 确保在正确的目录
    project_root = Path(__file__).parent.parent
    db_path = project_root / "data" / "lottery.db"
    
    if not db_path.exists():
        print(f"错误：数据库文件不存在 {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # 读取SQL脚本
        sql_file = project_root / "database" / "migrations" / "create_span_predictor_tables.sql"
        with open(sql_file, 'r', encoding='utf-8') as f:
            sql_script = f.read()
        
        # 执行SQL脚本
        cursor.executescript(sql_script)
        conn.commit()
        
        print("✅ P7跨度预测器数据库表创建成功")
        
        # 验证表是否创建成功
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE 'span_%'")
        tables = cursor.fetchall()
        
        print("📋 新创建的表:")
        for table in tables:
            print(f"  - {table[0]}")
            
        # 检查表结构
        for table in tables:
            table_name = table[0]
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            print(f"\n📊 {table_name} 表结构:")
            for col in columns:
                print(f"  - {col[1]} ({col[2]})")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 创建数据库表失败: {e}")
        return False

if __name__ == "__main__":
    success = create_span_predictor_tables()
    sys.exit(0 if success else 1)
