#!/usr/bin/env python3
"""
告警系统脚本

实现系统告警、通知和响应功能
为P8智能交集融合系统提供完整的告警支持

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import os
import sys
import json
import time
import logging
import smtplib
import sqlite3
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Callable
from email.mime.text import MimeText
from email.mime.multipart import MimeMultipart
import argparse

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

class AlertSystem:
    """告警系统"""
    
    def __init__(self, config_path: str = "config/alert_config.json"):
        """
        初始化告警系统
        
        Args:
            config_path: 告警配置文件路径
        """
        self.project_root = Path(__file__).parent.parent
        self.config_path = self.project_root / config_path
        self.logs_dir = self.project_root / "logs"
        self.alerts_db = self.project_root / "data" / "alerts.db"
        
        # 确保目录存在
        self.logs_dir.mkdir(exist_ok=True)
        (self.project_root / "data").mkdir(exist_ok=True)
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler(self.logs_dir / 'alert_system.log')
            ]
        )
        self.logger = logging.getLogger('AlertSystem')
        
        # 加载配置
        self.config = self._load_config()
        
        # 初始化数据库
        self._init_database()
        
        # 告警处理器
        self.alert_handlers = {
            'email': self._send_email_alert,
            'log': self._log_alert,
            'console': self._console_alert,
            'webhook': self._webhook_alert
        }
    
    def _load_config(self) -> Dict[str, Any]:
        """加载告警配置"""
        default_config = {
            'email': {
                'enabled': False,
                'smtp_server': 'smtp.gmail.com',
                'smtp_port': 587,
                'username': '',
                'password': '',
                'from_email': '',
                'to_emails': []
            },
            'webhook': {
                'enabled': False,
                'url': '',
                'timeout': 10
            },
            'alert_levels': {
                'INFO': {'priority': 1, 'handlers': ['log']},
                'WARNING': {'priority': 2, 'handlers': ['log', 'console']},
                'ERROR': {'priority': 3, 'handlers': ['log', 'console', 'email']},
                'CRITICAL': {'priority': 4, 'handlers': ['log', 'console', 'email', 'webhook']}
            },
            'cooldown_minutes': 15,  # 相同告警的冷却时间
            'max_alerts_per_hour': 50  # 每小时最大告警数量
        }
        
        try:
            if self.config_path.exists():
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                # 合并配置
                default_config.update(user_config)
        except Exception as e:
            self.logger.warning(f"加载告警配置失败，使用默认配置: {e}")
        
        return default_config
    
    def _init_database(self):
        """初始化告警数据库"""
        try:
            with sqlite3.connect(self.alerts_db) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS alerts (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        alert_type TEXT NOT NULL,
                        level TEXT NOT NULL,
                        message TEXT NOT NULL,
                        details TEXT,
                        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                        resolved BOOLEAN DEFAULT FALSE,
                        resolved_at DATETIME,
                        source TEXT
                    )
                ''')
                
                cursor.execute('''
                    CREATE INDEX IF NOT EXISTS idx_alerts_timestamp 
                    ON alerts(timestamp)
                ''')
                
                cursor.execute('''
                    CREATE INDEX IF NOT EXISTS idx_alerts_type_level 
                    ON alerts(alert_type, level)
                ''')
                
                conn.commit()
                
        except Exception as e:
            self.logger.error(f"初始化告警数据库失败: {e}")
    
    def send_alert(self, alert_type: str, level: str, message: str, 
                   details: Optional[Dict[str, Any]] = None, 
                   source: str = "system") -> bool:
        """
        发送告警
        
        Args:
            alert_type: 告警类型
            level: 告警级别 (INFO, WARNING, ERROR, CRITICAL)
            message: 告警消息
            details: 详细信息
            source: 告警来源
            
        Returns:
            bool: 是否发送成功
        """
        try:
            # 检查告警频率限制
            if not self._check_rate_limit(alert_type, level):
                self.logger.warning(f"告警频率超限，跳过: {alert_type}")
                return False
            
            # 检查冷却时间
            if not self._check_cooldown(alert_type, level):
                self.logger.debug(f"告警在冷却期内，跳过: {alert_type}")
                return False
            
            # 创建告警记录
            alert_record = {
                'alert_type': alert_type,
                'level': level,
                'message': message,
                'details': json.dumps(details) if details else None,
                'timestamp': datetime.now().isoformat(),
                'source': source
            }
            
            # 保存到数据库
            self._save_alert(alert_record)
            
            # 获取处理器列表
            level_config = self.config['alert_levels'].get(level, {})
            handlers = level_config.get('handlers', ['log'])
            
            # 执行告警处理
            success = True
            for handler_name in handlers:
                if handler_name in self.alert_handlers:
                    try:
                        self.alert_handlers[handler_name](alert_record)
                    except Exception as e:
                        self.logger.error(f"告警处理器 {handler_name} 执行失败: {e}")
                        success = False
            
            return success
            
        except Exception as e:
            self.logger.error(f"发送告警失败: {e}")
            return False
    
    def _check_rate_limit(self, alert_type: str, level: str) -> bool:
        """检查告警频率限制"""
        try:
            one_hour_ago = datetime.now() - timedelta(hours=1)
            
            with sqlite3.connect(self.alerts_db) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT COUNT(*) FROM alerts 
                    WHERE timestamp > ? AND alert_type = ? AND level = ?
                ''', (one_hour_ago.isoformat(), alert_type, level))
                
                count = cursor.fetchone()[0]
                max_alerts = self.config['max_alerts_per_hour']
                
                return count < max_alerts
                
        except Exception as e:
            self.logger.error(f"检查告警频率限制失败: {e}")
            return True  # 出错时允许发送
    
    def _check_cooldown(self, alert_type: str, level: str) -> bool:
        """检查告警冷却时间"""
        try:
            cooldown_minutes = self.config['cooldown_minutes']
            cooldown_time = datetime.now() - timedelta(minutes=cooldown_minutes)
            
            with sqlite3.connect(self.alerts_db) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT timestamp FROM alerts 
                    WHERE alert_type = ? AND level = ? AND timestamp > ?
                    ORDER BY timestamp DESC LIMIT 1
                ''', (alert_type, level, cooldown_time.isoformat()))
                
                result = cursor.fetchone()
                return result is None
                
        except Exception as e:
            self.logger.error(f"检查告警冷却时间失败: {e}")
            return True  # 出错时允许发送
    
    def _save_alert(self, alert_record: Dict[str, Any]):
        """保存告警记录到数据库"""
        try:
            with sqlite3.connect(self.alerts_db) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO alerts (alert_type, level, message, details, timestamp, source)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    alert_record['alert_type'],
                    alert_record['level'],
                    alert_record['message'],
                    alert_record['details'],
                    alert_record['timestamp'],
                    alert_record['source']
                ))
                conn.commit()
                
        except Exception as e:
            self.logger.error(f"保存告警记录失败: {e}")
    
    def _log_alert(self, alert_record: Dict[str, Any]):
        """记录告警到日志"""
        level = alert_record['level']
        message = f"[{alert_record['alert_type']}] {alert_record['message']}"
        
        if level == 'INFO':
            self.logger.info(message)
        elif level == 'WARNING':
            self.logger.warning(message)
        elif level == 'ERROR':
            self.logger.error(message)
        elif level == 'CRITICAL':
            self.logger.critical(message)
    
    def _console_alert(self, alert_record: Dict[str, Any]):
        """输出告警到控制台"""
        level = alert_record['level']
        timestamp = alert_record['timestamp']
        message = alert_record['message']
        
        # 使用不同的图标表示不同级别
        icons = {
            'INFO': 'ℹ️',
            'WARNING': '⚠️',
            'ERROR': '❌',
            'CRITICAL': '🚨'
        }
        
        icon = icons.get(level, '📢')
        print(f"{icon} [{timestamp}] {level}: {message}")
    
    def _send_email_alert(self, alert_record: Dict[str, Any]):
        """发送邮件告警"""
        if not self.config['email']['enabled']:
            return
        
        try:
            # 构建邮件内容
            subject = f"[{alert_record['level']}] 福彩3D系统告警: {alert_record['alert_type']}"
            
            body = f"""
系统告警通知

告警类型: {alert_record['alert_type']}
告警级别: {alert_record['level']}
告警时间: {alert_record['timestamp']}
告警来源: {alert_record['source']}

告警消息:
{alert_record['message']}

详细信息:
{alert_record.get('details', '无')}

请及时处理相关问题。

---
福彩3D预测系统自动告警
            """.strip()
            
            # 发送邮件
            self._send_email(subject, body)
            
        except Exception as e:
            self.logger.error(f"发送邮件告警失败: {e}")
    
    def _send_email(self, subject: str, body: str):
        """发送邮件"""
        email_config = self.config['email']
        
        msg = MimeMultipart()
        msg['From'] = email_config['from_email']
        msg['To'] = ', '.join(email_config['to_emails'])
        msg['Subject'] = subject
        
        msg.attach(MimeText(body, 'plain', 'utf-8'))
        
        with smtplib.SMTP(email_config['smtp_server'], email_config['smtp_port']) as server:
            server.starttls()
            server.login(email_config['username'], email_config['password'])
            server.send_message(msg)
    
    def _webhook_alert(self, alert_record: Dict[str, Any]):
        """发送Webhook告警"""
        if not self.config['webhook']['enabled']:
            return
        
        try:
            import requests
            
            payload = {
                'alert_type': alert_record['alert_type'],
                'level': alert_record['level'],
                'message': alert_record['message'],
                'timestamp': alert_record['timestamp'],
                'source': alert_record['source'],
                'details': json.loads(alert_record['details']) if alert_record['details'] else None
            }
            
            response = requests.post(
                self.config['webhook']['url'],
                json=payload,
                timeout=self.config['webhook']['timeout']
            )
            
            if response.status_code != 200:
                self.logger.warning(f"Webhook响应异常: {response.status_code}")
                
        except Exception as e:
            self.logger.error(f"发送Webhook告警失败: {e}")
    
    def get_recent_alerts(self, hours: int = 24, level: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取最近的告警记录"""
        try:
            since_time = datetime.now() - timedelta(hours=hours)
            
            with sqlite3.connect(self.alerts_db) as conn:
                cursor = conn.cursor()
                
                if level:
                    cursor.execute('''
                        SELECT * FROM alerts 
                        WHERE timestamp > ? AND level = ?
                        ORDER BY timestamp DESC
                    ''', (since_time.isoformat(), level))
                else:
                    cursor.execute('''
                        SELECT * FROM alerts 
                        WHERE timestamp > ?
                        ORDER BY timestamp DESC
                    ''', (since_time.isoformat(),))
                
                columns = [desc[0] for desc in cursor.description]
                alerts = []
                
                for row in cursor.fetchall():
                    alert = dict(zip(columns, row))
                    if alert['details']:
                        try:
                            alert['details'] = json.loads(alert['details'])
                        except:
                            pass
                    alerts.append(alert)
                
                return alerts
                
        except Exception as e:
            self.logger.error(f"获取告警记录失败: {e}")
            return []
    
    def resolve_alert(self, alert_id: int) -> bool:
        """标记告警为已解决"""
        try:
            with sqlite3.connect(self.alerts_db) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE alerts 
                    SET resolved = TRUE, resolved_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (alert_id,))
                
                conn.commit()
                return cursor.rowcount > 0
                
        except Exception as e:
            self.logger.error(f"标记告警解决失败: {e}")
            return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="告警系统管理工具")
    parser.add_argument('--action', choices=['test', 'list', 'resolve'], 
                       default='test', help='执行的操作')
    parser.add_argument('--level', choices=['INFO', 'WARNING', 'ERROR', 'CRITICAL'],
                       default='INFO', help='告警级别')
    parser.add_argument('--hours', type=int, default=24, help='查询时间范围(小时)')
    parser.add_argument('--alert-id', type=int, help='要解决的告警ID')
    
    args = parser.parse_args()
    
    alert_system = AlertSystem()
    
    if args.action == 'test':
        print(f"发送测试告警 (级别: {args.level})")
        success = alert_system.send_alert(
            alert_type='test_alert',
            level=args.level,
            message=f'这是一个 {args.level} 级别的测试告警',
            details={'test': True, 'timestamp': datetime.now().isoformat()},
            source='manual_test'
        )
        print(f"告警发送{'成功' if success else '失败'}")
    
    elif args.action == 'list':
        alerts = alert_system.get_recent_alerts(args.hours, args.level)
        print(f"最近 {args.hours} 小时的告警记录:")
        print(f"总计: {len(alerts)} 条")
        
        for alert in alerts[:10]:  # 只显示前10条
            resolved = "✅" if alert['resolved'] else "❌"
            print(f"  [{alert['id']}] {alert['timestamp']} - {alert['level']} - {alert['message']} {resolved}")
    
    elif args.action == 'resolve':
        if not args.alert_id:
            print("错误: 需要指定告警ID (--alert-id)")
            return
        
        success = alert_system.resolve_alert(args.alert_id)
        print(f"告警 {args.alert_id} {'已解决' if success else '解决失败'}")


if __name__ == "__main__":
    main()
