#!/usr/bin/env python3
"""
P9性能分析器

该模块扩展P8性能监控功能，提供高级性能分析，包括：
1. 性能趋势分析和预测
2. 性能下降检测和告警
3. 优化建议生成
4. 性能基线管理

作者: Augment Code AI Assistant
创建日期: 2025-01-14
版本: 1.0.0
"""

import numpy as np
import pandas as pd
import sqlite3
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import statistics
from collections import defaultdict

class PerformanceStatus(Enum):
    """性能状态枚举"""
    EXCELLENT = "excellent"
    GOOD = "good"
    NORMAL = "normal"
    WARNING = "warning"
    CRITICAL = "critical"

class TrendDirection(Enum):
    """趋势方向枚举"""
    IMPROVING = "improving"
    STABLE = "stable"
    DECLINING = "declining"
    VOLATILE = "volatile"

@dataclass
class PerformanceMetric:
    """性能指标数据类"""
    component_name: str
    metric_name: str
    current_value: float
    baseline_value: Optional[float] = None
    threshold_value: Optional[float] = None
    trend_direction: Optional[TrendDirection] = None
    status: PerformanceStatus = PerformanceStatus.NORMAL
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

class PerformanceAnalyzer:
    """性能分析器 - P9扩展组件"""
    
    def __init__(self, db_path: str, config: Optional[Dict[str, Any]] = None):
        """
        初始化性能分析器
        
        Args:
            db_path: 数据库路径
            config: 配置参数
        """
        self.db_path = db_path
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 性能分析配置
        self.analysis_config = {
            'trend_window_hours': self.config.get('trend_window_hours', 24),
            'baseline_window_days': self.config.get('baseline_window_days', 30),
            'volatility_threshold': self.config.get('volatility_threshold', 0.2),
            'improvement_threshold': self.config.get('improvement_threshold', 0.05),
            'degradation_threshold': self.config.get('degradation_threshold', 0.1)
        }
        
        # 性能权重配置
        self.metric_weights = {
            'accuracy': 0.3,
            'hit_rate': 0.25,
            'top10_hit_rate': 0.2,
            'mae': 0.15,
            'response_time': 0.1
        }
        
        # 性能阈值配置
        self.performance_thresholds = {
            'hundreds_accuracy': {'warning': 0.25, 'critical': 0.20},
            'tens_accuracy': {'warning': 0.25, 'critical': 0.20},
            'units_accuracy': {'warning': 0.25, 'critical': 0.20},
            'fusion_hit_rate': {'warning': 0.12, 'critical': 0.08},
            'fusion_top10_hit_rate': {'warning': 0.50, 'critical': 0.40},
            'sum_mae': {'warning': 2.5, 'critical': 3.0},
            'span_mae': {'warning': 2.0, 'critical': 2.5}
        }
        
        # 确保性能监控表存在
        self._ensure_performance_tables()
        
        self.logger.info("性能分析器初始化完成")
    
    def _ensure_performance_tables(self):
        """确保性能监控表存在"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 检查system_performance_monitor表是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='system_performance_monitor'")
            if not cursor.fetchone():
                self.logger.warning("system_performance_monitor表不存在，将创建基本表结构")
                
                # 创建性能监控表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS system_performance_monitor (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        monitor_time TIMESTAMP NOT NULL,
                        component_name TEXT NOT NULL,
                        performance_metric TEXT NOT NULL,
                        current_value REAL NOT NULL,
                        baseline_value REAL,
                        threshold_value REAL,
                        trend_direction TEXT,
                        status TEXT NOT NULL,
                        alert_sent BOOLEAN DEFAULT FALSE,
                        alert_level TEXT,
                        notes TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # 创建性能基线表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS performance_baselines (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        component_name TEXT NOT NULL,
                        metric_name TEXT NOT NULL,
                        baseline_value REAL NOT NULL,
                        baseline_type TEXT NOT NULL,
                        calculation_period TEXT,
                        sample_size INTEGER,
                        confidence_level REAL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        expires_at TIMESTAMP,
                        is_active BOOLEAN DEFAULT TRUE
                    )
                """)
                
                conn.commit()
                self.logger.info("性能监控表创建成功")
            
            conn.close()
            
        except Exception as e:
            self.logger.error(f"确保性能监控表失败: {e}")
    
    def analyze_performance_trends(self, component: Optional[str] = None, 
                                 hours_back: int = 24) -> Dict[str, Any]:
        """
        分析性能趋势
        
        Args:
            component: 组件名称，None表示分析所有组件
            hours_back: 分析时间范围（小时）
            
        Returns:
            Dict: 趋势分析结果
        """
        try:
            conn = sqlite3.connect(self.db_path)
            
            # 构建查询条件
            where_clause = "WHERE monitor_time > datetime('now', '-{} hours')".format(hours_back)
            if component:
                where_clause += f" AND component_name = '{component}'"
            
            # 查询性能数据
            query = f"""
                SELECT component_name, performance_metric, current_value, monitor_time
                FROM system_performance_monitor
                {where_clause}
                ORDER BY component_name, performance_metric, monitor_time
            """
            
            df = pd.read_sql_query(query, conn)
            conn.close()
            
            if df.empty:
                return {'message': '暂无性能数据', 'trends': {}}
            
            # 按组件和指标分组分析趋势
            trends = {}
            overall_scores = []
            
            for (comp, metric), group in df.groupby(['component_name', 'performance_metric']):
                trend_analysis = self._analyze_metric_trend(group)
                
                if comp not in trends:
                    trends[comp] = {}
                trends[comp][metric] = trend_analysis
                
                # 收集整体评分
                if 'score' in trend_analysis:
                    overall_scores.append(trend_analysis['score'])
            
            # 计算整体性能评分
            overall_score = {
                'score': np.mean(overall_scores) if overall_scores else 0.5,
                'component_count': len(trends),
                'metric_count': sum(len(metrics) for metrics in trends.values())
            }
            
            return {
                'trends': trends,
                'overall_score': overall_score,
                'analysis_period_hours': hours_back,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"分析性能趋势失败: {e}")
            return {'error': str(e)}
    
    def _analyze_metric_trend(self, data: pd.DataFrame) -> Dict[str, Any]:
        """分析单个指标的趋势"""
        try:
            if len(data) < 2:
                return {'trend': TrendDirection.STABLE.value, 'confidence': 0.0, 'score': 0.5}
            
            values = data['current_value'].values
            times = pd.to_datetime(data['monitor_time'])
            
            # 计算趋势
            if len(values) >= 3:
                # 使用线性回归计算趋势
                x = np.arange(len(values))
                slope = np.polyfit(x, values, 1)[0]
                
                # 计算变异系数
                cv = np.std(values) / np.mean(values) if np.mean(values) != 0 else 0
                
                # 确定趋势方向
                if cv > self.analysis_config['volatility_threshold']:
                    trend = TrendDirection.VOLATILE
                elif slope > self.analysis_config['improvement_threshold']:
                    trend = TrendDirection.IMPROVING
                elif slope < -self.analysis_config['degradation_threshold']:
                    trend = TrendDirection.DECLINING
                else:
                    trend = TrendDirection.STABLE
                
                # 计算置信度
                confidence = min(1.0, len(values) / 10)  # 样本越多置信度越高
                
                # 计算性能评分
                current_value = values[-1]
                baseline_value = np.mean(values)
                
                if baseline_value != 0:
                    improvement_ratio = (current_value - baseline_value) / baseline_value
                    score = max(0.0, min(1.0, 0.5 + improvement_ratio))
                else:
                    score = 0.5
                
            else:
                trend = TrendDirection.STABLE
                confidence = 0.5
                score = 0.5
            
            return {
                'trend': trend.value,
                'confidence': confidence,
                'score': score,
                'current_value': float(values[-1]),
                'average_value': float(np.mean(values)),
                'min_value': float(np.min(values)),
                'max_value': float(np.max(values)),
                'sample_count': len(values),
                'volatility': float(cv) if len(values) >= 3 else 0.0
            }
            
        except Exception as e:
            self.logger.error(f"分析指标趋势失败: {e}")
            return {'trend': TrendDirection.STABLE.value, 'confidence': 0.0, 'score': 0.5, 'error': str(e)}
    
    def detect_performance_degradation(self, component: Optional[str] = None) -> Dict[str, Any]:
        """
        检测性能下降
        
        Args:
            component: 组件名称，None表示检测所有组件
            
        Returns:
            Dict: 性能下降检测结果
        """
        try:
            degradations = []
            warnings = []
            
            # 获取最新性能数据
            conn = sqlite3.connect(self.db_path)
            
            where_clause = "WHERE monitor_time > datetime('now', '-1 hour')"
            if component:
                where_clause += f" AND component_name = '{component}'"
            
            query = f"""
                SELECT component_name, performance_metric, current_value, 
                       baseline_value, threshold_value, status
                FROM system_performance_monitor
                {where_clause}
                ORDER BY monitor_time DESC
            """
            
            df = pd.read_sql_query(query, conn)
            conn.close()
            
            if df.empty:
                return {'degradations_detected': 0, 'degradations': [], 'warnings': []}
            
            # 检测每个指标的性能状况
            for _, row in df.iterrows():
                component_name = row['component_name']
                metric_name = row['performance_metric']
                current_value = row['current_value']
                baseline_value = row['baseline_value']
                threshold_value = row['threshold_value']
                status = row['status']
                
                # 检查是否低于阈值
                metric_key = f"{component_name}_{metric_name}"
                thresholds = self.performance_thresholds.get(metric_key, {})
                
                degradation_info = {
                    'component': component_name,
                    'metric': metric_name,
                    'current_value': current_value,
                    'baseline_value': baseline_value,
                    'threshold_value': threshold_value,
                    'status': status
                }
                
                if thresholds:
                    if current_value < thresholds.get('critical', 0):
                        degradation_info['severity'] = 'critical'
                        degradation_info['message'] = f"{metric_name}严重下降: {current_value:.3f} < {thresholds['critical']}"
                        degradations.append(degradation_info)
                    elif current_value < thresholds.get('warning', 0):
                        degradation_info['severity'] = 'warning'
                        degradation_info['message'] = f"{metric_name}轻微下降: {current_value:.3f} < {thresholds['warning']}"
                        warnings.append(degradation_info)
                
                # 检查与基线的偏差
                elif baseline_value and current_value < baseline_value * 0.9:  # 低于基线10%
                    degradation_info['severity'] = 'warning'
                    degradation_info['message'] = f"{metric_name}低于基线: {current_value:.3f} < {baseline_value:.3f}"
                    warnings.append(degradation_info)
            
            # 生成优化建议
            recommendations = self._generate_optimization_recommendations(degradations + warnings)
            
            return {
                'degradations_detected': len(degradations),
                'warnings_detected': len(warnings),
                'degradations': degradations,
                'warnings': warnings,
                'recommendations': recommendations,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"检测性能下降失败: {e}")
            return {'error': str(e)}
    
    def _generate_optimization_recommendations(self, issues: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """生成优化建议"""
        recommendations = []
        
        try:
            # 按组件分组问题
            component_issues = defaultdict(list)
            for issue in issues:
                component_issues[issue['component']].append(issue)
            
            for component, comp_issues in component_issues.items():
                # 分析问题类型
                critical_count = sum(1 for issue in comp_issues if issue.get('severity') == 'critical')
                warning_count = len(comp_issues) - critical_count
                
                if critical_count > 0:
                    recommendations.append({
                        'component': component,
                        'priority': 'high',
                        'action': 'immediate_optimization',
                        'description': f"{component}存在{critical_count}个严重性能问题，需要立即优化",
                        'suggested_actions': [
                            '检查模型参数配置',
                            '重新训练模型',
                            '调整融合权重',
                            '更新训练数据'
                        ]
                    })
                elif warning_count > 2:
                    recommendations.append({
                        'component': component,
                        'priority': 'medium',
                        'action': 'scheduled_optimization',
                        'description': f"{component}存在{warning_count}个性能警告，建议安排优化",
                        'suggested_actions': [
                            '微调模型参数',
                            '增加训练数据',
                            '优化特征工程'
                        ]
                    })
            
            # 全局建议
            if len(issues) > 5:
                recommendations.append({
                    'component': 'system',
                    'priority': 'high',
                    'action': 'comprehensive_review',
                    'description': '系统整体性能下降，建议进行全面检查',
                    'suggested_actions': [
                        '检查数据质量',
                        '验证系统配置',
                        '分析外部环境变化',
                        '考虑系统重构'
                    ]
                })
            
            return recommendations

        except Exception as e:
            self.logger.error(f"生成优化建议失败: {e}")
            return []

    def calculate_improvement_score(self, before_metrics: Dict[str, float],
                                  after_metrics: Dict[str, float]) -> Dict[str, Any]:
        """
        计算优化改进分数

        Args:
            before_metrics: 优化前的性能指标
            after_metrics: 优化后的性能指标

        Returns:
            Dict: 改进分数和详细分析
        """
        try:
            improvements = {}
            weighted_improvements = []

            for metric, before_value in before_metrics.items():
                if metric in after_metrics:
                    after_value = after_metrics[metric]

                    # 计算改进率
                    if before_value != 0:
                        improvement_rate = (after_value - before_value) / before_value
                    else:
                        improvement_rate = 1.0 if after_value > 0 else 0.0

                    # 对于误差类指标（MAE等），改进率需要反转
                    if 'mae' in metric.lower() or 'error' in metric.lower():
                        improvement_rate = -improvement_rate

                    improvements[metric] = {
                        'before': before_value,
                        'after': after_value,
                        'improvement_rate': improvement_rate,
                        'absolute_change': after_value - before_value
                    }

                    # 计算加权改进
                    weight = self.metric_weights.get(metric, 0.1)
                    weighted_improvements.append(improvement_rate * weight)

            # 计算总体改进分数
            if weighted_improvements:
                overall_improvement = sum(weighted_improvements) / sum(
                    self.metric_weights.get(metric, 0.1) for metric in before_metrics.keys()
                    if metric in after_metrics
                )
            else:
                overall_improvement = 0.0

            # 确定改进等级
            if overall_improvement >= 0.1:
                improvement_level = 'significant'
            elif overall_improvement >= 0.05:
                improvement_level = 'moderate'
            elif overall_improvement >= 0.01:
                improvement_level = 'minor'
            elif overall_improvement >= -0.01:
                improvement_level = 'negligible'
            else:
                improvement_level = 'degradation'

            return {
                'improvement_score': overall_improvement,
                'improvement_level': improvement_level,
                'metric_improvements': improvements,
                'summary': {
                    'improved_metrics': sum(1 for imp in improvements.values() if imp['improvement_rate'] > 0),
                    'degraded_metrics': sum(1 for imp in improvements.values() if imp['improvement_rate'] < 0),
                    'total_metrics': len(improvements)
                },
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"计算改进分数失败: {e}")
            return {'error': str(e)}

    def record_performance_metric(self, metric: PerformanceMetric):
        """
        记录性能指标

        Args:
            metric: 性能指标对象
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                INSERT INTO system_performance_monitor
                (monitor_time, component_name, performance_metric, current_value,
                 baseline_value, threshold_value, trend_direction, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                metric.timestamp.isoformat(),
                metric.component_name,
                metric.metric_name,
                metric.current_value,
                metric.baseline_value,
                metric.threshold_value,
                metric.trend_direction.value if metric.trend_direction else None,
                metric.status.value
            ))

            conn.commit()
            conn.close()

            self.logger.debug(f"性能指标已记录: {metric.component_name}.{metric.metric_name} = {metric.current_value}")

        except Exception as e:
            self.logger.error(f"记录性能指标失败: {e}")

    def update_performance_baseline(self, component_name: str, metric_name: str,
                                  baseline_value: float, baseline_type: str = "monthly"):
        """
        更新性能基线

        Args:
            component_name: 组件名称
            metric_name: 指标名称
            baseline_value: 基线值
            baseline_type: 基线类型
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 检查是否已存在基线
            cursor.execute("""
                SELECT id FROM performance_baselines
                WHERE component_name = ? AND metric_name = ? AND is_active = TRUE
            """, (component_name, metric_name))

            existing = cursor.fetchone()

            if existing:
                # 更新现有基线
                cursor.execute("""
                    UPDATE performance_baselines
                    SET baseline_value = ?, baseline_type = ?, created_at = ?
                    WHERE id = ?
                """, (baseline_value, baseline_type, datetime.now().isoformat(), existing[0]))
            else:
                # 创建新基线
                cursor.execute("""
                    INSERT INTO performance_baselines
                    (component_name, metric_name, baseline_value, baseline_type, is_active)
                    VALUES (?, ?, ?, ?, TRUE)
                """, (component_name, metric_name, baseline_value, baseline_type))

            conn.commit()
            conn.close()

            self.logger.info(f"性能基线已更新: {component_name}.{metric_name} = {baseline_value}")

        except Exception as e:
            self.logger.error(f"更新性能基线失败: {e}")

    def get_performance_summary(self, hours_back: int = 24) -> Dict[str, Any]:
        """
        获取性能摘要

        Args:
            hours_back: 统计时间范围（小时）

        Returns:
            Dict: 性能摘要
        """
        try:
            # 获取趋势分析
            trends = self.analyze_performance_trends(hours_back=hours_back)

            # 获取性能下降检测
            degradations = self.detect_performance_degradation()

            # 统计信息
            conn = sqlite3.connect(self.db_path)

            query = """
                SELECT
                    COUNT(*) as total_metrics,
                    COUNT(DISTINCT component_name) as components,
                    AVG(current_value) as avg_performance,
                    MIN(current_value) as min_performance,
                    MAX(current_value) as max_performance
                FROM system_performance_monitor
                WHERE monitor_time > datetime('now', '-{} hours')
            """.format(hours_back)

            stats = pd.read_sql_query(query, conn)
            conn.close()

            # 组合摘要
            summary = {
                'summary': trends.get('overall_score', {}),
                'trends': trends,
                'degradations': degradations,
                'statistics': stats.iloc[0].to_dict() if not stats.empty else {},
                'analysis_period_hours': hours_back,
                'timestamp': datetime.now().isoformat()
            }

            return summary

        except Exception as e:
            self.logger.error(f"获取性能摘要失败: {e}")
            return {'error': str(e)}

    def cleanup_old_performance_data(self, days_back: int = 30):
        """
        清理旧的性能数据

        Args:
            days_back: 保留多少天的数据
        """
        try:
            cutoff_date = datetime.now() - timedelta(days=days_back)

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 清理旧的性能监控数据
            cursor.execute("""
                DELETE FROM system_performance_monitor
                WHERE monitor_time < ?
            """, (cutoff_date.isoformat(),))

            deleted_monitor = cursor.rowcount

            # 清理过期的性能基线
            cursor.execute("""
                DELETE FROM performance_baselines
                WHERE expires_at IS NOT NULL AND expires_at < ?
            """, (datetime.now().isoformat(),))

            deleted_baselines = cursor.rowcount

            conn.commit()
            conn.close()

            self.logger.info(f"清理了 {deleted_monitor} 条性能监控数据和 {deleted_baselines} 条过期基线")

        except Exception as e:
            self.logger.error(f"清理旧性能数据失败: {e}")
