"""
P2高级特征工程系统性能验证测试
验证系统是否达到预期的性能指标
"""

import sys
import os
import time
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.data.advanced_feature_engineer import AdvancedFeatureEngineer

def test_p2_performance():
    """测试P2系统性能指标"""
    print("=== P2性能指标验证 ===")
    
    try:
        # 初始化系统
        engineer = AdvancedFeatureEngineer("data/lottery.db")
        
        # 1. 测试高级特征计算时间
        print("📊 测试高级特征计算性能...")
        start_time = time.time()
        features = engineer.get_features_with_cache("2025205", "hundreds")
        end_time = time.time()
        calc_time = (end_time - start_time) * 1000
        
        target_time = 10  # 目标：<10毫秒
        status = "✅" if calc_time < target_time else "⚠️"
        print(f"{status} 高级特征计算时间: {calc_time:.2f}毫秒 (目标: <{target_time}毫秒)")
        
        # 2. 测试缓存命中率
        print("📊 测试缓存系统性能...")
        # 多次访问同一特征以测试缓存
        for i in range(5):
            engineer.get_features_with_cache("2025205", "hundreds")
        
        cache_stats = engineer.get_cache_stats()
        hit_rate = cache_stats["hit_rate"]
        target_hit_rate = 0.8  # 目标：>80%
        status = "✅" if hit_rate > target_hit_rate else "⚠️"
        print(f"{status} 缓存命中率: {hit_rate:.1%} (目标: >{target_hit_rate:.0%})")
        
        # 3. 测试特征数量
        print("📊 测试特征完整性...")
        feature_count = len(features) if features else 0
        target_features = 40  # 目标：40+维特征
        status = "✅" if feature_count >= target_features else "⚠️"
        print(f"{status} 特征数量: {feature_count}个 (目标: {target_features}+维)")
        
        # 4. 测试所有特征类型
        print("📊 测试特征类型完整性...")
        feature_types = engineer.get_available_feature_types()
        expected_types = ["hundreds", "tens", "units", "sum", "span", "common"]
        
        all_types_available = all(ft in feature_types for ft in expected_types)
        status = "✅" if all_types_available else "❌"
        print(f"{status} 特征类型: {len(feature_types)}种 (期望: {len(expected_types)}种)")
        print(f"   可用类型: {list(feature_types.keys())}")
        
        # 5. 测试ML数据准备性能
        print("📊 测试ML数据准备性能...")
        start_time = time.time()
        ml_data = engineer.prepare_ml_training_data(["hundreds", "tens"], limit=100)
        end_time = time.time()
        ml_time = (end_time - start_time) * 1000
        
        target_ml_time = 1000  # 目标：<1秒
        status = "✅" if ml_time < target_ml_time else "⚠️"
        print(f"{status} ML数据准备时间: {ml_time:.0f}毫秒 (目标: <{target_ml_time}毫秒)")
        print(f"   ML数据形状: {ml_data.shape if not ml_data.empty else 'Empty'}")
        
        # 6. 系统整体状态验证
        print("📊 测试系统整体状态...")
        validation = engineer.validate_system()
        overall_status = validation.get("overall_status", "unknown")
        status = "✅" if overall_status == "ready" else "❌"
        print(f"{status} 系统整体状态: {overall_status}")
        
        # 总结
        print("\n🎯 P2性能指标验证完成")
        print("=" * 50)
        
        # 评估总体性能
        performance_score = 0
        if calc_time < target_time:
            performance_score += 1
        if hit_rate > target_hit_rate:
            performance_score += 1
        if feature_count >= target_features:
            performance_score += 1
        if all_types_available:
            performance_score += 1
        if ml_time < target_ml_time:
            performance_score += 1
        if overall_status == "ready":
            performance_score += 1
        
        total_tests = 6
        success_rate = performance_score / total_tests
        
        if success_rate >= 0.8:
            print(f"🎉 性能验证通过: {performance_score}/{total_tests} ({success_rate:.1%})")
            return True
        else:
            print(f"⚠️ 性能验证部分通过: {performance_score}/{total_tests} ({success_rate:.1%})")
            return False
            
    except Exception as e:
        print(f"❌ 性能测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_p2_performance()
    sys.exit(0 if success else 1)
