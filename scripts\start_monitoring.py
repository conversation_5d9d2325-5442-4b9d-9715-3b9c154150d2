#!/usr/bin/env python3
"""
启动监控服务脚本

简化的监控启动脚本，用于验证监控功能
"""

import os
import sys
import time
import json
import logging
from datetime import datetime
from pathlib import Path

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def setup_logging():
    """设置日志"""
    logs_dir = Path(__file__).parent.parent / "logs"
    logs_dir.mkdir(exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(logs_dir / 'monitoring.log')
        ]
    )
    return logging.getLogger(__name__)

def check_log_files():
    """检查日志文件状态"""
    logs_dir = Path(__file__).parent.parent / "logs"
    log_files = list(logs_dir.glob("*.log"))
    
    print(f"📁 日志目录: {logs_dir}")
    print(f"📄 日志文件数量: {len(log_files)}")
    
    for log_file in log_files:
        size_mb = log_file.stat().st_size / (1024 * 1024)
        print(f"  - {log_file.name}: {size_mb:.2f} MB")
    
    return len(log_files)

def test_log_rotation():
    """测试日志轮转功能"""
    print("\n🔄 测试日志轮转功能...")
    
    # 创建测试日志文件
    logs_dir = Path(__file__).parent.parent / "logs"
    test_log = logs_dir / "test_rotation.log"
    
    # 写入测试数据
    with open(test_log, 'w') as f:
        for i in range(1000):
            f.write(f"测试日志行 {i}: {datetime.now()}\n")
    
    size_mb = test_log.stat().st_size / (1024 * 1024)
    print(f"✅ 创建测试日志文件: {test_log.name} ({size_mb:.2f} MB)")
    
    return True

def test_alert_system():
    """测试告警系统"""
    print("\n🚨 测试告警系统...")
    
    try:
        # 创建告警数据库
        data_dir = Path(__file__).parent.parent / "data"
        data_dir.mkdir(exist_ok=True)
        
        alerts_db = data_dir / "alerts.db"
        
        import sqlite3
        with sqlite3.connect(alerts_db) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS alerts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    alert_type TEXT NOT NULL,
                    level TEXT NOT NULL,
                    message TEXT NOT NULL,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 插入测试告警
            cursor.execute('''
                INSERT INTO alerts (alert_type, level, message)
                VALUES (?, ?, ?)
            ''', ('test_alert', 'INFO', '监控系统测试告警'))
            
            conn.commit()
        
        print(f"✅ 告警数据库创建成功: {alerts_db}")
        return True
        
    except Exception as e:
        print(f"❌ 告警系统测试失败: {e}")
        return False

def create_monitoring_config():
    """创建监控配置文件"""
    print("\n⚙️ 创建监控配置...")
    
    config_dir = Path(__file__).parent.parent / "config"
    config_file = config_dir / "monitoring_config.json"
    
    config = {
        "monitoring": {
            "enabled": True,
            "check_interval": 60,
            "log_rotation": {
                "enabled": True,
                "max_size_mb": 20,
                "backup_count": 10
            },
            "alerts": {
                "enabled": True,
                "levels": ["WARNING", "ERROR", "CRITICAL"]
            }
        },
        "performance": {
            "cpu_threshold": 80,
            "memory_threshold": 85,
            "disk_threshold": 90
        }
    }
    
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 监控配置文件创建: {config_file}")
    return True

def verify_monitoring_setup():
    """验证监控设置"""
    print("\n🔍 验证监控设置...")
    
    checks = []
    
    # 检查日志目录
    logs_dir = Path(__file__).parent.parent / "logs"
    checks.append(("日志目录", logs_dir.exists()))
    
    # 检查配置文件
    config_files = [
        "config/system_logging_config.yaml",
        "config/monitoring_config.json"
    ]
    
    for config_file in config_files:
        file_path = Path(__file__).parent.parent / config_file
        checks.append((f"配置文件 {config_file}", file_path.exists()))
    
    # 检查脚本文件
    script_files = [
        "scripts/system_monitor.py",
        "scripts/log_rotation.py",
        "scripts/alert_system.py"
    ]
    
    for script_file in script_files:
        file_path = Path(__file__).parent.parent / script_file
        checks.append((f"脚本文件 {script_file}", file_path.exists()))
    
    # 输出检查结果
    all_passed = True
    for check_name, passed in checks:
        status = "✅" if passed else "❌"
        print(f"  {status} {check_name}")
        if not passed:
            all_passed = False
    
    return all_passed

def main():
    """主函数"""
    print("🚀 启动福彩3D系统监控设置...")
    print("=" * 50)
    
    # 设置日志
    logger = setup_logging()
    logger.info("开始监控系统设置")
    
    try:
        # 1. 检查日志文件
        log_count = check_log_files()
        
        # 2. 创建监控配置
        create_monitoring_config()
        
        # 3. 测试日志轮转
        test_log_rotation()
        
        # 4. 测试告警系统
        test_alert_system()
        
        # 5. 验证监控设置
        setup_ok = verify_monitoring_setup()
        
        print("\n" + "=" * 50)
        if setup_ok:
            print("✅ 监控和日志设置完成！")
            print("\n📋 监控功能状态:")
            print("  ✅ 日志轮转: 已配置")
            print("  ✅ 性能监控: 已配置")
            print("  ✅ 告警系统: 已配置")
            print("  ✅ 配置文件: 已创建")
            
            print("\n🎯 下一步操作:")
            print("  1. 运行 'python scripts/log_rotation.py --action status' 检查日志状态")
            print("  2. 运行 'python scripts/alert_system.py --action test' 测试告警")
            print("  3. 监控系统已准备就绪，可以进入下一阶段")
            
            logger.info("监控和日志设置成功完成")
            return True
        else:
            print("❌ 监控设置存在问题，请检查错误信息")
            logger.error("监控设置失败")
            return False
            
    except Exception as e:
        print(f"❌ 监控设置过程中发生错误: {e}")
        logger.error(f"监控设置错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
