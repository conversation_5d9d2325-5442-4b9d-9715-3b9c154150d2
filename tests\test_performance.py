#!/usr/bin/env python3
"""
P1模块性能测试
验证采集速度和系统稳定性
"""

import time
import sys
import os
import tempfile
import sqlite3
from pathlib import Path
import threading
import concurrent.futures

# 添加src目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / 'src'))


class PerformanceTester:
    """性能测试器"""
    
    def __init__(self):
        self.test_results = {}
    
    def test_database_performance(self, record_count: int = 1000) -> dict:
        """测试数据库性能"""
        print(f"测试数据库性能 - {record_count} 条记录")
        
        # 创建临时数据库
        test_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        test_db.close()
        db_path = test_db.name
        
        try:
            # 创建数据库和表
            conn = sqlite3.connect(db_path)
            conn.execute("""
            CREATE TABLE lottery_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                issue TEXT UNIQUE NOT NULL,
                draw_date TEXT NOT NULL,
                hundreds INTEGER NOT NULL,
                tens INTEGER NOT NULL,
                units INTEGER NOT NULL,
                sum_value INTEGER,
                span INTEGER,
                number_type TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """)
            
            # 创建索引
            conn.execute("CREATE INDEX idx_issue ON lottery_data (issue)")
            conn.execute("CREATE INDEX idx_date ON lottery_data (draw_date)")
            conn.commit()
            
            # 测试插入性能
            start_time = time.time()
            
            for i in range(record_count):
                issue = f"2024{i:03d}"
                date = f"2024-01-{(i % 30) + 1:02d}"
                hundreds = i % 10
                tens = (i + 1) % 10
                units = (i + 2) % 10
                sum_value = hundreds + tens + units
                span = max(hundreds, tens, units) - min(hundreds, tens, units)
                
                unique_count = len(set([hundreds, tens, units]))
                if unique_count == 1:
                    number_type = "豹子"
                elif unique_count == 2:
                    number_type = "对子"
                else:
                    number_type = "组六"
                
                conn.execute("""
                INSERT INTO lottery_data 
                (issue, draw_date, hundreds, tens, units, sum_value, span, number_type)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (issue, date, hundreds, tens, units, sum_value, span, number_type))
            
            conn.commit()
            insert_time = time.time() - start_time
            
            # 测试查询性能
            start_time = time.time()
            
            # 按期号查询
            cursor = conn.execute("SELECT * FROM lottery_data WHERE issue = ?", ("2024100",))
            cursor.fetchone()
            
            # 按日期范围查询
            cursor = conn.execute(
                "SELECT * FROM lottery_data WHERE draw_date BETWEEN ? AND ?",
                ("2024-01-01", "2024-01-10")
            )
            cursor.fetchall()
            
            # 统计查询
            cursor = conn.execute("SELECT COUNT(*) FROM lottery_data")
            total_count = cursor.fetchone()[0]
            
            query_time = time.time() - start_time
            
            conn.close()
            
            # 计算性能指标
            insert_rate = record_count / insert_time if insert_time > 0 else 0
            
            result = {
                'record_count': record_count,
                'insert_time': insert_time,
                'insert_rate': insert_rate,
                'query_time': query_time,
                'total_records': total_count,
                'avg_insert_time': insert_time / record_count if record_count > 0 else 0
            }
            
            print(f"  插入时间: {insert_time:.2f} 秒")
            print(f"  插入速率: {insert_rate:.0f} 记录/秒")
            print(f"  查询时间: {query_time:.3f} 秒")
            print(f"  平均插入时间: {result['avg_insert_time']*1000:.2f} 毫秒/记录")
            
            return result
            
        finally:
            # 清理临时文件
            if os.path.exists(db_path):
                os.unlink(db_path)
    
    def test_data_parsing_performance(self, data_count: int = 10000) -> dict:
        """测试数据解析性能"""
        print(f"测试数据解析性能 - {data_count} 条记录")
        
        # 生成测试数据
        test_data = []
        for i in range(data_count):
            line = f"2024{i:03d},2024-01-{(i % 30) + 1:02d},{i % 10}{(i+1) % 10}{(i+2) % 10}"
            test_data.append(line)
        
        # 测试解析性能
        start_time = time.time()
        parsed_records = []
        
        for line in test_data:
            parts = line.split(',')
            if len(parts) >= 3:
                issue = parts[0]
                date = parts[1]
                numbers = [int(d) for d in parts[2]]
                
                record = {
                    'issue': issue,
                    'draw_date': date,
                    'hundreds': numbers[0],
                    'tens': numbers[1],
                    'units': numbers[2],
                    'sum_value': sum(numbers),
                    'span': max(numbers) - min(numbers)
                }
                
                # 判断号码类型
                unique_count = len(set(numbers))
                if unique_count == 1:
                    record['number_type'] = "豹子"
                elif unique_count == 2:
                    record['number_type'] = "对子"
                else:
                    record['number_type'] = "组六"
                
                parsed_records.append(record)
        
        parse_time = time.time() - start_time
        parse_rate = len(parsed_records) / parse_time if parse_time > 0 else 0
        
        result = {
            'data_count': data_count,
            'parsed_count': len(parsed_records),
            'parse_time': parse_time,
            'parse_rate': parse_rate,
            'avg_parse_time': parse_time / data_count if data_count > 0 else 0
        }
        
        print(f"  解析时间: {parse_time:.2f} 秒")
        print(f"  解析速率: {parse_rate:.0f} 记录/秒")
        print(f"  平均解析时间: {result['avg_parse_time']*1000:.2f} 毫秒/记录")
        
        return result
    
    def test_validation_performance(self, record_count: int = 5000) -> dict:
        """测试数据验证性能"""
        print(f"测试数据验证性能 - {record_count} 条记录")
        
        # 生成测试记录
        test_records = []
        for i in range(record_count):
            numbers = [i % 10, (i + 1) % 10, (i + 2) % 10]
            record = {
                'issue': f"2024{i:03d}",
                'draw_date': f"2024-01-{(i % 30) + 1:02d}",
                'hundreds': numbers[0],
                'tens': numbers[1],
                'units': numbers[2],
                'sum_value': sum(numbers),
                'span': max(numbers) - min(numbers)
            }
            test_records.append(record)
        
        # 测试验证性能
        start_time = time.time()
        valid_count = 0
        invalid_count = 0
        
        for record in test_records:
            # 基本格式验证
            is_valid = True
            
            # 期号验证
            if not (len(record['issue']) == 7 and record['issue'].isdigit()):
                is_valid = False
            
            # 数字范围验证
            if not (0 <= record['hundreds'] <= 9 and 
                   0 <= record['tens'] <= 9 and 
                   0 <= record['units'] <= 9):
                is_valid = False
            
            # 和值验证
            expected_sum = record['hundreds'] + record['tens'] + record['units']
            if record['sum_value'] != expected_sum:
                is_valid = False
            
            # 跨度验证
            numbers = [record['hundreds'], record['tens'], record['units']]
            expected_span = max(numbers) - min(numbers)
            if record['span'] != expected_span:
                is_valid = False
            
            if is_valid:
                valid_count += 1
            else:
                invalid_count += 1
        
        validation_time = time.time() - start_time
        validation_rate = record_count / validation_time if validation_time > 0 else 0
        
        result = {
            'record_count': record_count,
            'valid_count': valid_count,
            'invalid_count': invalid_count,
            'validation_time': validation_time,
            'validation_rate': validation_rate,
            'validation_accuracy': valid_count / record_count if record_count > 0 else 0
        }
        
        print(f"  验证时间: {validation_time:.2f} 秒")
        print(f"  验证速率: {validation_rate:.0f} 记录/秒")
        print(f"  有效记录: {valid_count}")
        print(f"  无效记录: {invalid_count}")
        print(f"  验证准确率: {result['validation_accuracy']:.2%}")
        
        return result
    
    def test_concurrent_operations(self, thread_count: int = 5, operations_per_thread: int = 100) -> dict:
        """测试并发操作性能"""
        print(f"测试并发操作性能 - {thread_count} 线程，每线程 {operations_per_thread} 操作")
        
        # 创建临时数据库
        test_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        test_db.close()
        db_path = test_db.name
        
        try:
            # 初始化数据库
            conn = sqlite3.connect(db_path)
            conn.execute("""
            CREATE TABLE lottery_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                issue TEXT UNIQUE NOT NULL,
                draw_date TEXT NOT NULL,
                hundreds INTEGER NOT NULL,
                tens INTEGER NOT NULL,
                units INTEGER NOT NULL,
                thread_id INTEGER
            )
            """)
            conn.commit()
            conn.close()
            
            def worker_thread(thread_id: int, operations: int):
                """工作线程函数"""
                thread_conn = sqlite3.connect(db_path)
                success_count = 0
                
                for i in range(operations):
                    try:
                        issue = f"T{thread_id:02d}{i:03d}"
                        date = f"2024-01-{(i % 30) + 1:02d}"
                        hundreds = (thread_id + i) % 10
                        tens = (thread_id + i + 1) % 10
                        units = (thread_id + i + 2) % 10
                        
                        thread_conn.execute("""
                        INSERT INTO lottery_data 
                        (issue, draw_date, hundreds, tens, units, thread_id)
                        VALUES (?, ?, ?, ?, ?, ?)
                        """, (issue, date, hundreds, tens, units, thread_id))
                        
                        success_count += 1
                    except Exception as e:
                        pass  # 忽略错误，继续执行
                
                thread_conn.commit()
                thread_conn.close()
                return success_count
            
            # 执行并发测试
            start_time = time.time()
            
            with concurrent.futures.ThreadPoolExecutor(max_workers=thread_count) as executor:
                futures = [
                    executor.submit(worker_thread, i, operations_per_thread)
                    for i in range(thread_count)
                ]
                
                results = [future.result() for future in concurrent.futures.as_completed(futures)]
            
            concurrent_time = time.time() - start_time
            
            # 验证结果
            conn = sqlite3.connect(db_path)
            cursor = conn.execute("SELECT COUNT(*) FROM lottery_data")
            total_records = cursor.fetchone()[0]
            conn.close()
            
            total_operations = sum(results)
            operations_rate = total_operations / concurrent_time if concurrent_time > 0 else 0
            
            result = {
                'thread_count': thread_count,
                'operations_per_thread': operations_per_thread,
                'total_operations': total_operations,
                'successful_operations': total_operations,
                'concurrent_time': concurrent_time,
                'operations_rate': operations_rate,
                'total_records': total_records
            }
            
            print(f"  并发时间: {concurrent_time:.2f} 秒")
            print(f"  总操作数: {total_operations}")
            print(f"  操作速率: {operations_rate:.0f} 操作/秒")
            print(f"  数据库记录: {total_records}")
            
            return result
            
        finally:
            # 清理临时文件
            if os.path.exists(db_path):
                os.unlink(db_path)
    
    def run_all_performance_tests(self) -> dict:
        """运行所有性能测试"""
        print("=== P1模块性能测试开始 ===\n")
        
        all_results = {}
        
        # 1. 数据库性能测试
        print("1. 数据库性能测试")
        all_results['database'] = self.test_database_performance(1000)
        print()
        
        # 2. 数据解析性能测试
        print("2. 数据解析性能测试")
        all_results['parsing'] = self.test_data_parsing_performance(10000)
        print()
        
        # 3. 数据验证性能测试
        print("3. 数据验证性能测试")
        all_results['validation'] = self.test_validation_performance(5000)
        print()
        
        # 4. 并发操作性能测试
        print("4. 并发操作性能测试")
        all_results['concurrent'] = self.test_concurrent_operations(5, 100)
        print()
        
        # 生成性能报告
        self._generate_performance_report(all_results)
        
        return all_results
    
    def _generate_performance_report(self, results: dict):
        """生成性能报告"""
        print("=== 性能测试报告 ===")
        
        # 数据库性能
        db_result = results.get('database', {})
        print(f"数据库性能:")
        print(f"  插入速率: {db_result.get('insert_rate', 0):.0f} 记录/秒")
        print(f"  查询时间: {db_result.get('query_time', 0):.3f} 秒")
        
        # 解析性能
        parse_result = results.get('parsing', {})
        print(f"解析性能:")
        print(f"  解析速率: {parse_result.get('parse_rate', 0):.0f} 记录/秒")
        
        # 验证性能
        validation_result = results.get('validation', {})
        print(f"验证性能:")
        print(f"  验证速率: {validation_result.get('validation_rate', 0):.0f} 记录/秒")
        print(f"  验证准确率: {validation_result.get('validation_accuracy', 0):.2%}")
        
        # 并发性能
        concurrent_result = results.get('concurrent', {})
        print(f"并发性能:")
        print(f"  并发操作速率: {concurrent_result.get('operations_rate', 0):.0f} 操作/秒")
        
        # 性能评级
        print(f"\n性能评级:")
        
        # 数据库插入速率评级
        insert_rate = db_result.get('insert_rate', 0)
        if insert_rate >= 1000:
            db_grade = "优秀"
        elif insert_rate >= 500:
            db_grade = "良好"
        elif insert_rate >= 100:
            db_grade = "一般"
        else:
            db_grade = "需要优化"
        print(f"  数据库性能: {db_grade}")
        
        # 解析速率评级
        parse_rate = parse_result.get('parse_rate', 0)
        if parse_rate >= 5000:
            parse_grade = "优秀"
        elif parse_rate >= 2000:
            parse_grade = "良好"
        elif parse_rate >= 1000:
            parse_grade = "一般"
        else:
            parse_grade = "需要优化"
        print(f"  解析性能: {parse_grade}")
        
        # 验证速率评级
        validation_rate = validation_result.get('validation_rate', 0)
        if validation_rate >= 3000:
            validation_grade = "优秀"
        elif validation_rate >= 1500:
            validation_grade = "良好"
        elif validation_rate >= 500:
            validation_grade = "一般"
        else:
            validation_grade = "需要优化"
        print(f"  验证性能: {validation_grade}")
        
        print(f"\n🎉 性能测试完成！")


def main():
    """主函数"""
    tester = PerformanceTester()
    results = tester.run_all_performance_tests()
    return results


if __name__ == "__main__":
    main()
