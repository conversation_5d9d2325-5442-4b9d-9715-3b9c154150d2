#!/usr/bin/env python3
"""
测试P9系统错误日志优化效果
验证阶段4的日志实现
"""

import sys
import os
import asyncio
import time
from datetime import datetime
from pathlib import Path

# 添加项目路径
sys.path.append('.')

def test_structured_logger():
    """测试结构化日志记录器"""
    print("=" * 60)
    print("🔍 测试阶段4：结构化日志记录器")
    print("=" * 60)
    
    try:
        from src.utils.logger import StructuredLogger, get_api_logger
        
        # 创建测试日志记录器
        test_logger = StructuredLogger("TestLogger", log_dir="logs/test")
        print("✅ 结构化日志记录器创建成功")
        
        # 测试不同级别的日志
        test_logger.debug("这是调试信息", test_param="debug_value")
        test_logger.info("这是信息日志", test_param="info_value")
        test_logger.warning("这是警告日志", test_param="warning_value")
        test_logger.error("这是错误日志", test_param="error_value")
        print("✅ 不同级别日志记录成功")
        
        # 测试请求追踪
        request_id = test_logger.set_request_id()
        test_logger.info("带请求ID的日志", operation="test_request_tracking")
        print(f"✅ 请求追踪功能正常，请求ID: {request_id}")
        
        # 测试专用日志方法
        test_logger.api_call("test_method", 0.123, status="success")
        test_logger.performance("test_operation", 0.456)
        test_logger.cache_operation("get", "test_key", hit=True)
        test_logger.database_operation("select", "test_table", duration=0.089)
        print("✅ 专用日志方法测试成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 结构化日志记录器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_logger_integration():
    """测试API日志记录器集成"""
    print("\n" + "=" * 60)
    print("🔍 测试API日志记录器集成")
    print("=" * 60)
    
    try:
        from src.utils.logger import get_api_logger
        
        # 获取API日志记录器
        api_logger = get_api_logger()
        print("✅ API日志记录器获取成功")
        
        # 测试API专用日志
        api_logger.info("API服务启动", service="P9SystemAdapter", port=8000)
        api_logger.api_call("get_dashboard_data", 0.234, status="success", cache_hit=True)
        api_logger.warning("API调用频率过高", endpoint="/api/status", rate="60/min")
        api_logger.error("API调用失败", endpoint="/api/data", error="数据库连接超时")
        print("✅ API专用日志记录成功")
        
        return True
        
    except Exception as e:
        print(f"❌ API日志记录器集成测试失败: {e}")
        return False

async def test_api_adapter_logging():
    """测试API适配器日志集成"""
    print("\n" + "=" * 60)
    print("🔍 测试API适配器日志集成")
    print("=" * 60)
    
    try:
        from src.web.api_adapter import P9SystemAdapter
        
        # 创建适配器实例
        adapter = P9SystemAdapter('data/fucai3d.db')
        print("✅ API适配器实例创建成功")
        
        # 测试带日志的API方法
        print("📊 测试仪表板数据获取日志...")
        dashboard_data = await adapter.get_dashboard_data()
        print("✅ 仪表板数据获取完成，检查日志输出")
        
        print("🔧 测试系统状态获取日志...")
        system_status = await adapter._get_system_status()
        print("✅ 系统状态获取完成，检查日志输出")
        
        print("📈 测试性能指标获取日志...")
        performance_metrics = await adapter._get_performance_metrics()
        print("✅ 性能指标获取完成，检查日志输出")
        
        return True
        
    except Exception as e:
        print(f"❌ API适配器日志集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_log_context_manager():
    """测试日志上下文管理器"""
    print("\n" + "=" * 60)
    print("🔍 测试日志上下文管理器")
    print("=" * 60)
    
    try:
        from src.utils.logger import get_api_logger, LogContext
        
        logger = get_api_logger()
        
        # 测试成功的操作
        with LogContext(logger, operation="test_success") as ctx:
            time.sleep(0.1)  # 模拟操作
            logger.info("操作进行中", step="processing")
        
        print("✅ 成功操作的上下文管理器测试完成")
        
        # 测试失败的操作
        try:
            with LogContext(logger, operation="test_failure") as ctx:
                time.sleep(0.05)  # 模拟操作
                raise ValueError("模拟错误")
        except ValueError:
            pass  # 预期的错误
        
        print("✅ 失败操作的上下文管理器测试完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 日志上下文管理器测试失败: {e}")
        return False

def test_log_files():
    """测试日志文件生成"""
    print("\n" + "=" * 60)
    print("🔍 测试日志文件生成")
    print("=" * 60)
    
    try:
        # 检查日志目录
        log_dir = Path("logs")
        if log_dir.exists():
            log_files = list(log_dir.glob("*.log"))
            print(f"✅ 发现 {len(log_files)} 个日志文件")
            
            for log_file in log_files:
                if log_file.stat().st_size > 0:
                    print(f"   📄 {log_file.name}: {log_file.stat().st_size} 字节")
                else:
                    print(f"   📄 {log_file.name}: 空文件")
        else:
            print("⚠️ 日志目录不存在")
            return False
        
        # 检查测试日志目录
        test_log_dir = Path("logs/test")
        if test_log_dir.exists():
            test_log_files = list(test_log_dir.glob("*.log"))
            print(f"✅ 发现 {len(test_log_files)} 个测试日志文件")
        
        return True
        
    except Exception as e:
        print(f"❌ 日志文件检查失败: {e}")
        return False

def test_log_format():
    """测试日志格式"""
    print("\n" + "=" * 60)
    print("🔍 测试日志格式")
    print("=" * 60)
    
    try:
        from src.utils.logger import get_api_logger
        
        logger = get_api_logger()
        
        # 生成一些测试日志
        request_id = logger.set_request_id("test_req_001")
        logger.info("测试日志格式", 
                   operation="format_test",
                   user_id="test_user",
                   endpoint="/api/test",
                   duration="0.123s")
        
        logger.warning("测试警告格式",
                      operation="warning_test", 
                      threshold=100,
                      current_value=150)
        
        logger.error("测试错误格式",
                    operation="error_test",
                    error_code="E001",
                    error_message="模拟错误")
        
        print("✅ 日志格式测试完成，请检查日志文件内容")
        
        return True
        
    except Exception as e:
        print(f"❌ 日志格式测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 P9系统错误日志优化测试")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试结构化日志记录器
    logger_test_passed = test_structured_logger()
    
    # 测试API日志记录器集成
    api_logger_test_passed = test_api_logger_integration()
    
    # 测试API适配器日志集成
    adapter_logging_test_passed = asyncio.run(test_api_adapter_logging())
    
    # 测试日志上下文管理器
    context_test_passed = test_log_context_manager()
    
    # 测试日志文件生成
    file_test_passed = test_log_files()
    
    # 测试日志格式
    format_test_passed = test_log_format()
    
    # 总结测试结果
    print("\n" + "=" * 60)
    print("📊 阶段4错误日志优化测试结果")
    print("=" * 60)
    print(f"结构化日志记录器: {'✅ 通过' if logger_test_passed else '❌ 失败'}")
    print(f"API日志记录器集成: {'✅ 通过' if api_logger_test_passed else '❌ 失败'}")
    print(f"API适配器日志集成: {'✅ 通过' if adapter_logging_test_passed else '❌ 失败'}")
    print(f"日志上下文管理器: {'✅ 通过' if context_test_passed else '❌ 失败'}")
    print(f"日志文件生成: {'✅ 通过' if file_test_passed else '❌ 失败'}")
    print(f"日志格式测试: {'✅ 通过' if format_test_passed else '❌ 失败'}")
    
    all_passed = all([
        logger_test_passed, api_logger_test_passed, adapter_logging_test_passed,
        context_test_passed, file_test_passed, format_test_passed
    ])
    
    if all_passed:
        print("\n🎉 阶段4错误日志优化验证通过！")
        print("📋 日志优化特性:")
        print("   ✅ 统一的日志格式")
        print("   ✅ 详细的上下文信息")
        print("   ✅ 日志级别分类")
        print("   ✅ 请求追踪功能")
        print("   ✅ 结构化日志输出")
        print("   ✅ 专用日志方法")
        return True
    else:
        print("\n⚠️ 部分日志优化验证失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
