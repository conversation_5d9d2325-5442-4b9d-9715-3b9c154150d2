#!/usr/bin/env python3
"""
项目清理脚本

清理P8项目中的临时文件、缓存文件和重复文档
保持项目目录整洁

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import os
import shutil
from pathlib import Path

def cleanup_project():
    """清理项目文件"""
    project_root = Path(__file__).parent.parent
    
    print("🧹 开始清理P8项目...")
    
    # 需要清理的目录和文件
    cleanup_items = [
        # 缓存目录
        "cache",
        "venv", 
        "analysis_output",
        "__pycache__",
        
        # 临时文件
        "*.pyc",
        "*.pyo", 
        "*.tmp",
        "*.log.backup*",
        
        # 重复的项目管理文档（保留docs目录下的最新版本）
        "项目交接文档.md",  # 根目录下的，保留docs下的
        "P8项目完成总结.md",  # 根目录下的，保留docs下的
    ]
    
    cleaned_count = 0
    
    # 清理缓存目录
    cache_dirs = ["cache", "venv", "analysis_output"]
    for cache_dir in cache_dirs:
        cache_path = project_root / cache_dir
        if cache_path.exists():
            try:
                shutil.rmtree(cache_path)
                print(f"✓ 删除缓存目录: {cache_dir}")
                cleaned_count += 1
            except Exception as e:
                print(f"✗ 删除缓存目录失败 {cache_dir}: {e}")
    
    # 清理__pycache__目录
    for pycache in project_root.rglob("__pycache__"):
        try:
            shutil.rmtree(pycache)
            print(f"✓ 删除缓存目录: {pycache.relative_to(project_root)}")
            cleaned_count += 1
        except Exception as e:
            print(f"✗ 删除缓存目录失败 {pycache}: {e}")
    
    # 清理.pyc文件
    for pyc_file in project_root.rglob("*.pyc"):
        try:
            pyc_file.unlink()
            print(f"✓ 删除缓存文件: {pyc_file.relative_to(project_root)}")
            cleaned_count += 1
        except Exception as e:
            print(f"✗ 删除缓存文件失败 {pyc_file}: {e}")
    
    # 清理重复文档
    duplicate_docs = [
        "项目交接文档.md",
        "P8项目完成总结.md"
    ]
    
    for doc in duplicate_docs:
        doc_path = project_root / doc
        if doc_path.exists():
            try:
                doc_path.unlink()
                print(f"✓ 删除重复文档: {doc}")
                cleaned_count += 1
            except Exception as e:
                print(f"✗ 删除重复文档失败 {doc}: {e}")
    
    # 整理文档结构
    organize_docs()
    
    print(f"\n🎉 项目清理完成！共清理 {cleaned_count} 个项目")
    print("\n📁 当前项目结构:")
    print_project_structure()

def organize_docs():
    """整理文档结构"""
    project_root = Path(__file__).parent.parent
    docs_dir = project_root / "docs"
    
    print("\n📚 整理文档结构...")
    
    # 确保docs目录存在
    docs_dir.mkdir(exist_ok=True)
    
    # 需要移动到docs目录的文档
    docs_to_move = [
        "P8使用指南.md",
        "快速开始指南.md", 
        "P8系统实施计划.md"
    ]
    
    for doc in docs_to_move:
        source = project_root / doc
        target = docs_dir / doc
        
        if source.exists() and not target.exists():
            try:
                shutil.move(str(source), str(target))
                print(f"✓ 移动文档: {doc} -> docs/")
            except Exception as e:
                print(f"✗ 移动文档失败 {doc}: {e}")

def print_project_structure():
    """打印项目结构"""
    project_root = Path(__file__).parent.parent
    
    # 核心目录
    core_dirs = [
        "src",
        "tests", 
        "config",
        "docs",
        "scripts",
        "data",
        "logs"
    ]
    
    print("\n核心目录结构:")
    for dir_name in core_dirs:
        dir_path = project_root / dir_name
        if dir_path.exists():
            file_count = len(list(dir_path.rglob("*")))
            print(f"  📁 {dir_name}/ ({file_count} 个文件)")
        else:
            print(f"  ❌ {dir_name}/ (不存在)")
    
    # 核心文件
    core_files = [
        "p8_fusion_cli.py",
        "create_fusion_db.py", 
        "README.md",
        "requirements.txt"
    ]
    
    print("\n核心文件:")
    for file_name in core_files:
        file_path = project_root / file_name
        if file_path.exists():
            size = file_path.stat().st_size
            print(f"  📄 {file_name} ({size} bytes)")
        else:
            print(f"  ❌ {file_name} (不存在)")

if __name__ == "__main__":
    cleanup_project()
