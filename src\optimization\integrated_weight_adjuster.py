#!/usr/bin/env python3
"""
P9集成动态权重调整器

该模块集成P8 DynamicWeightAdjuster和AutoAdjustmentTrigger，提供：
1. 智能权重优化算法
2. 实时权重调整机制
3. 权重变化历史记录
4. 自动触发条件管理
5. 权重调整效果评估

作者: Augment Code AI Assistant
创建日期: 2025-01-14
版本: 1.0.0
"""

import numpy as np
import pandas as pd
import sqlite3
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple, Callable
from dataclasses import dataclass, asdict
from enum import Enum
from collections import defaultdict, deque
import threading
import time

# 导入P8组件
try:
    from ..fusion.dynamic_weight_adjuster import DynamicWeightAdjuster, PerformanceRecord
    from ..fusion.auto_adjustment_trigger import AutoAdjustmentTrigger, TriggerCondition, AdjustmentAction
except ImportError:
    # 如果导入失败，定义基本类
    @dataclass
    class PerformanceRecord:
        predictor_name: str
        issue: str
        predicted_value: Any
        actual_value: Any
        accuracy: float
        confidence: float
        timestamp: datetime
    
    @dataclass
    class TriggerCondition:
        name: str
        metric_name: str
        condition: str
        threshold: float
        window_size: int
        min_samples: int
        enabled: bool = True
    
    @dataclass
    class AdjustmentAction:
        trigger_name: str
        action_type: str
        parameters: Dict[str, Any]
        timestamp: datetime
        success: bool
        details: str

class AdjustmentStrategy(Enum):
    """调整策略枚举"""
    CONSERVATIVE = "conservative"
    MODERATE = "moderate"
    AGGRESSIVE = "aggressive"
    ADAPTIVE = "adaptive"

class WeightChangeType(Enum):
    """权重变化类型枚举"""
    INCREASE = "increase"
    DECREASE = "decrease"
    REBALANCE = "rebalance"
    RESET = "reset"

@dataclass
class WeightChangeRecord:
    """权重变化记录"""
    change_id: str
    component_name: str
    old_weight: float
    new_weight: float
    change_type: WeightChangeType
    change_reason: str
    trigger_condition: Optional[str]
    performance_before: Dict[str, float]
    performance_after: Optional[Dict[str, float]]
    timestamp: datetime
    effectiveness_score: Optional[float] = None

class IntegratedWeightAdjuster:
    """集成动态权重调整器 - P9扩展组件"""
    
    def __init__(self, db_path: str, config: Optional[Dict[str, Any]] = None):
        """
        初始化集成权重调整器
        
        Args:
            db_path: 数据库路径
            config: 配置参数
        """
        self.db_path = db_path
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 调整配置
        self.adjustment_config = {
            'strategy': AdjustmentStrategy(self.config.get('strategy', 'moderate')),
            'learning_rate': self.config.get('learning_rate', 0.1),
            'adjustment_frequency': self.config.get('adjustment_frequency', 3600),  # 1小时
            'min_performance_samples': self.config.get('min_performance_samples', 10),
            'weight_change_threshold': self.config.get('weight_change_threshold', 0.05),
            'max_weight_change': self.config.get('max_weight_change', 0.3),
            'effectiveness_evaluation_period': self.config.get('effectiveness_evaluation_period', 24)
        }
        
        # 尝试初始化P8组件
        self.p8_weight_adjuster = None
        self.p8_auto_trigger = None
        self._initialize_p8_components()
        
        # 集成组件
        self.current_weights = {}
        self.weight_history = deque(maxlen=1000)
        self.adjustment_queue = deque()
        self.trigger_conditions = {}
        
        # 监控线程
        self.monitoring_thread = None
        self.monitoring_active = False
        
        # 性能评估
        self.performance_evaluator = None
        
        # 初始化组件
        self._initialize_default_weights()
        self._initialize_trigger_conditions()
        self._ensure_weight_tables()
        
        self.logger.info("集成动态权重调整器初始化完成")
    
    def _initialize_p8_components(self):
        """初始化P8组件"""
        try:
            # 初始化P8动态权重调整器
            p8_config = {
                'learning_rate': self.adjustment_config['learning_rate'],
                'evaluation_window': 30,
                'min_samples': self.adjustment_config['min_performance_samples']
            }
            
            if 'DynamicWeightAdjuster' in globals():
                self.p8_weight_adjuster = DynamicWeightAdjuster(self.db_path, p8_config)
                self.logger.info("P8动态权重调整器初始化成功")
            else:
                self.logger.warning("P8动态权重调整器类不可用")
            
            # 初始化P8自动触发器
            if 'AutoAdjustmentTrigger' in globals() and self.p8_weight_adjuster:
                trigger_config = {
                    'cooldown_period': self.adjustment_config['adjustment_frequency']
                }
                self.p8_auto_trigger = AutoAdjustmentTrigger(
                    self.db_path, None, trigger_config
                )
                self.logger.info("P8自动触发器初始化成功")
            else:
                self.logger.warning("P8自动触发器类不可用")
                
        except Exception as e:
            self.logger.error(f"P8组件初始化失败: {e}")
    
    def _initialize_default_weights(self):
        """初始化默认权重"""
        self.current_weights = {
            'hundreds_predictor': 1.0,
            'tens_predictor': 1.0,
            'units_predictor': 1.0,
            'sum_predictor': 0.8,
            'span_predictor': 0.6,
            'fusion_predictor': 1.2
        }
        
        # 记录初始权重
        self._record_weight_change(
            'system_initialization',
            self.current_weights,
            {},
            WeightChangeType.RESET,
            "系统初始化默认权重"
        )
    
    def _initialize_trigger_conditions(self):
        """初始化触发条件"""
        default_conditions = {
            'low_accuracy': TriggerCondition(
                "低准确率触发", "accuracy", "<", 0.25, 10, 5, True
            ),
            'high_error_rate': TriggerCondition(
                "高错误率触发", "error_rate", ">", 0.15, 5, 3, True
            ),
            'poor_hit_rate': TriggerCondition(
                "低命中率触发", "hit_rate", "<", 0.10, 8, 4, True
            ),
            'weight_imbalance': TriggerCondition(
                "权重失衡触发", "weight_variance", ">", 0.4, 5, 3, True
            ),
            'performance_degradation': TriggerCondition(
                "性能下降触发", "performance_trend", "<", -0.05, 15, 8, True
            )
        }
        
        self.trigger_conditions.update(default_conditions)
    
    def _ensure_weight_tables(self):
        """确保权重管理表存在"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建权重历史表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS weight_change_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    change_id TEXT UNIQUE NOT NULL,
                    component_name TEXT NOT NULL,
                    old_weight REAL NOT NULL,
                    new_weight REAL NOT NULL,
                    change_type TEXT NOT NULL,
                    change_reason TEXT NOT NULL,
                    trigger_condition TEXT,
                    performance_before TEXT,
                    performance_after TEXT,
                    effectiveness_score REAL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 创建当前权重表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS current_weights (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    component_name TEXT UNIQUE NOT NULL,
                    weight_value REAL NOT NULL,
                    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    update_reason TEXT
                )
            """)
            
            # 创建触发条件配置表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS weight_trigger_conditions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    condition_name TEXT UNIQUE NOT NULL,
                    metric_name TEXT NOT NULL,
                    condition_operator TEXT NOT NULL,
                    threshold_value REAL NOT NULL,
                    window_size INTEGER NOT NULL,
                    min_samples INTEGER NOT NULL,
                    enabled BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            conn.commit()
            conn.close()
            
            self.logger.info("权重管理表创建成功")
            
        except Exception as e:
            self.logger.error(f"创建权重管理表失败: {e}")
    
    def start_intelligent_adjustment(self):
        """启动智能权重调整"""
        try:
            if self.monitoring_active:
                self.logger.warning("智能权重调整已在运行")
                return
            
            self.monitoring_active = True
            self.monitoring_thread = threading.Thread(target=self._adjustment_loop, daemon=True)
            self.monitoring_thread.start()
            
            self.logger.info("智能权重调整已启动")
            
        except Exception as e:
            self.logger.error(f"启动智能权重调整失败: {e}")
    
    def stop_intelligent_adjustment(self):
        """停止智能权重调整"""
        try:
            self.monitoring_active = False
            if self.monitoring_thread and self.monitoring_thread.is_alive():
                self.monitoring_thread.join(timeout=5)
            
            self.logger.info("智能权重调整已停止")
            
        except Exception as e:
            self.logger.error(f"停止智能权重调整失败: {e}")
    
    def _adjustment_loop(self):
        """权重调整循环"""
        while self.monitoring_active:
            try:
                # 收集性能数据
                performance_data = self._collect_performance_data()
                
                # 检查触发条件
                triggered_conditions = self._check_trigger_conditions(performance_data)
                
                # 执行权重调整
                if triggered_conditions:
                    self._execute_weight_adjustments(triggered_conditions, performance_data)
                
                # 评估调整效果
                self._evaluate_adjustment_effectiveness()
                
                # 等待下一次检查
                time.sleep(self.adjustment_config['adjustment_frequency'])
                
            except Exception as e:
                self.logger.error(f"权重调整循环出错: {e}")
                time.sleep(60)  # 出错时等待1分钟再继续

    def _collect_performance_data(self) -> Dict[str, Any]:
        """收集性能数据"""
        try:
            conn = sqlite3.connect(self.db_path)

            # 查询最近的性能数据
            query = """
                SELECT
                    component_name,
                    AVG(current_value) as avg_performance,
                    COUNT(*) as sample_count,
                    MIN(monitor_time) as earliest_time,
                    MAX(monitor_time) as latest_time
                FROM system_performance_monitor
                WHERE monitor_time > datetime('now', '-1 hour')
                GROUP BY component_name
            """

            df = pd.read_sql_query(query, conn)

            # 查询准确率数据
            accuracy_query = """
                SELECT
                    'hundreds_accuracy' as component,
                    AVG(CASE WHEN predicted_hundreds = actual_hundreds THEN 1.0 ELSE 0.0 END) as accuracy
                FROM fusion_predictions
                WHERE prediction_time > datetime('now', '-1 hour')
                UNION ALL
                SELECT
                    'tens_accuracy' as component,
                    AVG(CASE WHEN predicted_tens = actual_tens THEN 1.0 ELSE 0.0 END) as accuracy
                FROM fusion_predictions
                WHERE prediction_time > datetime('now', '-1 hour')
                UNION ALL
                SELECT
                    'units_accuracy' as component,
                    AVG(CASE WHEN predicted_units = actual_units THEN 1.0 ELSE 0.0 END) as accuracy
                FROM fusion_predictions
                WHERE prediction_time > datetime('now', '-1 hour')
            """

            accuracy_df = pd.read_sql_query(accuracy_query, conn)
            conn.close()

            # 组织性能数据
            performance_data = {
                'component_performance': df.to_dict('records') if not df.empty else [],
                'accuracy_metrics': accuracy_df.to_dict('records') if not accuracy_df.empty else [],
                'collection_time': datetime.now().isoformat(),
                'data_quality': {
                    'total_components': len(df) if not df.empty else 0,
                    'total_samples': df['sample_count'].sum() if not df.empty else 0
                }
            }

            return performance_data

        except Exception as e:
            self.logger.error(f"收集性能数据失败: {e}")
            return {}

    def _check_trigger_conditions(self, performance_data: Dict[str, Any]) -> List[str]:
        """检查触发条件"""
        triggered_conditions = []

        try:
            accuracy_metrics = performance_data.get('accuracy_metrics', [])

            for condition_name, condition in self.trigger_conditions.items():
                if not condition.enabled:
                    continue

                # 检查准确率条件
                if condition.metric_name == 'accuracy':
                    for metric in accuracy_metrics:
                        if metric.get('accuracy') is not None:
                            accuracy = metric['accuracy']
                            if self._evaluate_condition(accuracy, condition):
                                triggered_conditions.append(condition_name)
                                self.logger.warning(f"触发条件: {condition.name}, 当前值: {accuracy}")

                # 检查其他条件（简化实现）
                elif condition.metric_name in ['error_rate', 'hit_rate']:
                    # 模拟检查
                    simulated_value = 0.12  # 模拟值
                    if self._evaluate_condition(simulated_value, condition):
                        triggered_conditions.append(condition_name)

            return triggered_conditions

        except Exception as e:
            self.logger.error(f"检查触发条件失败: {e}")
            return []

    def _evaluate_condition(self, value: float, condition: TriggerCondition) -> bool:
        """评估单个条件"""
        try:
            if condition.condition == '>':
                return value > condition.threshold
            elif condition.condition == '<':
                return value < condition.threshold
            elif condition.condition == '>=':
                return value >= condition.threshold
            elif condition.condition == '<=':
                return value <= condition.threshold
            elif condition.condition == '==':
                return abs(value - condition.threshold) < 0.001
            elif condition.condition == '!=':
                return abs(value - condition.threshold) >= 0.001

            return False

        except Exception as e:
            self.logger.error(f"评估条件失败: {e}")
            return False

    def _execute_weight_adjustments(self, triggered_conditions: List[str],
                                  performance_data: Dict[str, Any]):
        """执行权重调整"""
        try:
            for condition_name in triggered_conditions:
                condition = self.trigger_conditions[condition_name]

                # 根据条件类型选择调整策略
                if condition_name == 'low_accuracy':
                    self._adjust_for_low_accuracy(performance_data)
                elif condition_name == 'high_error_rate':
                    self._adjust_for_high_error_rate(performance_data)
                elif condition_name == 'poor_hit_rate':
                    self._adjust_for_poor_hit_rate(performance_data)
                elif condition_name == 'weight_imbalance':
                    self._rebalance_weights(performance_data)
                elif condition_name == 'performance_degradation':
                    self._adjust_for_performance_degradation(performance_data)

        except Exception as e:
            self.logger.error(f"执行权重调整失败: {e}")

    def _adjust_for_low_accuracy(self, performance_data: Dict[str, Any]):
        """针对低准确率调整权重"""
        try:
            accuracy_metrics = performance_data.get('accuracy_metrics', [])

            for metric in accuracy_metrics:
                component = metric.get('component', '')
                accuracy = metric.get('accuracy', 0)

                if accuracy < 0.25:  # 准确率低于25%
                    # 降低对应组件的权重
                    component_key = component.replace('_accuracy', '_predictor')
                    if component_key in self.current_weights:
                        old_weight = self.current_weights[component_key]
                        new_weight = max(0.5, old_weight * 0.9)  # 降低10%，最低0.5

                        self._update_component_weight(
                            component_key, new_weight, old_weight,
                            WeightChangeType.DECREASE,
                            f"准确率过低({accuracy:.3f})，降低权重"
                        )

        except Exception as e:
            self.logger.error(f"调整低准确率权重失败: {e}")

    def _adjust_for_high_error_rate(self, performance_data: Dict[str, Any]):
        """针对高错误率调整权重"""
        try:
            # 降低所有预测器权重
            for component_name in self.current_weights:
                if 'predictor' in component_name:
                    old_weight = self.current_weights[component_name]
                    new_weight = max(0.3, old_weight * 0.85)  # 降低15%

                    self._update_component_weight(
                        component_name, new_weight, old_weight,
                        WeightChangeType.DECREASE,
                        "系统错误率过高，降低预测器权重"
                    )

        except Exception as e:
            self.logger.error(f"调整高错误率权重失败: {e}")

    def _adjust_for_poor_hit_rate(self, performance_data: Dict[str, Any]):
        """针对低命中率调整权重"""
        try:
            # 提高融合预测器权重，降低单独预测器权重
            if 'fusion_predictor' in self.current_weights:
                old_weight = self.current_weights['fusion_predictor']
                new_weight = min(2.0, old_weight * 1.1)  # 提高10%

                self._update_component_weight(
                    'fusion_predictor', new_weight, old_weight,
                    WeightChangeType.INCREASE,
                    "命中率过低，提高融合预测器权重"
                )

        except Exception as e:
            self.logger.error(f"调整低命中率权重失败: {e}")

    def _rebalance_weights(self, performance_data: Dict[str, Any]):
        """重新平衡权重"""
        try:
            # 计算权重方差
            weights = list(self.current_weights.values())
            weight_variance = np.var(weights)

            if weight_variance > 0.4:  # 权重差异过大
                # 向平均值靠拢
                mean_weight = np.mean(weights)

                for component_name, old_weight in self.current_weights.items():
                    # 向平均值调整20%
                    adjustment = (mean_weight - old_weight) * 0.2
                    new_weight = max(0.3, min(2.0, old_weight + adjustment))

                    if abs(new_weight - old_weight) > 0.05:  # 变化超过5%才调整
                        self._update_component_weight(
                            component_name, new_weight, old_weight,
                            WeightChangeType.REBALANCE,
                            f"权重重新平衡，方差过大({weight_variance:.3f})"
                        )

        except Exception as e:
            self.logger.error(f"重新平衡权重失败: {e}")

    def _adjust_for_performance_degradation(self, performance_data: Dict[str, Any]):
        """针对性能下降调整权重"""
        try:
            # 重置所有权重到默认值
            default_weights = {
                'hundreds_predictor': 1.0,
                'tens_predictor': 1.0,
                'units_predictor': 1.0,
                'sum_predictor': 0.8,
                'span_predictor': 0.6,
                'fusion_predictor': 1.2
            }

            for component_name, new_weight in default_weights.items():
                old_weight = self.current_weights.get(component_name, 1.0)

                self._update_component_weight(
                    component_name, new_weight, old_weight,
                    WeightChangeType.RESET,
                    "性能持续下降，重置权重到默认值"
                )

        except Exception as e:
            self.logger.error(f"调整性能下降权重失败: {e}")

    def _update_component_weight(self, component_name: str, new_weight: float,
                               old_weight: float, change_type: WeightChangeType,
                               reason: str, trigger_condition: Optional[str] = None):
        """更新组件权重"""
        try:
            # 应用权重约束
            new_weight = max(0.1, min(3.0, new_weight))

            # 检查变化是否足够大
            if abs(new_weight - old_weight) < self.adjustment_config['weight_change_threshold']:
                return

            # 更新当前权重
            self.current_weights[component_name] = new_weight

            # 记录权重变化
            self._record_weight_change(
                component_name, {component_name: new_weight}, {component_name: old_weight},
                change_type, reason, trigger_condition
            )

            # 保存到数据库
            self._save_weight_to_db(component_name, new_weight, reason)

            self.logger.info(f"权重已更新: {component_name} {old_weight:.3f} -> {new_weight:.3f} ({reason})")

        except Exception as e:
            self.logger.error(f"更新组件权重失败: {e}")

    def _record_weight_change(self, component_name: str, new_weights: Dict[str, float],
                            old_weights: Dict[str, float], change_type: WeightChangeType,
                            reason: str, trigger_condition: Optional[str] = None):
        """记录权重变化"""
        try:
            change_record = WeightChangeRecord(
                change_id=f"weight_change_{component_name}_{int(datetime.now().timestamp())}",
                component_name=component_name,
                old_weight=old_weights.get(component_name, 0.0),
                new_weight=new_weights.get(component_name, 0.0),
                change_type=change_type,
                change_reason=reason,
                trigger_condition=trigger_condition,
                performance_before={},  # 可以添加性能数据
                performance_after=None,
                timestamp=datetime.now()
            )

            # 添加到历史记录
            self.weight_history.append(change_record)

            # 保存到数据库
            self._save_weight_change_to_db(change_record)

        except Exception as e:
            self.logger.error(f"记录权重变化失败: {e}")

    def _save_weight_to_db(self, component_name: str, weight_value: float, reason: str):
        """保存权重到数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                INSERT OR REPLACE INTO current_weights
                (component_name, weight_value, last_updated, update_reason)
                VALUES (?, ?, ?, ?)
            """, (component_name, weight_value, datetime.now().isoformat(), reason))

            conn.commit()
            conn.close()

        except Exception as e:
            self.logger.error(f"保存权重到数据库失败: {e}")

    def _save_weight_change_to_db(self, change_record: WeightChangeRecord):
        """保存权重变化记录到数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                INSERT INTO weight_change_history
                (change_id, component_name, old_weight, new_weight, change_type,
                 change_reason, trigger_condition, performance_before,
                 performance_after, effectiveness_score, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                change_record.change_id,
                change_record.component_name,
                change_record.old_weight,
                change_record.new_weight,
                change_record.change_type.value,
                change_record.change_reason,
                change_record.trigger_condition,
                json.dumps(change_record.performance_before),
                json.dumps(change_record.performance_after) if change_record.performance_after else None,
                change_record.effectiveness_score,
                change_record.timestamp.isoformat()
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            self.logger.error(f"保存权重变化记录失败: {e}")

    def _evaluate_adjustment_effectiveness(self):
        """评估调整效果"""
        try:
            # 获取最近的权重变化
            recent_changes = [
                change for change in self.weight_history
                if change.timestamp > datetime.now() - timedelta(
                    hours=self.adjustment_config['effectiveness_evaluation_period']
                )
            ]

            for change in recent_changes:
                if change.effectiveness_score is None:
                    # 计算效果评分
                    effectiveness = self._calculate_effectiveness_score(change)
                    change.effectiveness_score = effectiveness

                    # 更新数据库记录
                    self._update_effectiveness_score(change.change_id, effectiveness)

        except Exception as e:
            self.logger.error(f"评估调整效果失败: {e}")

    def _calculate_effectiveness_score(self, change_record: WeightChangeRecord) -> float:
        """计算效果评分"""
        try:
            # 简化的效果评分算法
            # 实际应该比较调整前后的性能指标

            # 基于权重变化类型给出基础分数
            base_scores = {
                WeightChangeType.INCREASE: 0.6,
                WeightChangeType.DECREASE: 0.5,
                WeightChangeType.REBALANCE: 0.7,
                WeightChangeType.RESET: 0.4
            }

            base_score = base_scores.get(change_record.change_type, 0.5)

            # 添加随机因子模拟实际效果
            import random
            effectiveness = base_score + random.uniform(-0.2, 0.3)

            return max(0.0, min(1.0, effectiveness))

        except Exception as e:
            self.logger.error(f"计算效果评分失败: {e}")
            return 0.5

    def _update_effectiveness_score(self, change_id: str, effectiveness_score: float):
        """更新效果评分"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                UPDATE weight_change_history
                SET effectiveness_score = ?
                WHERE change_id = ?
            """, (effectiveness_score, change_id))

            conn.commit()
            conn.close()

        except Exception as e:
            self.logger.error(f"更新效果评分失败: {e}")

    def get_current_weights(self) -> Dict[str, float]:
        """获取当前权重"""
        return self.current_weights.copy()

    def get_weight_history(self, component_name: Optional[str] = None,
                          hours_back: int = 24) -> List[Dict[str, Any]]:
        """获取权重历史"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours_back)

            filtered_history = []
            for change in self.weight_history:
                if change.timestamp > cutoff_time:
                    if not component_name or change.component_name == component_name:
                        change_dict = asdict(change)
                        change_dict['timestamp'] = change.timestamp.isoformat()
                        change_dict['change_type'] = change.change_type.value
                        filtered_history.append(change_dict)

            # 按时间倒序排列
            filtered_history.sort(key=lambda x: x['timestamp'], reverse=True)

            return filtered_history

        except Exception as e:
            self.logger.error(f"获取权重历史失败: {e}")
            return []

    def manual_weight_adjustment(self, component_name: str, new_weight: float,
                                reason: str = "手动调整") -> bool:
        """手动调整权重"""
        try:
            if component_name not in self.current_weights:
                self.logger.error(f"未知组件: {component_name}")
                return False

            old_weight = self.current_weights[component_name]

            self._update_component_weight(
                component_name, new_weight, old_weight,
                WeightChangeType.INCREASE if new_weight > old_weight else WeightChangeType.DECREASE,
                reason
            )

            return True

        except Exception as e:
            self.logger.error(f"手动调整权重失败: {e}")
            return False

    def reset_weights_to_default(self) -> bool:
        """重置权重到默认值"""
        try:
            default_weights = {
                'hundreds_predictor': 1.0,
                'tens_predictor': 1.0,
                'units_predictor': 1.0,
                'sum_predictor': 0.8,
                'span_predictor': 0.6,
                'fusion_predictor': 1.2
            }

            for component_name, new_weight in default_weights.items():
                old_weight = self.current_weights.get(component_name, 1.0)
                self._update_component_weight(
                    component_name, new_weight, old_weight,
                    WeightChangeType.RESET,
                    "手动重置到默认值"
                )

            return True

        except Exception as e:
            self.logger.error(f"重置权重失败: {e}")
            return False

    def get_adjustment_statistics(self, hours_back: int = 24) -> Dict[str, Any]:
        """获取调整统计"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours_back)

            recent_changes = [
                change for change in self.weight_history
                if change.timestamp > cutoff_time
            ]

            # 统计各类型调整
            type_counts = {}
            for change in recent_changes:
                change_type = change.change_type.value
                type_counts[change_type] = type_counts.get(change_type, 0) + 1

            # 统计组件调整频率
            component_counts = {}
            for change in recent_changes:
                component = change.component_name
                component_counts[component] = component_counts.get(component, 0) + 1

            # 计算平均效果
            effectiveness_scores = [
                change.effectiveness_score for change in recent_changes
                if change.effectiveness_score is not None
            ]
            avg_effectiveness = np.mean(effectiveness_scores) if effectiveness_scores else 0.0

            return {
                'total_adjustments': len(recent_changes),
                'adjustment_types': type_counts,
                'component_adjustments': component_counts,
                'average_effectiveness': avg_effectiveness,
                'analysis_period_hours': hours_back,
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"获取调整统计失败: {e}")
            return {'error': str(e)}

    def get_trigger_status(self) -> Dict[str, Any]:
        """获取触发器状态"""
        try:
            trigger_status = {}

            for condition_name, condition in self.trigger_conditions.items():
                trigger_status[condition_name] = {
                    'name': condition.name,
                    'metric_name': condition.metric_name,
                    'condition': condition.condition,
                    'threshold': condition.threshold,
                    'window_size': condition.window_size,
                    'min_samples': condition.min_samples,
                    'enabled': condition.enabled
                }

            return {
                'trigger_conditions': trigger_status,
                'total_conditions': len(self.trigger_conditions),
                'enabled_conditions': sum(1 for c in self.trigger_conditions.values() if c.enabled),
                'monitoring_active': self.monitoring_active,
                'adjustment_frequency': self.adjustment_config['adjustment_frequency']
            }

        except Exception as e:
            self.logger.error(f"获取触发器状态失败: {e}")
            return {'error': str(e)}

    def update_trigger_condition(self, condition_name: str, **kwargs) -> bool:
        """更新触发条件"""
        try:
            if condition_name not in self.trigger_conditions:
                self.logger.error(f"未知触发条件: {condition_name}")
                return False

            condition = self.trigger_conditions[condition_name]

            # 更新属性
            for key, value in kwargs.items():
                if hasattr(condition, key):
                    setattr(condition, key, value)

            self.logger.info(f"触发条件已更新: {condition_name}")
            return True

        except Exception as e:
            self.logger.error(f"更新触发条件失败: {e}")
            return False

    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        try:
            status = {
                'monitoring_active': self.monitoring_active,
                'p8_components': {
                    'weight_adjuster_available': self.p8_weight_adjuster is not None,
                    'auto_trigger_available': self.p8_auto_trigger is not None
                },
                'current_weights': self.current_weights,
                'adjustment_config': {
                    'strategy': self.adjustment_config['strategy'].value,
                    'learning_rate': self.adjustment_config['learning_rate'],
                    'adjustment_frequency': self.adjustment_config['adjustment_frequency']
                },
                'recent_activity': {
                    'total_weight_changes': len(self.weight_history),
                    'recent_changes_1h': len([
                        change for change in self.weight_history
                        if change.timestamp > datetime.now() - timedelta(hours=1)
                    ]),
                    'last_adjustment': max([
                        change.timestamp for change in self.weight_history
                    ], default=None)
                },
                'trigger_conditions_count': len(self.trigger_conditions),
                'enabled_triggers': sum(1 for c in self.trigger_conditions.values() if c.enabled),
                'timestamp': datetime.now().isoformat()
            }

            # 转换datetime为字符串
            if status['recent_activity']['last_adjustment']:
                status['recent_activity']['last_adjustment'] = status['recent_activity']['last_adjustment'].isoformat()

            return status

        except Exception as e:
            self.logger.error(f"获取系统状态失败: {e}")
            return {'error': str(e)}

    def export_weight_data(self, start_time: Optional[datetime] = None,
                          end_time: Optional[datetime] = None) -> Dict[str, Any]:
        """导出权重数据"""
        try:
            conn = sqlite3.connect(self.db_path)

            # 构建查询条件
            where_conditions = []
            params = []

            if start_time:
                where_conditions.append("created_at >= ?")
                params.append(start_time.isoformat())

            if end_time:
                where_conditions.append("created_at <= ?")
                params.append(end_time.isoformat())

            where_clause = "WHERE " + " AND ".join(where_conditions) if where_conditions else ""

            query = f"""
                SELECT change_id, component_name, old_weight, new_weight,
                       change_type, change_reason, trigger_condition,
                       effectiveness_score, created_at
                FROM weight_change_history
                {where_clause}
                ORDER BY created_at DESC
            """

            df = pd.read_sql_query(query, conn, params=params)
            conn.close()

            if df.empty:
                return {'message': '暂无权重数据', 'data': []}

            # 转换为字典格式
            export_data = df.to_dict('records')

            return {
                'data': export_data,
                'total_records': len(export_data),
                'components_included': df['component_name'].unique().tolist(),
                'time_range': {
                    'start': df['created_at'].min(),
                    'end': df['created_at'].max()
                },
                'export_time': datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"导出权重数据失败: {e}")
            return {'error': str(e)}
