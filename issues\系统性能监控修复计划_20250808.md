# 系统性能监控修复计划

## 背景
文件名：系统性能监控修复计划_20250808.md
创建于：2025-08-08_23:50:00
创建者：Augment Agent
主分支：main
任务分支：fix/performance-monitoring-20250808
Yolo模式：Off

## 任务描述
修复福彩3D预测系统中的性能监控相关问题：
1. 后端持续报错"获取性能指标失败: 数据库查询失败，无法获取真实性能数据"
2. 前端API请求频率过高，每几秒就发送大量请求
3. 系统日志被错误信息刷屏，影响正常监控

## 项目概览
福彩3D预测系统 - 基于P8智能交集融合系统和P9智能优化管理器的彩票预测平台
- 后端：FastAPI + SQLite + P8/P9预测系统
- 前端：React + TypeScript + Ant Design
- 数据库：SQLite (data/lottery.db)

⚠️ 警告：永远不要修改此部分 ⚠️
核心RIPER-5协议规则：
- 严格按照计划执行，不偏离既定步骤
- 每次修改前必须备份关键文件
- 保持最小化修改原则，避免影响现有功能
- 修改后立即进行功能验证
- 记录所有修改和验证结果
⚠️ 警告：永远不要修改此部分 ⚠️

## 分析

### 问题根因分析（已更新）
1. **数据库表存在但无数据**：
   - ✅ `enhanced_performance_monitor` 表存在于 `data/fucai3d.db`
   - ❌ 表中没有数据（record_count: 0）
   - API适配器查询空表时抛出异常

2. **API错误处理不当**：
   - 文件：`src/web/api_adapter.py`
   - 方法：`_get_performance_metrics` (175-218行)
   - 当表为空时直接抛出异常，而不是返回模拟数据或空结果

3. **前端轮询频率过高**：
   - Dashboard组件：30秒轮询 `/api/status`
   - usePredictionData Hook：30秒轮询预测数据
   - useRealTimeData Hook：WebSocket断开时频繁轮询
   - 可能存在多个组件实例或WebSocket连接问题

4. **数据库架构确认**：
   - `data/alerts.db`：2个表 (警报系统)
   - `data/fucai3d.db`：15个表 (P8/P9系统和性能监控) ⭐ **主数据库**
   - `data/lottery.db`：22个表 (历史数据和预测结果)
   - API适配器正确连接到 `fucai3d.db`

### 影响范围
- 后端日志被错误信息刷屏
- 前端用户体验可能受到影响
- 服务器资源消耗过高
- 不影响核心预测功能

## 提议的解决方案

### 方案1：数据库表修复 (优先级：高)
**优点**：解决根本问题，消除错误源头
**缺点**：需要创建新的数据库结构
**工作量**：低 (1-2小时)

### 方案2：API错误处理优化 (优先级：中)
**优点**：提高系统健壮性，减少日志错误
**缺点**：治标不治本
**工作量**：低 (30分钟)

### 方案3：前端轮询优化 (优先级：中)
**优点**：减少服务器负载，改善用户体验
**缺点**：可能影响数据实时性
**工作量**：中 (1小时)

## 当前执行步骤："1. 创建缺失的性能监控数据库表"

## 实施清单（已更新）：

### 阶段1：API适配器修复（优先级：高）
1. **备份API适配器文件**
   - 源文件：`src/web/api_adapter.py`
   - 备份文件：`src/web/api_adapter.py.backup`
   - 预期结果：备份文件创建成功

2. **修改_get_performance_metrics方法**
   - 文件：`src/web/api_adapter.py`
   - 行数范围：175-218行
   - 修改内容：优化空表查询处理逻辑
   - 预期结果：当表为空时返回模拟数据而不是抛出异常

3. **验证API修复效果**
   - 启动后端服务
   - 观察日志确认无"数据库查询失败"错误
   - 预期结果：API调用正常，无异常日志

### 阶段2：前端轮询优化（优先级：中）
4. **备份前端组件文件**
   - 备份Dashboard.tsx、usePredictionData.ts、useRealTimeData.ts
   - 预期结果：备份文件创建成功

5. **修改Dashboard组件轮询间隔**
   - 文件：`web-frontend/src/components/Dashboard.tsx`
   - 行数：第73行
   - 修改：30000ms → 60000ms
   - 预期结果：Dashboard轮询频率降低

6. **修改usePredictionData轮询间隔**
   - 文件：`web-frontend/src/hooks/usePredictionData.ts`
   - 行数：第136行
   - 修改：30000ms → 60000ms
   - 预期结果：预测数据轮询频率降低

7. **优化useRealTimeData WebSocket逻辑**
   - 文件：`web-frontend/src/hooks/useRealTimeData.ts`
   - 行数：第185行
   - 修改：增加重连间隔，添加防重复请求
   - 预期结果：WebSocket连接更稳定

### 阶段3：可选数据初始化（优先级：低）
8. **创建性能监控初始数据脚本**
   - 文件：`initialize_performance_data.py`
   - 功能：向enhanced_performance_monitor表插入初始监控数据
   - 预期结果：表中有基础监控数据

9. **执行数据初始化**
   - 命令：`python initialize_performance_data.py`
   - 预期结果：性能监控显示真实数据而非模拟数据

### 阶段4：系统验证
10. **启动后端服务验证**
    - 命令：`python src/web/app.py`
    - 验证：观察日志无"数据库查询失败"错误
    - 预期结果：后端启动正常，无错误日志

11. **启动前端服务验证**
    - 命令：`cd web-frontend && npm run dev`
    - 验证：观察API请求频率降低
    - 预期结果：前端启动正常，请求频率合理

12. **API功能验证**
    - 测试：`/api/status`、`/api/prediction/latest`等关键端点
    - 预期结果：所有API正常返回数据

13. **系统稳定性测试**
    - 运行时间：10分钟
    - 观察：日志无异常，API请求频率稳定
    - 预期结果：系统稳定运行

## 技术规范

### 数据库表结构
```sql
-- enhanced_performance_monitor 表
CREATE TABLE IF NOT EXISTS enhanced_performance_monitor (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    component_name TEXT NOT NULL,
    performance_metric TEXT NOT NULL,
    current_value REAL NOT NULL,
    threshold_value REAL NOT NULL,
    status TEXT NOT NULL,
    monitor_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- performance_monitor 表  
CREATE TABLE IF NOT EXISTS performance_monitor (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    component_name TEXT NOT NULL,
    metric_name TEXT NOT NULL,
    metric_value REAL NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### API修改规范
- 文件：`src/web/api_adapter.py`
- 方法：`_get_performance_metrics`
- 修改类型：添加表存在性检查，优化异常处理
- 预期结果：当表不存在时返回模拟数据，不抛出异常

### 前端修改规范
- 轮询间隔：30秒 → 60秒
- 添加请求去重机制
- 改善WebSocket重连逻辑
- 保持功能完整性

## 任务进度
[待执行]

## 最终审查
[待完成]
