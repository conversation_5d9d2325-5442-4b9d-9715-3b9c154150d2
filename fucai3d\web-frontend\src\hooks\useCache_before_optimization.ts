import { useState, useEffect, useCallback, useRef } from 'react'

interface CacheItem<T> {
  data: T
  timestamp: number
  expiry: number
}

interface CacheOptions {
  ttl?: number // 缓存时间（毫秒）
  maxSize?: number // 最大缓存条目数
  staleWhileRevalidate?: boolean // 是否在后台更新过期数据
}

class MemoryCache {
  private cache = new Map<string, CacheItem<any>>()
  private maxSize: number
  private accessOrder = new Map<string, number>() // LRU跟踪

  constructor(maxSize: number = 100) {
    this.maxSize = maxSize
  }

  set<T>(key: string, data: T, ttl: number): void {
    const now = Date.now()
    const expiry = now + ttl

    // 如果缓存已满，删除最久未使用的项
    if (this.cache.size >= this.maxSize && !this.cache.has(key)) {
      this.evictLRU()
    }

    this.cache.set(key, { data, timestamp: now, expiry })
    this.accessOrder.set(key, now)
  }

  get<T>(key: string): T | null {
    const item = this.cache.get(key)
    if (!item) return null

    const now = Date.now()
    
    // 更新访问时间
    this.accessOrder.set(key, now)

    // 检查是否过期
    if (now > item.expiry) {
      this.cache.delete(key)
      this.accessOrder.delete(key)
      return null
    }

    return item.data
  }

  has(key: string): boolean {
    const item = this.cache.get(key)
    if (!item) return false

    const now = Date.now()
    if (now > item.expiry) {
      this.cache.delete(key)
      this.accessOrder.delete(key)
      return false
    }

    return true
  }

  delete(key: string): void {
    this.cache.delete(key)
    this.accessOrder.delete(key)
  }

  clear(): void {
    this.cache.clear()
    this.accessOrder.clear()
  }

  private evictLRU(): void {
    let oldestKey = ''
    let oldestTime = Date.now()

    for (const [key, time] of this.accessOrder) {
      if (time < oldestTime) {
        oldestTime = time
        oldestKey = key
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey)
      this.accessOrder.delete(oldestKey)
    }
  }

  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      keys: Array.from(this.cache.keys())
    }
  }
}

// 全局缓存实例
const globalCache = new MemoryCache(200)

export const useCache = <T>(
  key: string,
  fetcher: () => Promise<T>,
  options: CacheOptions = {}
) => {
  const {
    ttl = 5 * 60 * 1000, // 默认5分钟
    staleWhileRevalidate = true
  } = options

  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const [lastFetch, setLastFetch] = useState<number>(0)

  const fetcherRef = useRef(fetcher)
  fetcherRef.current = fetcher

  const fetchData = useCallback(async (forceRefresh = false) => {
    try {
      setError(null)

      // 检查缓存
      if (!forceRefresh) {
        const cachedData = globalCache.get<T>(key)
        if (cachedData !== null) {
          setData(cachedData)
          setLastFetch(Date.now())
          return cachedData
        }
      }

      setLoading(true)
      const result = await fetcherRef.current()
      
      // 存储到缓存
      globalCache.set(key, result, ttl)
      
      setData(result)
      setLastFetch(Date.now())
      setLoading(false)
      
      return result
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error')
      setError(error)
      setLoading(false)
      throw error
    }
  }, [key, ttl])

  const revalidate = useCallback(() => {
    return fetchData(true)
  }, [fetchData])

  const mutate = useCallback((newData: T | ((prevData: T | null) => T)) => {
    const updatedData = typeof newData === 'function' 
      ? (newData as (prevData: T | null) => T)(data)
      : newData

    setData(updatedData)
    globalCache.set(key, updatedData, ttl)
  }, [key, ttl, data])

  const invalidate = useCallback(() => {
    globalCache.delete(key)
    setData(null)
    setLastFetch(0)
  }, [key])

  // 初始加载
  useEffect(() => {
    fetchData()
  }, [fetchData])

  // 后台重新验证（stale-while-revalidate）
  useEffect(() => {
    if (!staleWhileRevalidate || !data) return

    const now = Date.now()
    const timeSinceLastFetch = now - lastFetch

    // 如果数据已经过期一半时间，在后台重新获取
    if (timeSinceLastFetch > ttl / 2) {
      fetchData(true).catch(() => {
        // 静默失败，保持现有数据
      })
    }
  }, [data, lastFetch, ttl, staleWhileRevalidate, fetchData])

  return {
    data,
    loading,
    error,
    revalidate,
    mutate,
    invalidate,
    isStale: lastFetch > 0 && (Date.now() - lastFetch) > ttl
  }
}

// 缓存管理工具
export const cacheManager = {
  clear: () => globalCache.clear(),
  delete: (key: string) => globalCache.delete(key),
  has: (key: string) => globalCache.has(key),
  getStats: () => globalCache.getStats(),
  
  // 预加载数据
  preload: async <T>(key: string, fetcher: () => Promise<T>, ttl = 5 * 60 * 1000) => {
    try {
      const data = await fetcher()
      globalCache.set(key, data, ttl)
      return data
    } catch (error) {
      console.warn(`预加载失败 ${key}:`, error)
      return null
    }
  },

  // 批量预加载
  preloadBatch: async (items: Array<{
    key: string
    fetcher: () => Promise<any>
    ttl?: number
  }>) => {
    const promises = items.map(item => 
      cacheManager.preload(item.key, item.fetcher, item.ttl)
    )
    
    return Promise.allSettled(promises)
  }
}

// 专用的API缓存Hook
export const useApiCache = <T>(
  url: string,
  options: CacheOptions & { enabled?: boolean } = {}
) => {
  const { enabled = true, ...cacheOptions } = options

  const fetcher = useCallback(async (): Promise<T> => {
    const response = await fetch(url)
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    return response.json()
  }, [url])

  const result = useCache<T>(
    `api:${url}`,
    fetcher,
    cacheOptions
  )

  // 如果禁用，返回空状态
  if (!enabled) {
    return {
      data: null,
      loading: false,
      error: null,
      revalidate: async () => null,
      mutate: () => {},
      invalidate: () => {},
      isStale: false
    }
  }

  return result
}

export default useCache
