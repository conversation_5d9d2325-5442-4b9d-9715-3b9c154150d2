# 独立预测器统一架构设计

## 📋 设计概述

**设计理念**: 🎯 **独立位置预测** - 每个位置作为完全独立的随机变量进行预测  
**核心原则**: 符合福彩3D的随机性本质，避免虚假关联性，专注单位置优化  
**技术基础**: 基于P2高级特征工程系统的统一架构  

## 🎯 设计哲学

### 独立性原理
```
P(直选=ijk) = P(百位=i) × P(十位=j) × P(个位=k)
```

- **数学基础**: 每个位置理论上是独立的随机变量
- **实践验证**: 如果每个位置预测准确率达到35%，直选准确率 = 0.35³ = 4.29%
- **风险控制**: 避免关联性噪音影响单位置预测准确率

### 设计优势
- **简单可靠**: 每个预测器专注于自身位置的深度优化
- **并行开发**: P3、P4、P5可以同时开发，提高效率
- **独立调优**: 每个位置可以使用最适合的算法和参数
- **错误隔离**: 单个预测器的问题不会影响其他位置
- **易于维护**: 代码结构清晰，调试和优化更容易

## 🏗️ 统一架构设计

### 1. 基础预测器类
```python
from abc import ABC, abstractmethod
from typing import Dict, List, Tuple, Optional
import numpy as np
import pandas as pd
import logging
from src.interfaces.predictor_feature_interface import PredictorFeatureInterface, FeatureConfig
from src.data.cache_optimizer import CacheOptimizer, CacheConfig

class BaseIndependentPredictor(ABC):
    """
    独立位置预测器基类
    
    设计原则：
    - 每个位置完全独立预测
    - 基于P2系统的特征工程
    - 统一的接口和性能标准
    """
    
    def __init__(self, position: str, db_path: str):
        """
        初始化独立预测器
        
        Args:
            position: 位置类型 ('hundreds', 'tens', 'units')
            db_path: 数据库路径
        """
        self.position = position
        self.db_path = db_path
        self.model = None
        self.is_trained = False
        
        # 初始化P2系统组件
        self.feature_interface = PredictorFeatureInterface(db_path, position)
        
        # 配置缓存优化器
        cache_config = CacheConfig(
            memory_size=200,
            db_cache_enabled=True,
            db_cache_path=f"cache/{position}_predictor_cache.db"
        )
        self.cache_optimizer = CacheOptimizer(cache_config)
        
        # 配置日志
        self.logger = logging.getLogger(f"{position.title()}Predictor")
        
    @abstractmethod
    def build_model(self):
        """构建模型"""
        pass
    
    @abstractmethod
    def train(self, X: np.ndarray, y: np.ndarray):
        """训练模型"""
        pass
    
    @abstractmethod
    def predict_probability(self, X: np.ndarray) -> np.ndarray:
        """预测概率分布（独立预测，无依赖）"""
        pass
    
    def load_training_data(self, limit: Optional[int] = None) -> Tuple[np.ndarray, np.ndarray]:
        """基于P2系统加载训练数据"""
        config = FeatureConfig(
            feature_types=[self.position, 'common'],
            window_size=20,
            lag_features=[1, 2, 3, 5, 7],
            feature_selection=True,
            normalization="standard"
        )
        
        # 获取历史期号
        import sqlite3
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        query = "SELECT issue FROM lottery_data ORDER BY issue"
        if limit:
            query += f" LIMIT {limit}"
            
        cursor.execute(query)
        issues = [row[0] for row in cursor.fetchall()]
        conn.close()
        
        # 使用P2系统创建ML数据集
        dataset = self.feature_interface.create_ml_pipeline(issues, config)
        
        self.logger.info(f"{self.position}位加载 {len(issues)} 期数据，特征维度: {dataset.X.shape}")
        
        return dataset.X.values, dataset.y.values
    
    def predict_next_period(self, current_issue: str) -> Dict[str, any]:
        """预测下一期的概率分布"""
        if not self.is_trained:
            raise ValueError(f"{self.position}位预测器尚未训练")
        
        # 获取当前期特征
        features = self.feature_interface.get_prediction_features(current_issue)
        X = np.array(features).reshape(1, -1)
        
        # 预测概率分布
        probabilities = self.predict_probability(X)[0]
        
        # 获取最高概率的数字
        predicted_digit = np.argmax(probabilities)
        confidence = probabilities[predicted_digit]
        
        return {
            'position': self.position,
            'probabilities': probabilities,
            'predicted_digit': predicted_digit,
            'confidence': confidence,
            'issue': current_issue
        }
```

### 2. 具体实现类
```python
# P3 - 百位预测器
class HundredsPredictor(BaseIndependentPredictor):
    def __init__(self, db_path: str):
        super().__init__('hundreds', db_path)

# P4 - 十位预测器  
class TensPredictor(BaseIndependentPredictor):
    def __init__(self, db_path: str):
        super().__init__('tens', db_path)

# P5 - 个位预测器
class UnitsPredictor(BaseIndependentPredictor):
    def __init__(self, db_path: str):
        super().__init__('units', db_path)
```

### 3. 预测器管理器
```python
class IndependentPredictorManager:
    """独立预测器管理器"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.predictors = {
            'hundreds': HundredsPredictor(db_path),
            'tens': TensPredictor(db_path),
            'units': UnitsPredictor(db_path)
        }
    
    def train_all(self, limit: Optional[int] = None):
        """并行训练所有预测器"""
        for position, predictor in self.predictors.items():
            print(f"训练{position}位预测器...")
            X, y = predictor.load_training_data(limit)
            predictor.train(X, y)
    
    def predict_all_positions(self, current_issue: str) -> Dict[str, Dict]:
        """预测所有位置"""
        results = {}
        for position, predictor in self.predictors.items():
            results[position] = predictor.predict_next_period(current_issue)
        return results
    
    def get_direct_combination(self, current_issue: str) -> List[str]:
        """获取直选号码组合"""
        results = self.predict_all_positions(current_issue)
        
        combinations = []
        h_digit = results['hundreds']['predicted_digit']
        t_digit = results['tens']['predicted_digit']
        u_digit = results['units']['predicted_digit']
        
        # 最高概率组合
        combinations.append(f"{h_digit}{t_digit}{u_digit}")
        
        return combinations
```

## 📊 性能目标

### 统一性能标准
- **单位置准确率**: > 35% (每个位置独立达到)
- **Top3准确率**: > 70% (前3个概率最高的数字)
- **直选理论准确率**: 0.35³ = 4.29% (远超随机的0.1%)
- **预测响应时间**: < 2秒 (每个位置)

### 质量保证
- **独立验证**: 每个位置独立进行交叉验证
- **并行测试**: 支持多位置同时测试
- **性能监控**: 实时监控每个位置的预测准确率
- **错误隔离**: 单位置问题不影响其他位置

## 🚀 实施优势

### 开发效率
- **并行开发**: P3、P4、P5可以同时开发
- **代码复用**: 统一的基类减少重复代码
- **快速迭代**: 每个位置可以独立优化和测试

### 系统稳定性
- **风险分散**: 单点故障不会影响整个系统
- **简单可靠**: 避免复杂的依赖关系
- **易于调试**: 问题定位更加精确

### 扩展性
- **模块化设计**: 便于后续P6-P11的开发
- **算法灵活**: 每个位置可以使用不同的最优算法
- **配置独立**: 支持每个位置的个性化配置

这种独立预测器架构完全符合福彩3D的随机性本质，最大化了预测准确率和系统稳定性。
