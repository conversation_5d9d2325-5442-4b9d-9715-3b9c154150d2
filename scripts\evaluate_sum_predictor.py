#!/usr/bin/env python3
"""
P6和值预测器评估脚本

支持模型性能评估和约束一致性验证
提供详细的评估报告和可视化图表

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import sys
import os
import argparse
import logging
import json
import numpy as np
import pandas as pd
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.predictors.sum_predictor import SumPredictor

def setup_logging(log_level: str = "INFO"):
    """设置日志配置"""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[logging.StreamHandler(sys.stdout)]
    )

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="P6和值预测器评估脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 评估所有模型
  python evaluate_sum_predictor.py
  
  # 评估指定模型
  python evaluate_sum_predictor.py --model xgb
  
  # 评估约束一致性
  python evaluate_sum_predictor.py --constraint-evaluation
  
  # 生成详细报告
  python evaluate_sum_predictor.py --detailed-report --output-dir reports/
        """
    )
    
    parser.add_argument(
        "--model", "-m",
        type=str,
        choices=["xgb", "lgb", "lstm", "distribution", "constraint", "ensemble", "all"],
        default="all",
        help="要评估的模型类型 (默认: all)"
    )
    
    parser.add_argument(
        "--model-path",
        type=str,
        default=None,
        help="模型文件路径 (可选)"
    )
    
    parser.add_argument(
        "--config", "-c",
        type=str,
        default="config/sum_predictor_config.yaml",
        help="配置文件路径"
    )
    
    parser.add_argument(
        "--db-path", "-d",
        type=str,
        default="data/lottery.db",
        help="数据库文件路径"
    )
    
    parser.add_argument(
        "--test-size",
        type=float,
        default=0.2,
        help="测试集比例 (默认: 0.2)"
    )
    
    parser.add_argument(
        "--constraint-evaluation",
        action="store_true",
        help="执行约束一致性评估"
    )
    
    parser.add_argument(
        "--cross-validation",
        action="store_true",
        help="执行交叉验证评估"
    )
    
    parser.add_argument(
        "--cv-folds",
        type=int,
        default=5,
        help="交叉验证折数"
    )
    
    parser.add_argument(
        "--detailed-report",
        action="store_true",
        help="生成详细评估报告"
    )
    
    parser.add_argument(
        "--output-dir", "-o",
        type=str,
        default="reports/sum_predictor",
        help="输出目录"
    )
    
    parser.add_argument(
        "--save-predictions",
        action="store_true",
        help="保存预测结果"
    )
    
    parser.add_argument(
        "--log-level",
        type=str,
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="日志级别"
    )
    
    return parser.parse_args()

def load_test_data(predictor: SumPredictor, test_size: float = 0.2):
    """加载测试数据"""
    logger = logging.getLogger("load_test_data")
    
    try:
        # 使用XGB模型的数据加载方法
        X, y = predictor.models['xgb'].load_data()
        
        # 分割数据
        from sklearn.model_selection import train_test_split
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=test_size, random_state=42
        )
        
        logger.info(f"加载测试数据: {len(X_test)} 个样本")
        return X_test, y_test
        
    except Exception as e:
        logger.error(f"加载测试数据失败: {e}")
        raise

def evaluate_single_model(predictor: SumPredictor, model_name: str, 
                         X_test: np.ndarray, y_test: np.ndarray) -> Dict[str, Any]:
    """评估单个模型"""
    logger = logging.getLogger("evaluate_single_model")
    
    try:
        logger.info(f"评估模型: {model_name}")
        
        # 切换到指定模型
        original_model = predictor.current_model
        predictor.switch_model(model_name)
        
        # 执行评估
        performance = predictor.evaluate(X_test, y_test)
        
        # 获取预测结果
        predictions, confidences = predictor.predict_with_confidence(X_test)
        
        # 计算额外指标
        additional_metrics = {
            'mean_confidence': float(np.mean(confidences)),
            'std_confidence': float(np.std(confidences)),
            'prediction_range': {
                'min': float(np.min(predictions)),
                'max': float(np.max(predictions)),
                'mean': float(np.mean(predictions)),
                'std': float(np.std(predictions))
            }
        }
        
        # 合并结果
        result = {**performance, **additional_metrics}
        result['model_name'] = model_name
        result['test_samples'] = len(X_test)
        
        # 恢复原模型
        predictor.switch_model(original_model)
        
        return result
        
    except Exception as e:
        logger.error(f"评估模型 {model_name} 失败: {e}")
        return {'model_name': model_name, 'error': str(e)}

def evaluate_all_models(predictor: SumPredictor, X_test: np.ndarray, y_test: np.ndarray) -> Dict[str, Any]:
    """评估所有模型"""
    logger = logging.getLogger("evaluate_all_models")
    
    results = {}
    
    for model_name in predictor.get_available_models():
        if predictor.models[model_name].is_trained:
            results[model_name] = evaluate_single_model(predictor, model_name, X_test, y_test)
        else:
            logger.warning(f"模型 {model_name} 尚未训练，跳过评估")
            results[model_name] = {'model_name': model_name, 'status': 'not_trained'}
    
    return results

def evaluate_constraint_consistency(predictor: SumPredictor, X_test: np.ndarray, y_test: np.ndarray) -> Dict[str, Any]:
    """评估约束一致性"""
    logger = logging.getLogger("evaluate_constraint_consistency")
    
    try:
        logger.info("评估约束一致性")
        
        # 生成示例位置预测（实际应该从位置预测器获取）
        position_predictions = {
            'hundreds_pred': np.random.randint(0, 10, len(X_test)),
            'tens_pred': np.random.randint(0, 10, len(X_test)),
            'units_pred': np.random.randint(0, 10, len(X_test))
        }
        
        # 评估约束一致性
        constraint_results = predictor.evaluate_constraint_consistency(
            X_test, y_test, position_predictions
        )
        
        return constraint_results
        
    except Exception as e:
        logger.error(f"约束一致性评估失败: {e}")
        return {'error': str(e)}

def compare_models(evaluation_results: Dict[str, Any]) -> Dict[str, Any]:
    """比较模型性能"""
    comparison = {
        'best_accuracy': {'model': None, 'value': 0},
        'best_mae': {'model': None, 'value': float('inf')},
        'best_rmse': {'model': None, 'value': float('inf')},
        'best_confidence': {'model': None, 'value': 0},
        'model_ranking': []
    }
    
    valid_results = {k: v for k, v in evaluation_results.items() 
                    if isinstance(v, dict) and 'error' not in v and 'status' not in v}
    
    if not valid_results:
        return comparison
    
    # 找出最佳模型
    for model_name, result in valid_results.items():
        # 最佳准确率
        accuracy = result.get('accuracy', 0)
        if accuracy > comparison['best_accuracy']['value']:
            comparison['best_accuracy'] = {'model': model_name, 'value': accuracy}
        
        # 最佳MAE
        mae = result.get('mae', float('inf'))
        if mae < comparison['best_mae']['value']:
            comparison['best_mae'] = {'model': model_name, 'value': mae}
        
        # 最佳RMSE
        rmse = result.get('rmse', float('inf'))
        if rmse < comparison['best_rmse']['value']:
            comparison['best_rmse'] = {'model': model_name, 'value': rmse}
        
        # 最佳置信度
        confidence = result.get('mean_confidence', 0)
        if confidence > comparison['best_confidence']['value']:
            comparison['best_confidence'] = {'model': model_name, 'value': confidence}
    
    # 模型排名（基于综合评分）
    model_scores = []
    for model_name, result in valid_results.items():
        # 综合评分：准确率权重0.5，MAE权重0.3，置信度权重0.2
        accuracy = result.get('accuracy', 0)
        mae = result.get('mae', 10)  # 默认较大值
        confidence = result.get('mean_confidence', 0)
        
        # 归一化MAE（越小越好）
        mae_score = max(0, 1 - mae / 10)
        
        combined_score = 0.5 * accuracy + 0.3 * mae_score + 0.2 * confidence
        model_scores.append({'model': model_name, 'score': combined_score})
    
    # 按评分排序
    model_scores.sort(key=lambda x: x['score'], reverse=True)
    comparison['model_ranking'] = model_scores
    
    return comparison

def generate_report(evaluation_results: Dict[str, Any], comparison: Dict[str, Any], 
                   constraint_results: Dict[str, Any], args) -> str:
    """生成评估报告"""
    report_lines = []
    
    # 报告头部
    report_lines.append("=" * 80)
    report_lines.append("P6和值预测器评估报告")
    report_lines.append("=" * 80)
    report_lines.append(f"评估时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report_lines.append(f"评估模型: {args.model}")
    report_lines.append("")
    
    # 模型性能摘要
    report_lines.append("模型性能摘要")
    report_lines.append("-" * 40)
    
    for model_name, result in evaluation_results.items():
        if isinstance(result, dict) and 'error' not in result and 'status' not in result:
            report_lines.append(f"\n{model_name.upper()} 模型:")
            report_lines.append(f"  准确率: {result.get('accuracy', 0):.4f}")
            report_lines.append(f"  MAE: {result.get('mae', 0):.4f}")
            report_lines.append(f"  RMSE: {result.get('rmse', 0):.4f}")
            report_lines.append(f"  R²分数: {result.get('r2_score', 0):.4f}")
            report_lines.append(f"  平均置信度: {result.get('mean_confidence', 0):.4f}")
            report_lines.append(f"  测试样本数: {result.get('test_samples', 0)}")
        elif 'error' in result:
            report_lines.append(f"\n{model_name.upper()} 模型: 评估失败 - {result['error']}")
        elif 'status' in result:
            report_lines.append(f"\n{model_name.upper()} 模型: {result['status']}")
    
    # 模型比较
    if comparison['model_ranking']:
        report_lines.append("\n\n模型排名")
        report_lines.append("-" * 40)
        
        for i, model_info in enumerate(comparison['model_ranking'], 1):
            report_lines.append(f"{i}. {model_info['model'].upper()}: {model_info['score']:.4f}")
        
        report_lines.append(f"\n最佳准确率: {comparison['best_accuracy']['model']} ({comparison['best_accuracy']['value']:.4f})")
        report_lines.append(f"最佳MAE: {comparison['best_mae']['model']} ({comparison['best_mae']['value']:.4f})")
        report_lines.append(f"最佳RMSE: {comparison['best_rmse']['model']} ({comparison['best_rmse']['value']:.4f})")
    
    # 约束一致性评估
    if constraint_results and 'error' not in constraint_results:
        report_lines.append("\n\n约束一致性评估")
        report_lines.append("-" * 40)
        report_lines.append(f"平均约束分数: {constraint_results.get('avg_constraint_score', 0):.4f}")
        report_lines.append(f"约束一致性率: {constraint_results.get('constraint_consistency_rate', 0):.4f}")
        report_lines.append(f"相对基础模型改进: {constraint_results.get('improvement_over_base', 0):.4f}")
    
    return "\n".join(report_lines)

def save_results(evaluation_results: Dict[str, Any], comparison: Dict[str, Any], 
                constraint_results: Dict[str, Any], report: str, args):
    """保存评估结果"""
    logger = logging.getLogger("save_results")
    
    try:
        # 创建输出目录
        output_dir = Path(args.output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存详细结果
        results_file = output_dir / f"evaluation_results_{timestamp}.json"
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump({
                'evaluation_results': evaluation_results,
                'comparison': comparison,
                'constraint_results': constraint_results,
                'evaluation_time': datetime.now().isoformat(),
                'args': vars(args)
            }, f, indent=2, ensure_ascii=False, default=str)
        
        # 保存报告
        report_file = output_dir / f"evaluation_report_{timestamp}.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        logger.info(f"评估结果已保存到: {output_dir}")
        
    except Exception as e:
        logger.error(f"保存评估结果失败: {e}")

def main():
    """主函数"""
    args = parse_arguments()
    
    # 设置日志
    setup_logging(args.log_level)
    logger = logging.getLogger("main")
    
    try:
        logger.info("开始P6和值预测器评估")
        
        # 检查数据库文件
        if not Path(args.db_path).exists():
            logger.error(f"数据库文件不存在: {args.db_path}")
            return 1
        
        # 初始化预测器
        logger.info("初始化和值预测器")
        predictor = SumPredictor(args.db_path, args.config)
        
        # 加载模型
        if args.model_path:
            if not predictor.load_model(args.model_path):
                logger.error(f"无法加载模型: {args.model_path}")
                return 1
        else:
            predictor.build_model()
        
        # 加载测试数据
        X_test, y_test = load_test_data(predictor, args.test_size)
        
        # 执行评估
        if args.model == "all":
            evaluation_results = evaluate_all_models(predictor, X_test, y_test)
        else:
            result = evaluate_single_model(predictor, args.model, X_test, y_test)
            evaluation_results = {args.model: result}
        
        # 模型比较
        comparison = compare_models(evaluation_results)
        
        # 约束一致性评估
        constraint_results = {}
        if args.constraint_evaluation:
            constraint_results = evaluate_constraint_consistency(predictor, X_test, y_test)
        
        # 生成报告
        report = generate_report(evaluation_results, comparison, constraint_results, args)
        
        # 显示报告
        print(report)
        
        # 保存结果
        if args.detailed_report:
            save_results(evaluation_results, comparison, constraint_results, report, args)
        
        logger.info("评估完成")
        return 0
        
    except Exception as e:
        logger.error(f"评估过程发生错误: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
