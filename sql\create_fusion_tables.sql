-- P8智能交集融合系统数据表创建脚本
-- Author: Augment Code AI Assistant
-- Date: 2025-01-14

-- 最终预测结果表（优化版）
CREATE TABLE IF NOT EXISTS final_predictions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    issue TEXT NOT NULL,
    prediction_rank INTEGER NOT NULL,   -- 推荐排名
    hundreds INTEGER NOT NULL,
    tens INTEGER NOT NULL,
    units INTEGER NOT NULL,
    sum_value INTEGER NOT NULL,
    span_value INTEGER NOT NULL,
    
    -- 概率信息
    combined_probability REAL NOT NULL, -- 综合概率
    hundreds_prob REAL NOT NULL,        -- 百位概率
    tens_prob REAL NOT NULL,            -- 十位概率
    units_prob REAL NOT NULL,           -- 个位概率
    sum_prob REAL NOT NULL,             -- 和值概率
    span_prob REAL NOT NULL,            -- 跨度概率
    
    -- 一致性分数
    sum_consistency REAL NOT NULL,      -- 和值一致性
    span_consistency REAL NOT NULL,     -- 跨度一致性
    constraint_score REAL NOT NULL,     -- 约束分数
    diversity_score REAL NOT NULL,      -- 多样性分数
    
    -- 元数据
    confidence_level TEXT,              -- 置信水平(high/medium/low)
    fusion_method TEXT,                 -- 融合方法
    ranking_strategy TEXT,              -- 排序策略
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 索引
    UNIQUE(issue, prediction_rank)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_final_predictions_issue ON final_predictions(issue);
CREATE INDEX IF NOT EXISTS idx_final_predictions_rank ON final_predictions(prediction_rank);
CREATE INDEX IF NOT EXISTS idx_final_predictions_created_at ON final_predictions(created_at);

-- 融合权重配置表（优化版）
CREATE TABLE IF NOT EXISTS fusion_weights (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    weight_type TEXT NOT NULL,          -- position/sum/span/constraint/diversity
    predictor_name TEXT NOT NULL,       -- 预测器名称(hundreds/tens/units/sum/span)
    model_name TEXT,                    -- 具体模型名称(xgb/lgb/lstm/ensemble等)
    weight_value REAL NOT NULL,         -- 权重值
    performance_score REAL,             -- 性能分数
    accuracy_rate REAL,                 -- 准确率
    confidence_score REAL,              -- 置信度分数
    is_active BOOLEAN DEFAULT TRUE,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 索引
    UNIQUE(weight_type, predictor_name, model_name)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_fusion_weights_type ON fusion_weights(weight_type);
CREATE INDEX IF NOT EXISTS idx_fusion_weights_predictor ON fusion_weights(predictor_name);
CREATE INDEX IF NOT EXISTS idx_fusion_weights_active ON fusion_weights(is_active);

-- 预测性能评估表（优化版）
CREATE TABLE IF NOT EXISTS prediction_performance (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    issue TEXT NOT NULL,
    
    -- 实际结果
    actual_hundreds INTEGER,
    actual_tens INTEGER,
    actual_units INTEGER,
    actual_sum INTEGER,
    actual_span INTEGER,
    
    -- 预测结果评估
    predicted_rank INTEGER,             -- 实际号码在预测中的排名
    hit_type TEXT,                      -- 命中类型(exact/position/partial/none)
    
    -- 各位置准确性
    hundreds_accuracy BOOLEAN,
    tens_accuracy BOOLEAN,
    units_accuracy BOOLEAN,
    sum_accuracy BOOLEAN,
    span_accuracy BOOLEAN,
    
    -- 评分指标
    overall_score REAL,                 -- 综合评分
    probability_score REAL,             -- 概率预测评分
    constraint_score REAL,              -- 约束一致性评分
    diversity_effectiveness REAL,       -- 多样性有效性
    
    -- 融合系统特定指标
    fusion_method TEXT,                 -- 使用的融合方法
    ranking_strategy TEXT,              -- 使用的排序策略
    top_k_hit INTEGER,                  -- Top-K命中情况
    confidence_accuracy REAL,           -- 置信度准确性
    
    evaluated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 索引
    UNIQUE(issue)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_prediction_performance_issue ON prediction_performance(issue);
CREATE INDEX IF NOT EXISTS idx_prediction_performance_hit_type ON prediction_performance(hit_type);
CREATE INDEX IF NOT EXISTS idx_prediction_performance_evaluated_at ON prediction_performance(evaluated_at);

-- 约束规则表（优化版）
CREATE TABLE IF NOT EXISTS fusion_constraint_rules (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    rule_name TEXT NOT NULL,
    rule_type TEXT NOT NULL,            -- sum_span/probability/consistency/diversity
    rule_expression TEXT NOT NULL,      -- 规则表达式
    weight REAL DEFAULT 1.0,
    tolerance REAL DEFAULT 0.1,         -- 容差范围
    is_active BOOLEAN DEFAULT TRUE,
    priority INTEGER DEFAULT 1,         -- 规则优先级
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 索引
    UNIQUE(rule_name)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_fusion_constraint_rules_type ON fusion_constraint_rules(rule_type);
CREATE INDEX IF NOT EXISTS idx_fusion_constraint_rules_active ON fusion_constraint_rules(is_active);

-- 融合会话记录表
CREATE TABLE IF NOT EXISTS fusion_sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_id TEXT NOT NULL,
    issue TEXT NOT NULL,
    fusion_method TEXT NOT NULL,
    ranking_strategy TEXT NOT NULL,
    input_data TEXT NOT NULL,           -- JSON格式的输入数据
    output_data TEXT NOT NULL,          -- JSON格式的输出结果
    execution_time REAL,                -- 执行时间(秒)
    success BOOLEAN DEFAULT TRUE,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 索引
    UNIQUE(session_id)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_fusion_sessions_issue ON fusion_sessions(issue);
CREATE INDEX IF NOT EXISTS idx_fusion_sessions_created_at ON fusion_sessions(created_at);
CREATE INDEX IF NOT EXISTS idx_fusion_sessions_success ON fusion_sessions(success);

-- 权重历史表
CREATE TABLE IF NOT EXISTS weight_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    timestamp TEXT NOT NULL,
    weights TEXT NOT NULL,              -- JSON格式的权重数据
    performance_summary TEXT,           -- JSON格式的性能摘要
    adjustment_reason TEXT,             -- 调整原因
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_weight_history_timestamp ON weight_history(timestamp);
CREATE INDEX IF NOT EXISTS idx_weight_history_created_at ON weight_history(created_at);

-- 融合统计表
CREATE TABLE IF NOT EXISTS fusion_statistics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    date TEXT NOT NULL,                 -- 日期(YYYY-MM-DD)
    total_predictions INTEGER DEFAULT 0,
    successful_predictions INTEGER DEFAULT 0,
    top_1_hits INTEGER DEFAULT 0,
    top_5_hits INTEGER DEFAULT 0,
    top_10_hits INTEGER DEFAULT 0,
    average_execution_time REAL,
    average_confidence REAL,
    most_used_fusion_method TEXT,
    most_used_ranking_strategy TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 索引
    UNIQUE(date)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_fusion_statistics_date ON fusion_statistics(date);

-- 插入默认权重配置
INSERT OR IGNORE INTO fusion_weights (weight_type, predictor_name, model_name, weight_value, performance_score, accuracy_rate, confidence_score) VALUES
('position', 'hundreds', 'ensemble', 1.0, 0.7, 0.6, 0.8),
('position', 'tens', 'ensemble', 1.0, 0.7, 0.6, 0.8),
('position', 'units', 'ensemble', 1.0, 0.7, 0.6, 0.8),
('auxiliary', 'sum', 'ensemble', 0.8, 0.6, 0.5, 0.7),
('auxiliary', 'span', 'ensemble', 0.6, 0.5, 0.4, 0.6),
('constraint', 'constraint', 'optimizer', 0.4, 0.4, 0.3, 0.5);

-- 插入默认约束规则
INSERT OR IGNORE INTO fusion_constraint_rules (rule_name, rule_type, rule_expression, weight, tolerance, priority) VALUES
('sum_range_constraint', 'sum_span', 'sum_value >= 0 AND sum_value <= 27', 1.0, 0.0, 1),
('span_range_constraint', 'sum_span', 'span_value >= 0 AND span_value <= 9', 1.0, 0.0, 1),
('probability_threshold', 'probability', 'combined_probability >= 0.001', 0.8, 0.0001, 2),
('diversity_minimum', 'diversity', 'diversity_score >= 0.1', 0.6, 0.05, 3),
('consistency_threshold', 'consistency', 'constraint_score >= 0.3', 0.7, 0.1, 2);

-- 创建视图：最新预测结果
CREATE VIEW IF NOT EXISTS latest_predictions AS
SELECT 
    issue,
    prediction_rank,
    hundreds || tens || units AS combination,
    sum_value,
    span_value,
    combined_probability,
    constraint_score,
    diversity_score,
    confidence_level,
    fusion_method,
    ranking_strategy,
    created_at
FROM final_predictions
WHERE issue = (SELECT MAX(issue) FROM final_predictions)
ORDER BY prediction_rank;

-- 创建视图：性能摘要
CREATE VIEW IF NOT EXISTS performance_summary AS
SELECT 
    COUNT(*) as total_evaluations,
    SUM(CASE WHEN hit_type = 'exact' THEN 1 ELSE 0 END) as exact_hits,
    SUM(CASE WHEN hit_type IN ('exact', 'position') THEN 1 ELSE 0 END) as position_hits,
    SUM(CASE WHEN predicted_rank <= 10 THEN 1 ELSE 0 END) as top_10_hits,
    AVG(overall_score) as avg_overall_score,
    AVG(probability_score) as avg_probability_score,
    AVG(constraint_score) as avg_constraint_score,
    fusion_method,
    ranking_strategy
FROM prediction_performance
GROUP BY fusion_method, ranking_strategy;

-- 创建视图：权重趋势
CREATE VIEW IF NOT EXISTS weight_trends AS
SELECT 
    timestamp,
    json_extract(weights, '$.hundreds') as hundreds_weight,
    json_extract(weights, '$.tens') as tens_weight,
    json_extract(weights, '$.units') as units_weight,
    json_extract(weights, '$.sum') as sum_weight,
    json_extract(weights, '$.span') as span_weight,
    json_extract(weights, '$.constraint') as constraint_weight
FROM weight_history
ORDER BY timestamp DESC;
