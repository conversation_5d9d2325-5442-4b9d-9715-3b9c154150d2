# P10-Web界面系统 Docker Compose配置
# 完整的前后端部署方案

version: '3.8'

services:
  # 后端服务
  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
    container_name: fucai3d-backend
    ports:
      - "8000:8000"
    environment:
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
      - ENV=production
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    networks:
      - fucai3d-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 前端服务
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    container_name: fucai3d-frontend
    ports:
      - "80:80"
    depends_on:
      - backend
    networks:
      - fucai3d-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis缓存服务（可选）
  redis:
    image: redis:7-alpine
    container_name: fucai3d-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - fucai3d-network
    restart: unless-stopped
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 3s
      retries: 3

  # Nginx负载均衡器（可选）
  nginx:
    image: nginx:alpine
    container_name: fucai3d-nginx
    ports:
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    networks:
      - fucai3d-network
    restart: unless-stopped

networks:
  fucai3d-network:
    driver: bridge
    name: fucai3d-network

volumes:
  redis_data:
    driver: local
    name: fucai3d-redis-data
