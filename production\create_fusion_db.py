#!/usr/bin/env python3
"""
创建P8融合系统数据库表

执行SQL脚本创建融合系统所需的数据表
"""

import sqlite3
import sys
from pathlib import Path

def create_fusion_tables(db_path: str = "data/lottery.db"):
    """创建融合系统数据表"""
    
    # 读取SQL脚本
    sql_file = Path("sql/create_fusion_tables.sql")
    
    if not sql_file.exists():
        print(f"错误: SQL文件不存在: {sql_file}")
        return False
    
    try:
        with open(sql_file, 'r', encoding='utf-8') as f:
            sql_script = f.read()
        
        # 连接数据库并执行脚本
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 执行SQL脚本
            cursor.executescript(sql_script)
            conn.commit()
            
            print("✓ 融合系统数据表创建成功")
            
            # 验证表是否创建成功
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '%fusion%' OR name LIKE '%final%' OR name LIKE '%weight%'")
            tables = cursor.fetchall()
            
            print(f"✓ 创建了 {len(tables)} 个融合系统相关表:")
            for table in tables:
                print(f"  - {table[0]}")
            
            return True
            
    except Exception as e:
        print(f"错误: 创建融合系统数据表失败: {e}")
        return False

def verify_tables(db_path: str = "data/lottery.db"):
    """验证表结构"""
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 检查关键表
            key_tables = [
                'final_predictions',
                'fusion_weights', 
                'prediction_performance',
                'fusion_constraint_rules',
                'fusion_sessions',
                'weight_history',
                'fusion_statistics'
            ]
            
            print("\n=== 表结构验证 ===")
            for table in key_tables:
                cursor.execute(f"PRAGMA table_info({table})")
                columns = cursor.fetchall()
                
                if columns:
                    print(f"✓ {table}: {len(columns)} 列")
                else:
                    print(f"✗ {table}: 表不存在")
            
            # 检查视图
            cursor.execute("SELECT name FROM sqlite_master WHERE type='view'")
            views = cursor.fetchall()
            
            print(f"\n✓ 创建了 {len(views)} 个视图:")
            for view in views:
                print(f"  - {view[0]}")
            
            # 检查默认数据
            cursor.execute("SELECT COUNT(*) FROM fusion_weights")
            weight_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM fusion_constraint_rules")
            rule_count = cursor.fetchone()[0]
            
            print(f"\n✓ 默认数据:")
            print(f"  - 权重配置: {weight_count} 条")
            print(f"  - 约束规则: {rule_count} 条")
            
            return True
            
    except Exception as e:
        print(f"错误: 验证表结构失败: {e}")
        return False

if __name__ == '__main__':
    print("开始创建P8融合系统数据表...")
    
    # 创建表
    success = create_fusion_tables()
    
    if success:
        # 验证表结构
        verify_tables()
        print("\n🎉 P8融合系统数据库初始化完成!")
    else:
        print("\n❌ P8融合系统数据库初始化失败!")
        sys.exit(1)
