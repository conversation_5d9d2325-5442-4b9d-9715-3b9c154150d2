#!/usr/bin/env python3
"""
权重自动调整触发器

实现基于性能指标的权重自动调整触发机制
为P8智能交集融合系统提供自动优化功能

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Callable
import logging
import threading
import time
import json
from datetime import datetime, timedelta
from dataclasses import dataclass
from collections import defaultdict
import sqlite3

@dataclass
class TriggerCondition:
    """触发条件数据类"""
    name: str
    metric_name: str
    condition: str  # >, <, >=, <=, ==, !=
    threshold: float
    window_size: int  # 评估窗口大小（数据点数量）
    min_samples: int  # 最小样本数
    enabled: bool = True

@dataclass
class AdjustmentAction:
    """调整动作数据类"""
    trigger_name: str
    action_type: str  # weight_adjustment/parameter_tuning/strategy_change
    parameters: Dict[str, Any]
    timestamp: datetime
    success: bool
    details: str

class AutoAdjustmentTrigger:
    """权重自动调整触发器"""
    
    def __init__(self, db_path: str, fusion_predictor, config: Dict[str, Any]):
        """
        初始化自动调整触发器
        
        Args:
            db_path: 数据库路径
            fusion_predictor: 融合预测器实例
            config: 配置参数
        """
        self.db_path = db_path
        self.fusion_predictor = fusion_predictor
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 触发器状态
        self.is_active = False
        self.trigger_thread = None
        
        # 触发条件
        self.trigger_conditions = self._load_trigger_conditions()
        
        # 调整历史
        self.adjustment_history = []
        
        # 性能数据缓存
        self.performance_cache = defaultdict(list)
        
        # 调整策略
        self.adjustment_strategies = {
            'low_hit_rate': self._handle_low_hit_rate,
            'high_error_rate': self._handle_high_error_rate,
            'poor_confidence': self._handle_poor_confidence,
            'weight_imbalance': self._handle_weight_imbalance,
            'performance_degradation': self._handle_performance_degradation
        }
        
        # 冷却期（防止频繁调整）
        self.cooldown_period = config.get('cooldown_period', 3600)  # 1小时
        self.last_adjustment_time = {}
        
        self.logger.info("权重自动调整触发器初始化完成")
    
    def _load_trigger_conditions(self) -> List[TriggerCondition]:
        """加载触发条件"""
        default_conditions = [
            TriggerCondition("低命中率触发", "exact_hit_rate", "<", 0.05, 10, 5),
            TriggerCondition("高错误率触发", "error_rate", ">", 0.2, 5, 3),
            TriggerCondition("置信度不准确触发", "confidence_accuracy", "<", 0.6, 8, 4),
            TriggerCondition("权重失衡触发", "weight_variance", ">", 0.5, 5, 3),
            TriggerCondition("性能下降触发", "performance_trend", "<", -0.1, 15, 8)
        ]
        
        # 从配置加载自定义条件
        custom_conditions = self.config.get('trigger_conditions', [])
        for condition_config in custom_conditions:
            condition = TriggerCondition(**condition_config)
            default_conditions.append(condition)
        
        return default_conditions
    
    def start_monitoring(self, interval: int = 300):
        """
        开始监控和自动调整
        
        Args:
            interval: 监控间隔（秒）
        """
        if self.is_active:
            self.logger.warning("自动调整触发器已经在运行")
            return
        
        self.is_active = True
        self.trigger_thread = threading.Thread(
            target=self._monitoring_loop,
            args=(interval,),
            daemon=True
        )
        self.trigger_thread.start()
        
        self.logger.info(f"自动调整触发器已启动，监控间隔: {interval}秒")
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_active = False
        if self.trigger_thread:
            self.trigger_thread.join(timeout=10)
        
        self.logger.info("自动调整触发器已停止")
    
    def _monitoring_loop(self, interval: int):
        """监控循环"""
        while self.is_active:
            try:
                # 收集性能数据
                performance_data = self._collect_performance_data()
                
                # 更新性能缓存
                self._update_performance_cache(performance_data)
                
                # 检查触发条件
                triggered_conditions = self._check_trigger_conditions()
                
                # 执行调整动作
                for condition in triggered_conditions:
                    self._execute_adjustment(condition)
                
                # 等待下一次检查
                time.sleep(interval)
                
            except Exception as e:
                self.logger.error(f"监控循环出错: {e}")
                time.sleep(interval)
    
    def _collect_performance_data(self) -> Dict[str, float]:
        """收集性能数据"""
        performance_data = {}
        
        try:
            # 从融合预测器获取性能摘要
            summary = self.fusion_predictor.data_access.get_performance_summary(days=7)
            
            if summary:
                performance_data['exact_hit_rate'] = summary.get('exact_hit_rate', 0.0)
                performance_data['position_hit_rate'] = summary.get('position_hit_rate', 0.0)
                performance_data['top_10_hit_rate'] = summary.get('top_10_hit_rate', 0.0)
                performance_data['avg_overall_score'] = summary.get('avg_overall_score', 0.0)
            
            # 计算错误率
            recent_sessions = self.fusion_predictor.data_access.get_recent_sessions(limit=20)
            if recent_sessions:
                failed_sessions = sum(1 for s in recent_sessions if not s['success'])
                performance_data['error_rate'] = failed_sessions / len(recent_sessions)
            else:
                performance_data['error_rate'] = 0.0
            
            # 计算置信度准确性
            performance_data['confidence_accuracy'] = self._calculate_confidence_accuracy()
            
            # 计算权重方差
            current_weights = self.fusion_predictor.data_access.get_fusion_weights()
            if current_weights:
                weight_values = list(current_weights.values())
                performance_data['weight_variance'] = np.var(weight_values)
            else:
                performance_data['weight_variance'] = 0.0
            
            # 计算性能趋势
            performance_data['performance_trend'] = self._calculate_performance_trend()
            
        except Exception as e:
            self.logger.error(f"收集性能数据失败: {e}")
        
        return performance_data
    
    def _calculate_confidence_accuracy(self) -> float:
        """计算置信度准确性"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 获取最近的预测和评估结果
                cursor.execute('''
                    SELECT fp.confidence_level, pp.hit_type
                    FROM final_predictions fp
                    JOIN prediction_performance pp ON fp.issue = pp.issue
                    WHERE fp.created_at >= datetime('now', '-7 days')
                    AND fp.prediction_rank = 1
                ''')
                
                results = cursor.fetchall()
                
                if not results:
                    return 0.5
                
                # 计算置信度与实际准确性的一致性
                confidence_scores = []
                for confidence_level, hit_type in results:
                    # 转换置信度为数值
                    confidence_value = {'high': 0.8, 'medium': 0.5, 'low': 0.2}.get(confidence_level, 0.5)
                    
                    # 转换命中类型为准确性
                    accuracy = 1.0 if hit_type == 'exact' else 0.0
                    
                    # 计算一致性（1 - 误差）
                    consistency = 1.0 - abs(confidence_value - accuracy)
                    confidence_scores.append(consistency)
                
                return np.mean(confidence_scores)
                
        except Exception as e:
            self.logger.error(f"计算置信度准确性失败: {e}")
            return 0.5
    
    def _calculate_performance_trend(self) -> float:
        """计算性能趋势"""
        try:
            # 获取最近15天的每日性能
            daily_performance = []
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT 
                        DATE(evaluated_at) as date,
                        AVG(overall_score) as avg_score
                    FROM prediction_performance
                    WHERE evaluated_at >= datetime('now', '-15 days')
                    GROUP BY DATE(evaluated_at)
                    ORDER BY date
                ''')
                
                results = cursor.fetchall()
                daily_performance = [row[1] for row in results if row[1] is not None]
            
            if len(daily_performance) < 3:
                return 0.0
            
            # 计算线性趋势
            x = np.arange(len(daily_performance))
            y = np.array(daily_performance)
            
            # 简单线性回归
            slope = np.polyfit(x, y, 1)[0]
            
            return slope
            
        except Exception as e:
            self.logger.error(f"计算性能趋势失败: {e}")
            return 0.0
    
    def _update_performance_cache(self, performance_data: Dict[str, float]):
        """更新性能数据缓存"""
        timestamp = datetime.now()
        
        for metric_name, value in performance_data.items():
            self.performance_cache[metric_name].append({
                'value': value,
                'timestamp': timestamp
            })
            
            # 限制缓存大小
            if len(self.performance_cache[metric_name]) > 100:
                self.performance_cache[metric_name] = self.performance_cache[metric_name][-100:]
    
    def _check_trigger_conditions(self) -> List[TriggerCondition]:
        """检查触发条件"""
        triggered_conditions = []
        
        for condition in self.trigger_conditions:
            if not condition.enabled:
                continue
            
            if self._evaluate_trigger_condition(condition):
                triggered_conditions.append(condition)
        
        return triggered_conditions
    
    def _evaluate_trigger_condition(self, condition: TriggerCondition) -> bool:
        """评估触发条件"""
        try:
            # 获取指标数据
            metric_data = self.performance_cache.get(condition.metric_name, [])
            
            if len(metric_data) < condition.min_samples:
                return False
            
            # 取最近的数据点
            recent_data = metric_data[-condition.window_size:]
            values = [item['value'] for item in recent_data]
            
            if not values:
                return False
            
            # 计算统计值（使用平均值）
            avg_value = np.mean(values)
            
            # 评估条件
            if condition.condition == ">":
                return avg_value > condition.threshold
            elif condition.condition == "<":
                return avg_value < condition.threshold
            elif condition.condition == ">=":
                return avg_value >= condition.threshold
            elif condition.condition == "<=":
                return avg_value <= condition.threshold
            elif condition.condition == "==":
                return abs(avg_value - condition.threshold) < 1e-6
            elif condition.condition == "!=":
                return abs(avg_value - condition.threshold) >= 1e-6
            
            return False
            
        except Exception as e:
            self.logger.error(f"评估触发条件失败: {e}")
            return False
    
    def _execute_adjustment(self, condition: TriggerCondition):
        """执行调整动作"""
        try:
            # 检查冷却期
            if self._is_in_cooldown(condition.name):
                return
            
            # 根据条件名称选择调整策略
            strategy_name = condition.name.split('触发')[0].lower()
            
            if 'low_hit_rate' in strategy_name or '低命中率' in strategy_name:
                action = self._handle_low_hit_rate(condition)
            elif 'high_error_rate' in strategy_name or '高错误率' in strategy_name:
                action = self._handle_high_error_rate(condition)
            elif 'poor_confidence' in strategy_name or '置信度' in strategy_name:
                action = self._handle_poor_confidence(condition)
            elif 'weight_imbalance' in strategy_name or '权重失衡' in strategy_name:
                action = self._handle_weight_imbalance(condition)
            elif 'performance_degradation' in strategy_name or '性能下降' in strategy_name:
                action = self._handle_performance_degradation(condition)
            else:
                action = self._handle_generic_adjustment(condition)
            
            # 记录调整动作
            if action:
                self.adjustment_history.append(action)
                self.last_adjustment_time[condition.name] = datetime.now()
                
                self.logger.info(f"执行自动调整: {action.trigger_name}, 类型: {action.action_type}")
            
        except Exception as e:
            self.logger.error(f"执行调整动作失败: {e}")
    
    def _is_in_cooldown(self, condition_name: str) -> bool:
        """检查是否在冷却期内"""
        if condition_name not in self.last_adjustment_time:
            return False
        
        time_since_last = (datetime.now() - self.last_adjustment_time[condition_name]).total_seconds()
        return time_since_last < self.cooldown_period
    
    def _handle_low_hit_rate(self, condition: TriggerCondition) -> Optional[AdjustmentAction]:
        """处理低命中率"""
        try:
            # 增加约束权重，减少概率权重
            current_config = self.fusion_predictor.config
            
            new_constraint_weight = min(current_config['fusion']['constraint_weight'] * 1.2, 0.5)
            new_probability_weight = max(current_config['fusion']['probability_weight'] * 0.9, 0.3)
            
            # 更新配置
            self.fusion_predictor.config['fusion']['constraint_weight'] = new_constraint_weight
            self.fusion_predictor.config['fusion']['probability_weight'] = new_probability_weight
            
            action = AdjustmentAction(
                trigger_name=condition.name,
                action_type="weight_adjustment",
                parameters={
                    'constraint_weight': new_constraint_weight,
                    'probability_weight': new_probability_weight,
                    'reason': 'low_hit_rate'
                },
                timestamp=datetime.now(),
                success=True,
                details=f"增加约束权重到{new_constraint_weight:.3f}，减少概率权重到{new_probability_weight:.3f}"
            )
            
            return action
            
        except Exception as e:
            self.logger.error(f"处理低命中率失败: {e}")
            return None
    
    def _handle_high_error_rate(self, condition: TriggerCondition) -> Optional[AdjustmentAction]:
        """处理高错误率"""
        try:
            # 重置权重到默认值
            default_weights = {
                'hundreds': 1.0,
                'tens': 1.0,
                'units': 1.0,
                'sum': 0.8,
                'span': 0.6
            }
            
            # 更新权重
            self.fusion_predictor.data_access.save_fusion_weights(default_weights)
            
            action = AdjustmentAction(
                trigger_name=condition.name,
                action_type="weight_reset",
                parameters={'weights': default_weights, 'reason': 'high_error_rate'},
                timestamp=datetime.now(),
                success=True,
                details="重置权重到默认值以降低错误率"
            )
            
            return action
            
        except Exception as e:
            self.logger.error(f"处理高错误率失败: {e}")
            return None
    
    def _handle_poor_confidence(self, condition: TriggerCondition) -> Optional[AdjustmentAction]:
        """处理置信度不准确"""
        try:
            # 调整置信度阈值
            current_config = self.fusion_predictor.config
            
            new_threshold = max(current_config['weights']['confidence_threshold'] * 0.9, 0.3)
            self.fusion_predictor.config['weights']['confidence_threshold'] = new_threshold
            
            action = AdjustmentAction(
                trigger_name=condition.name,
                action_type="parameter_tuning",
                parameters={'confidence_threshold': new_threshold, 'reason': 'poor_confidence'},
                timestamp=datetime.now(),
                success=True,
                details=f"降低置信度阈值到{new_threshold:.3f}"
            )
            
            return action
            
        except Exception as e:
            self.logger.error(f"处理置信度不准确失败: {e}")
            return None
    
    def _handle_weight_imbalance(self, condition: TriggerCondition) -> Optional[AdjustmentAction]:
        """处理权重失衡"""
        try:
            # 平衡权重
            current_weights = self.fusion_predictor.data_access.get_fusion_weights()
            
            # 计算平均权重
            avg_weight = np.mean(list(current_weights.values()))
            
            # 调整权重向平均值靠拢
            balanced_weights = {}
            for name, weight in current_weights.items():
                balanced_weights[name] = weight * 0.8 + avg_weight * 0.2
            
            # 更新权重
            self.fusion_predictor.data_access.save_fusion_weights(balanced_weights)
            
            action = AdjustmentAction(
                trigger_name=condition.name,
                action_type="weight_balancing",
                parameters={'weights': balanced_weights, 'reason': 'weight_imbalance'},
                timestamp=datetime.now(),
                success=True,
                details="平衡权重分布以减少失衡"
            )
            
            return action
            
        except Exception as e:
            self.logger.error(f"处理权重失衡失败: {e}")
            return None
    
    def _handle_performance_degradation(self, condition: TriggerCondition) -> Optional[AdjustmentAction]:
        """处理性能下降"""
        try:
            # 触发参数优化
            optimization_result = self.fusion_predictor.optimize_fusion_parameters()
            
            action = AdjustmentAction(
                trigger_name=condition.name,
                action_type="parameter_optimization",
                parameters=optimization_result.get('optimized_config', {}),
                timestamp=datetime.now(),
                success='error' not in optimization_result,
                details="触发融合参数优化以改善性能下降"
            )
            
            return action
            
        except Exception as e:
            self.logger.error(f"处理性能下降失败: {e}")
            return None
    
    def _handle_generic_adjustment(self, condition: TriggerCondition) -> Optional[AdjustmentAction]:
        """处理通用调整"""
        try:
            # 通用的保守调整
            action = AdjustmentAction(
                trigger_name=condition.name,
                action_type="generic_adjustment",
                parameters={'condition': condition.name, 'reason': 'generic_trigger'},
                timestamp=datetime.now(),
                success=True,
                details=f"触发通用调整: {condition.name}"
            )
            
            return action
            
        except Exception as e:
            self.logger.error(f"处理通用调整失败: {e}")
            return None
    
    def get_adjustment_history(self, limit: int = 20) -> List[Dict[str, Any]]:
        """获取调整历史"""
        history = []
        
        for action in self.adjustment_history[-limit:]:
            history.append({
                'trigger_name': action.trigger_name,
                'action_type': action.action_type,
                'parameters': action.parameters,
                'timestamp': action.timestamp.isoformat(),
                'success': action.success,
                'details': action.details
            })
        
        return history
    
    def get_trigger_status(self) -> Dict[str, Any]:
        """获取触发器状态"""
        return {
            'is_active': self.is_active,
            'trigger_conditions_count': len(self.trigger_conditions),
            'enabled_conditions': len([c for c in self.trigger_conditions if c.enabled]),
            'adjustment_history_count': len(self.adjustment_history),
            'cooldown_period': self.cooldown_period,
            'last_adjustments': {name: time.isoformat() for name, time in self.last_adjustment_time.items()}
        }
