"""
P7跨度预测器数据访问层

实现完整的数据访问层，包括9个标准方法+专属方法
支持跨度预测的数据操作，包括双重约束和模式分析功能

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import sqlite3
import json
import logging
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
import numpy as np

class SpanDataAccess:
    """P7跨度预测器数据访问层"""
    
    def __init__(self, db_path: str):
        """
        初始化跨度数据访问层

        Args:
            db_path: 数据库文件路径
        """
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        self.position = 'span'
        
        # 跨度专属配置
        self.span_range = list(range(10))  # 0-9
        self.pattern_types = ['ascending', 'descending', 'same_digit', 'consecutive']
    
    # ==================== 标准方法实现 ====================
    
    def save_prediction_result(self, prediction_result: Dict[str, Any]) -> bool:
        """
        保存预测结果
        
        Args:
            prediction_result: 预测结果字典
            
        Returns:
            是否保存成功
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 准备数据
            issue = prediction_result.get('issue')
            model_type = prediction_result.get('model_type')
            predicted_digit = prediction_result.get('predicted_digit')
            confidence = prediction_result.get('confidence', 0.0)
            
            # 跨度专属字段
            probabilities = json.dumps(prediction_result.get('probabilities', []))
            prediction_range_min = prediction_result.get('prediction_range_min')
            prediction_range_max = prediction_result.get('prediction_range_max')
            classification_probs = json.dumps(prediction_result.get('classification_probs', []))
            pattern_analysis = json.dumps(prediction_result.get('pattern_analysis', {}))
            
            # 双重约束字段
            position_constraint_score = prediction_result.get('position_constraint_score', 0.0)
            sum_constraint_score = prediction_result.get('sum_constraint_score', 0.0)
            dual_constraint_score = prediction_result.get('dual_constraint_score', 0.0)
            
            # 插入数据
            cursor.execute('''
                INSERT OR REPLACE INTO span_predictions 
                (issue, model_type, predicted_digit, confidence, probabilities,
                 prediction_range_min, prediction_range_max, classification_probs, pattern_analysis,
                 position_constraint_score, sum_constraint_score, dual_constraint_score)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (issue, model_type, predicted_digit, confidence, probabilities,
                  prediction_range_min, prediction_range_max, classification_probs, pattern_analysis,
                  position_constraint_score, sum_constraint_score, dual_constraint_score))
            
            conn.commit()
            conn.close()
            
            self.logger.info(f"保存跨度预测结果: {issue} - {model_type}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存跨度预测结果失败: {e}")
            return False
    
    def get_prediction_history(self, model_type: Optional[str] = None, limit: int = 100) -> List[Dict]:
        """
        获取预测历史记录
        
        Args:
            model_type: 模型类型过滤
            limit: 返回记录数限制
            
        Returns:
            预测历史记录列表
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 构建查询
            if model_type:
                cursor.execute('''
                    SELECT * FROM span_predictions 
                    WHERE model_type = ? 
                    ORDER BY created_at DESC 
                    LIMIT ?
                ''', (model_type, limit))
            else:
                cursor.execute('''
                    SELECT * FROM span_predictions 
                    ORDER BY created_at DESC 
                    LIMIT ?
                ''', (limit,))
            
            columns = [description[0] for description in cursor.description]
            results = []
            
            for row in cursor.fetchall():
                record = dict(zip(columns, row))
                
                # 解析JSON字段
                if record.get('probabilities'):
                    try:
                        record['probabilities'] = json.loads(record['probabilities'])
                    except:
                        record['probabilities'] = []
                
                if record.get('classification_probs'):
                    try:
                        record['classification_probs'] = json.loads(record['classification_probs'])
                    except:
                        record['classification_probs'] = []
                
                if record.get('pattern_analysis'):
                    try:
                        record['pattern_analysis'] = json.loads(record['pattern_analysis'])
                    except:
                        record['pattern_analysis'] = {}
                
                results.append(record)
            
            conn.close()
            return results
            
        except Exception as e:
            self.logger.error(f"获取跨度预测历史失败: {e}")
            return []
    
    def save_performance_metrics(self, performance_data: Dict[str, Any]) -> bool:
        """
        保存性能指标
        
        Args:
            performance_data: 性能数据字典
            
        Returns:
            是否保存成功
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 准备数据
            model_type = performance_data.get('model_type')
            evaluation_period = performance_data.get('evaluation_period')
            accuracy = performance_data.get('accuracy', 0.0)
            
            # 回归专属指标
            mae = performance_data.get('mae', 0.0)
            rmse = performance_data.get('rmse', 0.0)
            accuracy_1 = performance_data.get('accuracy_1', 0.0)
            accuracy_2 = performance_data.get('accuracy_2', 0.0)
            r2_score = performance_data.get('r2_score', 0.0)
            
            # 分类专属指标
            classification_accuracy = performance_data.get('classification_accuracy')
            top_3_accuracy = performance_data.get('top_3_accuracy')
            f1_score = performance_data.get('f1_score')
            
            # 模式分析专属指标
            pattern_accuracy = performance_data.get('pattern_accuracy')
            ascending_pattern_acc = performance_data.get('ascending_pattern_acc')
            descending_pattern_acc = performance_data.get('descending_pattern_acc')
            same_digit_pattern_acc = performance_data.get('same_digit_pattern_acc')
            consecutive_pattern_acc = performance_data.get('consecutive_pattern_acc')
            
            # 双重约束指标
            dual_constraint_score = performance_data.get('dual_constraint_score')
            position_consistency = performance_data.get('position_consistency')
            sum_consistency = performance_data.get('sum_consistency')
            
            # 标准性能字段
            avg_confidence = performance_data.get('avg_confidence', 0.0)
            training_time = performance_data.get('training_time', 0.0)
            prediction_time = performance_data.get('prediction_time', 0.0)
            model_size = performance_data.get('model_size', 0)
            
            # 插入数据
            cursor.execute('''
                INSERT INTO span_model_performance 
                (model_type, evaluation_period, accuracy, mae, rmse, accuracy_1, accuracy_2, r2_score,
                 classification_accuracy, top_3_accuracy, f1_score,
                 pattern_accuracy, ascending_pattern_acc, descending_pattern_acc, 
                 same_digit_pattern_acc, consecutive_pattern_acc,
                 dual_constraint_score, position_consistency, sum_consistency,
                 avg_confidence, training_time, prediction_time, model_size)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (model_type, evaluation_period, accuracy, mae, rmse, accuracy_1, accuracy_2, r2_score,
                  classification_accuracy, top_3_accuracy, f1_score,
                  pattern_accuracy, ascending_pattern_acc, descending_pattern_acc,
                  same_digit_pattern_acc, consecutive_pattern_acc,
                  dual_constraint_score, position_consistency, sum_consistency,
                  avg_confidence, training_time, prediction_time, model_size))
            
            conn.commit()
            conn.close()
            
            self.logger.info(f"保存跨度性能指标: {model_type} - {evaluation_period}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存跨度性能指标失败: {e}")
            return False
    
    def get_performance_history(self, model_type: str, limit: int = 50) -> List[Dict]:
        """
        获取性能历史记录
        
        Args:
            model_type: 模型类型
            limit: 返回记录数限制
            
        Returns:
            性能历史记录列表
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT * FROM span_model_performance 
                WHERE model_type = ? 
                ORDER BY evaluated_at DESC 
                LIMIT ?
            ''', (model_type, limit))
            
            columns = [description[0] for description in cursor.description]
            results = [dict(zip(columns, row)) for row in cursor.fetchall()]
            
            conn.close()
            return results
            
        except Exception as e:
            self.logger.error(f"获取跨度性能历史失败: {e}")
            return []
    
    def get_latest_performance(self, model_type: str) -> Optional[Dict]:
        """
        获取最新性能指标
        
        Args:
            model_type: 模型类型
            
        Returns:
            最新性能指标字典
        """
        history = self.get_performance_history(model_type, limit=1)
        return history[0] if history else None
    
    def calculate_accuracy(self, y_true: np.ndarray, y_pred: np.ndarray, tolerance: int = 0) -> float:
        """
        计算准确率
        
        Args:
            y_true: 真实值
            y_pred: 预测值
            tolerance: 容差范围
            
        Returns:
            准确率
        """
        if len(y_true) == 0:
            return 0.0
        
        # 跨度范围约束
        y_pred_clipped = np.clip(y_pred, 0, 9)
        
        # 计算准确率
        correct = np.abs(y_true - y_pred_clipped) <= tolerance
        return float(np.mean(correct))

    def load_lottery_data(self, limit: Optional[int] = None) -> pd.DataFrame:
        """
        加载彩票历史数据并转换为标准格式

        Args:
            limit: 限制返回的记录数

        Returns:
            包含历史数据的DataFrame
        """
        try:
            conn = sqlite3.connect(self.db_path)

            # 构建查询语句
            if limit:
                query = """
                    SELECT period, numbers, date
                    FROM lottery_records
                    ORDER BY id DESC
                    LIMIT ?
                """
                df = pd.read_sql_query(query, conn, params=(limit,))
            else:
                query = """
                    SELECT period, numbers, date
                    FROM lottery_records
                    ORDER BY id DESC
                """
                df = pd.read_sql_query(query, conn)

            conn.close()

            if df.empty:
                self.logger.warning("数据库中没有彩票数据")
                return pd.DataFrame()

            # 转换数据格式
            df = self._convert_data_format(df)

            self.logger.info(f"加载彩票数据成功: {len(df)} 条记录")
            return df

        except Exception as e:
            self.logger.error(f"加载彩票数据失败: {e}")
            return pd.DataFrame()

    def _convert_data_format(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        转换数据格式以匹配预期的结构

        Args:
            df: 原始数据DataFrame

        Returns:
            转换后的DataFrame
        """
        try:
            # 重命名列
            df = df.rename(columns={'period': 'issue'})

            # 解析numbers字段 (如 "123" -> hundreds=1, tens=2, units=3)
            df['hundreds'] = df['numbers'].str[0].astype(int)
            df['tens'] = df['numbers'].str[1].astype(int)
            df['units'] = df['numbers'].str[2].astype(int)

            # 计算和值
            df['sum'] = df['hundreds'] + df['tens'] + df['units']

            # 计算跨度
            df['span'] = df[['hundreds', 'tens', 'units']].max(axis=1) - df[['hundreds', 'tens', 'units']].min(axis=1)

            # 保留需要的列
            result_df = df[['issue', 'hundreds', 'tens', 'units', 'sum', 'span', 'date']].copy()

            return result_df

        except Exception as e:
            self.logger.error(f"数据格式转换失败: {e}")
            return pd.DataFrame()

    def get_data_statistics(self) -> Dict[str, Any]:
        """
        获取数据统计信息
        
        Returns:
            数据统计字典
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            stats = {}
            
            # 基础统计
            cursor.execute('SELECT COUNT(*) FROM span_predictions')
            stats['total_predictions'] = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(DISTINCT model_type) FROM span_predictions')
            stats['model_count'] = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(DISTINCT issue) FROM span_predictions')
            stats['issue_count'] = cursor.fetchone()[0]
            
            # 跨度分布统计
            cursor.execute('''
                SELECT predicted_digit, COUNT(*) as count 
                FROM span_predictions 
                GROUP BY predicted_digit 
                ORDER BY predicted_digit
            ''')
            span_distribution = dict(cursor.fetchall())
            stats['span_distribution'] = span_distribution
            
            # 双重约束统计
            cursor.execute('''
                SELECT 
                    AVG(dual_constraint_score) as avg_dual_score,
                    AVG(position_constraint_score) as avg_position_score,
                    AVG(sum_constraint_score) as avg_sum_score
                FROM span_predictions 
                WHERE dual_constraint_score IS NOT NULL
            ''')
            constraint_stats = cursor.fetchone()
            if constraint_stats and constraint_stats[0] is not None:
                stats['constraint_stats'] = {
                    'avg_dual_score': constraint_stats[0],
                    'avg_position_score': constraint_stats[1],
                    'avg_sum_score': constraint_stats[2]
                }
            
            conn.close()
            return stats
            
        except Exception as e:
            self.logger.error(f"获取跨度数据统计失败: {e}")
            return {}
    
    def cleanup_old_data(self, days: int = 90) -> bool:
        """
        清理旧数据
        
        Args:
            days: 保留天数
            
        Returns:
            是否清理成功
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 清理旧预测记录
            cursor.execute('''
                DELETE FROM span_predictions 
                WHERE created_at < datetime('now', '-{} days')
            '''.format(days))
            
            deleted_predictions = cursor.rowcount
            
            # 清理旧性能记录
            cursor.execute('''
                DELETE FROM span_model_performance 
                WHERE evaluated_at < datetime('now', '-{} days')
            '''.format(days))
            
            deleted_performance = cursor.rowcount
            
            conn.commit()
            conn.close()
            
            self.logger.info(f"清理跨度旧数据: 预测记录{deleted_predictions}条, 性能记录{deleted_performance}条")
            return True
            
        except Exception as e:
            self.logger.error(f"清理跨度旧数据失败: {e}")
            return False

    # ==================== 跨度专属方法 ====================

    def save_span_distribution_stats(self, span_value: int, stats_data: Dict[str, Any]) -> bool:
        """
        保存跨度分布统计数据

        Args:
            span_value: 跨度值（0-9）
            stats_data: 统计数据字典

        Returns:
            是否保存成功
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 准备数据
            frequency = stats_data.get('frequency', 0)
            probability = stats_data.get('probability', 0.0)
            avg_sum = stats_data.get('avg_sum')
            common_patterns = json.dumps(stats_data.get('common_patterns', []))
            seasonal_frequency = json.dumps(stats_data.get('seasonal_frequency', {}))
            correlation_with_positions = json.dumps(stats_data.get('correlation_with_positions', {}))
            correlation_with_sum = json.dumps(stats_data.get('correlation_with_sum', {}))

            # 模式统计
            ascending_count = stats_data.get('ascending_count', 0)
            descending_count = stats_data.get('descending_count', 0)
            same_digit_count = stats_data.get('same_digit_count', 0)
            consecutive_count = stats_data.get('consecutive_count', 0)

            # 插入或更新数据
            cursor.execute('''
                INSERT OR REPLACE INTO span_distribution_stats
                (span_value, frequency, probability, avg_sum, common_patterns, seasonal_frequency,
                 correlation_with_positions, correlation_with_sum, ascending_count, descending_count,
                 same_digit_count, consecutive_count, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            ''', (span_value, frequency, probability, avg_sum, common_patterns, seasonal_frequency,
                  correlation_with_positions, correlation_with_sum, ascending_count, descending_count,
                  same_digit_count, consecutive_count))

            conn.commit()
            conn.close()

            self.logger.info(f"保存跨度分布统计: 跨度{span_value}")
            return True

        except Exception as e:
            self.logger.error(f"保存跨度分布统计失败: {e}")
            return False

    def get_span_distribution_stats(self, span_value: Optional[int] = None) -> List[Dict]:
        """
        获取跨度分布统计数据

        Args:
            span_value: 指定跨度值，None表示获取所有

        Returns:
            跨度分布统计列表
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            if span_value is not None:
                cursor.execute('SELECT * FROM span_distribution_stats WHERE span_value = ?', (span_value,))
            else:
                cursor.execute('SELECT * FROM span_distribution_stats ORDER BY span_value')

            columns = [description[0] for description in cursor.description]
            results = []

            for row in cursor.fetchall():
                record = dict(zip(columns, row))

                # 解析JSON字段
                for json_field in ['common_patterns', 'seasonal_frequency', 'correlation_with_positions', 'correlation_with_sum']:
                    if record.get(json_field):
                        try:
                            record[json_field] = json.loads(record[json_field])
                        except:
                            record[json_field] = {} if 'frequency' in json_field or 'correlation' in json_field else []

                results.append(record)

            conn.close()
            return results

        except Exception as e:
            self.logger.error(f"获取跨度分布统计失败: {e}")
            return []

    def save_constraint_rule(self, rule_data: Dict[str, Any]) -> bool:
        """
        保存约束规则

        Args:
            rule_data: 约束规则数据字典

        Returns:
            是否保存成功
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 准备数据
            rule_name = rule_data.get('rule_name')
            rule_description = rule_data.get('rule_description')
            rule_type = rule_data.get('rule_type')

            # 约束条件
            min_span = rule_data.get('min_span')
            max_span = rule_data.get('max_span')
            position_constraint = json.dumps(rule_data.get('position_constraint', {}))
            sum_constraint = json.dumps(rule_data.get('sum_constraint', {}))
            pattern_constraint = json.dumps(rule_data.get('pattern_constraint', {}))

            # 双重约束配置
            dual_constraint_config = json.dumps(rule_data.get('dual_constraint_config', {}))
            position_weight = rule_data.get('position_weight', 0.5)
            sum_weight = rule_data.get('sum_weight', 0.5)

            # 模式约束配置
            pattern_weights = json.dumps(rule_data.get('pattern_weights', {}))
            enable_pattern_constraint = rule_data.get('enable_pattern_constraint', False)

            # 规则配置
            weight = rule_data.get('weight', 1.0)
            priority = rule_data.get('priority', 1)
            is_active = rule_data.get('is_active', True)

            # 插入数据
            cursor.execute('''
                INSERT INTO span_constraint_rules
                (rule_name, rule_description, rule_type, min_span, max_span,
                 position_constraint, sum_constraint, pattern_constraint,
                 dual_constraint_config, position_weight, sum_weight,
                 pattern_weights, enable_pattern_constraint,
                 weight, priority, is_active, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            ''', (rule_name, rule_description, rule_type, min_span, max_span,
                  position_constraint, sum_constraint, pattern_constraint,
                  dual_constraint_config, position_weight, sum_weight,
                  pattern_weights, enable_pattern_constraint,
                  weight, priority, is_active))

            conn.commit()
            conn.close()

            self.logger.info(f"保存跨度约束规则: {rule_name}")
            return True

        except Exception as e:
            self.logger.error(f"保存跨度约束规则失败: {e}")
            return False

    def get_constraint_rules(self, rule_type: Optional[str] = None, active_only: bool = True) -> List[Dict]:
        """
        获取约束规则

        Args:
            rule_type: 规则类型过滤
            active_only: 是否只获取激活的规则

        Returns:
            约束规则列表
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 构建查询
            query = 'SELECT * FROM span_constraint_rules'
            params = []
            conditions = []

            if rule_type:
                conditions.append('rule_type = ?')
                params.append(rule_type)

            if active_only:
                conditions.append('is_active = 1')

            if conditions:
                query += ' WHERE ' + ' AND '.join(conditions)

            query += ' ORDER BY priority DESC, created_at DESC'

            cursor.execute(query, params)

            columns = [description[0] for description in cursor.description]
            results = []

            for row in cursor.fetchall():
                record = dict(zip(columns, row))

                # 解析JSON字段
                json_fields = ['position_constraint', 'sum_constraint', 'pattern_constraint',
                              'dual_constraint_config', 'pattern_weights']
                for json_field in json_fields:
                    if record.get(json_field):
                        try:
                            record[json_field] = json.loads(record[json_field])
                        except:
                            record[json_field] = {}

                results.append(record)

            conn.close()
            return results

        except Exception as e:
            self.logger.error(f"获取跨度约束规则失败: {e}")
            return []

    def analyze_span_patterns(self, hundreds: int, tens: int, units: int) -> Dict[str, Any]:
        """
        分析跨度模式

        Args:
            hundreds: 百位数字
            tens: 十位数字
            units: 个位数字

        Returns:
            模式分析结果字典
        """
        digits = [hundreds, tens, units]
        span = max(digits) - min(digits)

        analysis = {
            'span': span,
            'digits': digits,
            'patterns': {}
        }

        # 升序模式
        analysis['patterns']['ascending'] = digits == sorted(digits) and len(set(digits)) == 3

        # 降序模式
        analysis['patterns']['descending'] = digits == sorted(digits, reverse=True) and len(set(digits)) == 3

        # 相同数字模式
        analysis['patterns']['same_digit'] = len(set(digits)) == 1

        # 连续数字模式
        sorted_digits = sorted(digits)
        analysis['patterns']['consecutive'] = (
            len(set(digits)) == 3 and
            sorted_digits[1] - sorted_digits[0] == 1 and
            sorted_digits[2] - sorted_digits[1] == 1
        )

        # 其他模式
        analysis['patterns']['has_duplicate'] = len(set(digits)) < 3
        analysis['patterns']['all_even'] = all(d % 2 == 0 for d in digits)
        analysis['patterns']['all_odd'] = all(d % 2 == 1 for d in digits)

        return analysis

    def get_span_correlation_with_positions(self, limit: int = 1000) -> Dict[str, float]:
        """
        获取跨度与位置预测的相关性

        Args:
            limit: 分析的记录数限制

        Returns:
            相关性分析结果
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 获取历史数据
            cursor.execute('''
                SELECT l.hundreds, l.tens, l.units,
                       (MAX(l.hundreds, l.tens, l.units) - MIN(l.hundreds, l.tens, l.units)) as actual_span
                FROM lottery_data l
                ORDER BY l.id DESC
                LIMIT ?
            ''', (limit,))

            data = cursor.fetchall()
            conn.close()

            if len(data) < 10:
                return {}

            # 计算相关性
            correlations = {}

            # 与百位的相关性
            hundreds_values = [row[0] for row in data]
            span_values = [row[3] for row in data]

            if len(set(hundreds_values)) > 1 and len(set(span_values)) > 1:
                correlation = np.corrcoef(hundreds_values, span_values)[0, 1]
                correlations['hundreds_correlation'] = float(correlation) if not np.isnan(correlation) else 0.0

            # 与十位的相关性
            tens_values = [row[1] for row in data]
            if len(set(tens_values)) > 1:
                correlation = np.corrcoef(tens_values, span_values)[0, 1]
                correlations['tens_correlation'] = float(correlation) if not np.isnan(correlation) else 0.0

            # 与个位的相关性
            units_values = [row[2] for row in data]
            if len(set(units_values)) > 1:
                correlation = np.corrcoef(units_values, span_values)[0, 1]
                correlations['units_correlation'] = float(correlation) if not np.isnan(correlation) else 0.0

            return correlations

        except Exception as e:
            self.logger.error(f"获取跨度位置相关性失败: {e}")
            return {}

    def get_span_correlation_with_sum(self, limit: int = 1000) -> Dict[str, float]:
        """
        获取跨度与和值的相关性

        Args:
            limit: 分析的记录数限制

        Returns:
            相关性分析结果
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 获取历史数据
            cursor.execute('''
                SELECT (l.hundreds + l.tens + l.units) as sum_value,
                       (MAX(l.hundreds, l.tens, l.units) - MIN(l.hundreds, l.tens, l.units)) as actual_span
                FROM lottery_data l
                ORDER BY l.id DESC
                LIMIT ?
            ''', (limit,))

            data = cursor.fetchall()
            conn.close()

            if len(data) < 10:
                return {}

            # 计算相关性
            sum_values = [row[0] for row in data]
            span_values = [row[1] for row in data]

            if len(set(sum_values)) > 1 and len(set(span_values)) > 1:
                correlation = np.corrcoef(sum_values, span_values)[0, 1]
                return {
                    'sum_correlation': float(correlation) if not np.isnan(correlation) else 0.0,
                    'sample_count': len(data)
                }

            return {'sum_correlation': 0.0, 'sample_count': len(data)}

        except Exception as e:
            self.logger.error(f"获取跨度和值相关性失败: {e}")
            return {}
