# P3-百位预测器

## 📋 项目概述
**设计理念**: 🎯 **独立位置预测** - 百位作为完全独立的随机变量进行预测
**前置条件**：P2-特征工程系统完成
**核心目标**：专门预测百位数字0-9的概率分布
**预计时间**：1-2周
**技术基础**：基于P2的PredictorFeatureInterface和百位专用特征生成器

## 🎯 设计原则

### 独立性原则
- **完全独立**: 百位预测不依赖十位、个位的任何信息
- **专注优化**: 专门针对百位特征进行深度优化
- **简单可靠**: 避免复杂的关联性分析，确保预测稳定性
- **并行友好**: 支持与P4、P5并行开发和训练

### 随机性本质
- **符合彩票本质**: 每个位置理论上是独立的随机变量
- **概率论基础**: P(直选=ijk) = P(百位=i) × P(十位=j) × P(个位=k)
- **避免过拟合**: 不引入可能的噪音关联性

## 🔧 技术要求

### 预测目标
- **输入**：百位专用特征向量（基于P2系统）
- **输出**：0-9每个数字的概率分布 (shape: 10,)
- **约束**：概率和为1，每个概率在[0,1]区间

### 模型架构
- **XGBoost分类器**：主力模型，处理非线性关系
- **LightGBM分类器**：辅助模型，快速训练
- **LSTM分类器**：时序模型，捕获时间依赖
- **集成融合**：多模型加权融合

### 数据库设计（独立架构）
```sql
-- 百位预测结果表（独立设计）
CREATE TABLE hundreds_predictions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    issue TEXT NOT NULL,
    model_type TEXT NOT NULL,           -- xgb/lgb/lstm/ensemble
    prob_0 REAL NOT NULL,
    prob_1 REAL NOT NULL,
    prob_2 REAL NOT NULL,
    prob_3 REAL NOT NULL,
    prob_4 REAL NOT NULL,
    prob_5 REAL NOT NULL,
    prob_6 REAL NOT NULL,
    prob_7 REAL NOT NULL,
    prob_8 REAL NOT NULL,
    prob_9 REAL NOT NULL,
    predicted_digit INTEGER,            -- 最高概率的数字
    confidence REAL,                    -- 预测置信度
    feature_count INTEGER,              -- 使用的特征数量
    training_samples INTEGER,           -- 训练样本数
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(issue, model_type)
);

-- 百位模型性能表（独立监控）
CREATE TABLE hundreds_model_performance (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    model_type TEXT NOT NULL,
    evaluation_period TEXT NOT NULL,    -- 评估周期
    accuracy REAL NOT NULL,             -- 准确率
    top3_accuracy REAL NOT NULL,        -- Top3准确率
    avg_confidence REAL NOT NULL,       -- 平均置信度
    precision_per_digit TEXT,           -- 每个数字的精确率(JSON)
    recall_per_digit TEXT,              -- 每个数字的召回率(JSON)
    evaluated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 🏗️ 核心功能实现

### 1. 独立百位预测器基类（基于P2系统）
```python
import numpy as np
import pandas as pd
from abc import ABC, abstractmethod
from typing import Dict, List, Tuple, Optional
import logging
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import accuracy_score, classification_report
import xgboost as xgb
import lightgbm as lgb
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout
from tensorflow.keras.utils import to_categorical

# 导入P2系统组件
from src.interfaces.predictor_feature_interface import PredictorFeatureInterface, FeatureConfig
from src.data.cache_optimizer import CacheOptimizer, CacheConfig

class IndependentHundredsPredictor(ABC):
    """
    独立百位预测器基类

    设计理念：
    - 百位作为完全独立的随机变量进行预测
    - 不依赖十位、个位的任何信息
    - 专注于百位特征的深度优化
    - 基于P2系统的特征工程和缓存优化
    """

    def __init__(self, db_path: str):
        self.db_path = db_path
        self.model = None
        self.is_trained = False

        # 初始化P2系统组件
        self.feature_interface = PredictorFeatureInterface(db_path, "hundreds")

        # 配置缓存优化器
        cache_config = CacheConfig(
            memory_size=200,
            db_cache_enabled=True,
            db_cache_path="cache/hundreds_predictor_cache.db"
        )
        self.cache_optimizer = CacheOptimizer(cache_config)

        # 配置日志
        self.logger = logging.getLogger(f"{self.__class__.__name__}")

    @abstractmethod
    def build_model(self):
        """构建模型"""
        pass

    @abstractmethod
    def train(self, X: np.ndarray, y: np.ndarray):
        """训练模型"""
        pass

    @abstractmethod
    def predict_probability(self, X: np.ndarray) -> np.ndarray:
        """预测概率分布（独立预测，无依赖）"""
        pass
    
    def load_training_data(self, limit: Optional[int] = None) -> Tuple[np.ndarray, np.ndarray]:
        """
        基于P2系统加载百位训练数据（独立加载）

        Args:
            limit: 限制训练样本数量，None表示使用全部数据

        Returns:
            X: 特征矩阵
            y: 目标向量（百位数字）
        """
        try:
            # 使用P2系统的特征配置
            config = FeatureConfig(
                feature_types=['hundreds', 'common'],  # 只使用百位和通用特征
                window_size=20,
                lag_features=[1, 2, 3, 5, 7],
                feature_selection=True,
                normalization="standard"
            )

            # 获取历史期号
            import sqlite3
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            query = "SELECT issue FROM lottery_data ORDER BY issue"
            if limit:
                query += f" LIMIT {limit}"

            cursor.execute(query)
            issues = [row[0] for row in cursor.fetchall()]
            conn.close()

            self.logger.info(f"加载 {len(issues)} 期数据进行训练")

            # 使用P2系统创建ML数据集
            dataset = self.feature_interface.create_ml_pipeline(issues, config)

            self.logger.info(f"特征矩阵形状: {dataset.X.shape}")
            self.logger.info(f"目标向量形状: {dataset.y.shape}")
            self.logger.info(f"特征数量: {len(dataset.feature_names)}")

            return dataset.X.values, dataset.y.values

        except Exception as e:
            self.logger.error(f"加载训练数据失败: {e}")
            raise
        """)
        feature_names_row = cursor.fetchone()
        if feature_names_row:
            self.feature_names = json.loads(feature_names_row[0])
        
        conn.close()
        
        return np.array(X), np.array(y)
    
    def evaluate_model(self, X_test: np.ndarray, y_test: np.ndarray) -> Dict:
        """评估模型性能"""
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        # 预测概率
        y_prob = self.predict_probability(X_test)
        y_pred = np.argmax(y_prob, axis=1)
        
        # 计算准确率
        accuracy = accuracy_score(y_test, y_pred)
        
        # 计算Top3准确率
        top3_accuracy = np.mean([
            y_test[i] in np.argsort(y_prob[i])[-3:] 
            for i in range(len(y_test))
        ])
        
        # 计算平均置信度
        avg_confidence = np.mean(np.max(y_prob, axis=1))
        
        # 计算每个数字的精确率和召回率
        report = classification_report(y_test, y_pred, output_dict=True, zero_division=0)
        
        precision_per_digit = {str(i): report.get(str(i), {}).get('precision', 0) for i in range(10)}
        recall_per_digit = {str(i): report.get(str(i), {}).get('recall', 0) for i in range(10)}
        
        return {
            'accuracy': accuracy,
            'top3_accuracy': top3_accuracy,
            'avg_confidence': avg_confidence,
            'precision_per_digit': precision_per_digit,
            'recall_per_digit': recall_per_digit
        }
    
    def save_prediction(self, issue: str, probabilities: np.ndarray, model_type: str):
        """保存预测结果"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        predicted_digit = int(np.argmax(probabilities))
        confidence = float(np.max(probabilities))
        
        cursor.execute("""
            INSERT OR REPLACE INTO hundreds_predictions 
            (issue, model_type, prob_0, prob_1, prob_2, prob_3, prob_4, 
             prob_5, prob_6, prob_7, prob_8, prob_9, predicted_digit, confidence)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (issue, model_type, *probabilities.tolist(), predicted_digit, confidence))
        
        conn.commit()
        conn.close()
```

### 2. XGBoost百位预测器
```python
class XGBHundredsPredictor(BaseHundredsPredictor):
    def __init__(self, db_path: str):
        super().__init__(db_path)
        self.model_params = {
            'objective': 'multi:softprob',
            'num_class': 10,
            'max_depth': 6,
            'learning_rate': 0.1,
            'subsample': 0.8,
            'colsample_bytree': 0.8,
            'reg_alpha': 0.1,
            'reg_lambda': 1.0,
            'n_estimators': 200,
            'random_state': 42,
            'n_jobs': -1
        }
    
    def build_model(self):
        """构建XGBoost模型"""
        self.model = xgb.XGBClassifier(**self.model_params)
        return self.model
    
    def train(self, X: np.ndarray, y: np.ndarray):
        """训练XGBoost模型"""
        if self.model is None:
            self.build_model()
        
        # 分割训练集和验证集
        X_train, X_val, y_train, y_val = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        # 训练模型
        self.model.fit(
            X_train, y_train,
            eval_set=[(X_val, y_val)],
            early_stopping_rounds=20,
            verbose=False
        )
        
        self.is_trained = True
        
        # 评估模型
        val_performance = self.evaluate_model(X_val, y_val)
        print(f"XGBoost百位预测器验证集性能: {val_performance}")
        
        return val_performance
    
    def predict_probability(self, X: np.ndarray) -> np.ndarray:
        """预测概率分布"""
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        probabilities = self.model.predict_proba(X)
        return probabilities
    
    def get_feature_importance(self) -> Dict[str, float]:
        """获取特征重要性"""
        if not self.is_trained or self.feature_names is None:
            return {}
        
        importance_scores = self.model.feature_importances_
        return dict(zip(self.feature_names, importance_scores))
```

### 3. LightGBM百位预测器
```python
class LGBHundredsPredictor(BaseHundredsPredictor):
    def __init__(self, db_path: str):
        super().__init__(db_path)
        self.model_params = {
            'objective': 'multiclass',
            'num_class': 10,
            'metric': 'multi_logloss',
            'boosting_type': 'gbdt',
            'num_leaves': 31,
            'learning_rate': 0.05,
            'feature_fraction': 0.9,
            'bagging_fraction': 0.8,
            'bagging_freq': 5,
            'n_estimators': 200,
            'random_state': 42,
            'verbose': -1
        }
    
    def build_model(self):
        """构建LightGBM模型"""
        self.model = lgb.LGBMClassifier(**self.model_params)
        return self.model
    
    def train(self, X: np.ndarray, y: np.ndarray):
        """训练LightGBM模型"""
        if self.model is None:
            self.build_model()
        
        X_train, X_val, y_train, y_val = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        self.model.fit(
            X_train, y_train,
            eval_set=[(X_val, y_val)],
            callbacks=[lgb.early_stopping(20), lgb.log_evaluation(0)]
        )
        
        self.is_trained = True
        
        val_performance = self.evaluate_model(X_val, y_val)
        print(f"LightGBM百位预测器验证集性能: {val_performance}")
        
        return val_performance
    
    def predict_probability(self, X: np.ndarray) -> np.ndarray:
        """预测概率分布"""
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        probabilities = self.model.predict_proba(X)
        return probabilities
```

### 4. LSTM百位预测器
```python
class LSTMHundredsPredictor(BaseHundredsPredictor):
    def __init__(self, db_path: str, sequence_length: int = 30):
        super().__init__(db_path)
        self.sequence_length = sequence_length
        self.model_params = {
            'lstm_units': 64,
            'dropout_rate': 0.2,
            'dense_units': 32,
            'epochs': 100,
            'batch_size': 32,
            'validation_split': 0.2
        }
    
    def build_model(self, input_shape: Tuple[int, int]):
        """构建LSTM模型"""
        model = Sequential([
            LSTM(self.model_params['lstm_units'], 
                 return_sequences=True, 
                 input_shape=input_shape),
            Dropout(self.model_params['dropout_rate']),
            LSTM(self.model_params['lstm_units'] // 2),
            Dropout(self.model_params['dropout_rate']),
            Dense(self.model_params['dense_units'], activation='relu'),
            Dense(10, activation='softmax')  # 10个数字的概率分布
        ])
        
        model.compile(
            optimizer='adam',
            loss='categorical_crossentropy',
            metrics=['accuracy']
        )
        
        self.model = model
        return model
    
    def prepare_sequence_data(self, X: np.ndarray, y: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """准备序列数据"""
        X_seq = []
        y_seq = []
        
        for i in range(self.sequence_length, len(X)):
            X_seq.append(X[i-self.sequence_length:i])
            y_seq.append(y[i])
        
        X_seq = np.array(X_seq)
        y_seq = to_categorical(np.array(y_seq), num_classes=10)
        
        return X_seq, y_seq
    
    def train(self, X: np.ndarray, y: np.ndarray):
        """训练LSTM模型"""
        # 准备序列数据
        X_seq, y_seq = self.prepare_sequence_data(X, y)
        
        if self.model is None:
            input_shape = (X_seq.shape[1], X_seq.shape[2])
            self.build_model(input_shape)
        
        # 训练模型
        history = self.model.fit(
            X_seq, y_seq,
            epochs=self.model_params['epochs'],
            batch_size=self.model_params['batch_size'],
            validation_split=self.model_params['validation_split'],
            verbose=0
        )
        
        self.is_trained = True
        
        # 评估模型
        val_loss, val_accuracy = self.model.evaluate(
            X_seq[-int(len(X_seq) * 0.2):], 
            y_seq[-int(len(y_seq) * 0.2):], 
            verbose=0
        )
        
        print(f"LSTM百位预测器验证集准确率: {val_accuracy:.4f}")
        
        return {'accuracy': val_accuracy, 'loss': val_loss}
    
    def predict_probability(self, X: np.ndarray) -> np.ndarray:
        """预测概率分布"""
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        # 如果输入是单个样本，需要扩展为序列
        if len(X.shape) == 1:
            X = X.reshape(1, -1)
        
        # 取最后sequence_length个样本作为输入序列
        if X.shape[0] >= self.sequence_length:
            X_seq = X[-self.sequence_length:].reshape(1, self.sequence_length, -1)
        else:
            # 如果样本不够，用零填充
            padding = np.zeros((self.sequence_length - X.shape[0], X.shape[1]))
            X_padded = np.vstack([padding, X])
            X_seq = X_padded.reshape(1, self.sequence_length, -1)
        
        probabilities = self.model.predict(X_seq, verbose=0)
        return probabilities
```

### 5. 集成百位预测器
```python
class EnsembleHundredsPredictor:
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.xgb_predictor = XGBHundredsPredictor(db_path)
        self.lgb_predictor = LGBHundredsPredictor(db_path)
        self.lstm_predictor = LSTMHundredsPredictor(db_path)
        
        # 初始权重
        self.weights = {
            'xgb': 0.4,
            'lgb': 0.4,
            'lstm': 0.2
        }
        
        self.is_trained = False
    
    def train_all_models(self):
        """训练所有模型"""
        # 加载数据
        X, y = self.xgb_predictor.load_data()
        
        print("训练XGBoost百位预测器...")
        xgb_performance = self.xgb_predictor.train(X, y)
        
        print("训练LightGBM百位预测器...")
        lgb_performance = self.lgb_predictor.train(X, y)
        
        print("训练LSTM百位预测器...")
        lstm_performance = self.lstm_predictor.train(X, y)
        
        # 根据性能调整权重
        self.adjust_weights(xgb_performance, lgb_performance, lstm_performance)
        
        self.is_trained = True
        
        return {
            'xgb': xgb_performance,
            'lgb': lgb_performance,
            'lstm': lstm_performance,
            'weights': self.weights
        }
    
    def adjust_weights(self, xgb_perf: Dict, lgb_perf: Dict, lstm_perf: Dict):
        """根据性能调整权重"""
        xgb_acc = xgb_perf.get('accuracy', 0)
        lgb_acc = lgb_perf.get('accuracy', 0)
        lstm_acc = lstm_perf.get('accuracy', 0)
        
        total_acc = xgb_acc + lgb_acc + lstm_acc
        
        if total_acc > 0:
            self.weights['xgb'] = xgb_acc / total_acc
            self.weights['lgb'] = lgb_acc / total_acc
            self.weights['lstm'] = lstm_acc / total_acc
        
        print(f"调整后的权重: {self.weights}")
    
    def predict_probability(self, X: np.ndarray) -> np.ndarray:
        """集成预测概率分布"""
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        # 获取各模型预测
        xgb_prob = self.xgb_predictor.predict_probability(X)
        lgb_prob = self.lgb_predictor.predict_probability(X)
        lstm_prob = self.lstm_predictor.predict_probability(X)
        
        # 加权融合
        ensemble_prob = (
            self.weights['xgb'] * xgb_prob +
            self.weights['lgb'] * lgb_prob +
            self.weights['lstm'] * lstm_prob
        )
        
        # 确保概率和为1
        ensemble_prob = ensemble_prob / np.sum(ensemble_prob, axis=1, keepdims=True)
        
        return ensemble_prob
    
    def predict_next_period(self, issue: str) -> Dict:
        """预测下一期百位"""
        # 获取最新特征
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT feature_vector FROM feature_data 
            WHERE feature_type = 'hundreds' 
            ORDER BY issue DESC 
            LIMIT 1
        """)
        
        row = cursor.fetchone()
        if not row:
            raise ValueError("没有找到最新的百位特征数据")
        
        latest_features = np.array(json.loads(row[0])).reshape(1, -1)
        
        # 预测概率
        probabilities = self.predict_probability(latest_features)[0]
        
        # 保存预测结果
        self.xgb_predictor.save_prediction(issue, probabilities, 'ensemble')
        
        conn.close()
        
        return {
            'issue': issue,
            'probabilities': probabilities.tolist(),
            'predicted_digit': int(np.argmax(probabilities)),
            'confidence': float(np.max(probabilities)),
            'top3_digits': np.argsort(probabilities)[-3:].tolist()[::-1]
        }
```

## 成功标准

### 模型性能
- [ ] XGBoost准确率 > 35%
- [ ] LightGBM准确率 > 35%
- [ ] LSTM准确率 > 30%
- [ ] 集成模型准确率 > 40%

### 预测质量
- [ ] Top3准确率 > 70%
- [ ] 平均置信度 > 0.15
- [ ] 概率分布合理（和为1）

### 系统稳定性
- [ ] 模型训练成功率 100%
- [ ] 预测生成时间 < 5秒
- [ ] 预测结果保存成功率 100%

## 部署说明

```python
# 使用示例
predictor = EnsembleHundredsPredictor("data/lottery.db")

# 训练所有模型
performance = predictor.train_all_models()
print(f"训练完成，性能: {performance}")

# 预测下一期
next_issue = "2024001"
prediction = predictor.predict_next_period(next_issue)
print(f"百位预测结果: {prediction}")
```

## 下一步
完成P3后，进入**P4-十位预测器**开发阶段。
