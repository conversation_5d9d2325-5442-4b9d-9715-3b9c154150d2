-- P7跨度预测器数据库表创建脚本
-- 创建日期: 2025-01-14
-- 描述: 为P7跨度预测器创建4个专用数据库表

-- 跨度预测结果表（标准表结构+专属字段）
CREATE TABLE IF NOT EXISTS span_predictions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    issue TEXT NOT NULL,
    model_type TEXT NOT NULL,           -- xgb/lgb/lstm/classification/constraint/ensemble
    predicted_digit REAL NOT NULL,     -- 统一字段名：预测跨度（0-9）
    confidence REAL NOT NULL,          -- 预测置信度
    probabilities TEXT,                 -- JSON格式：跨度概率分布（专属）

    -- 跨度专属字段
    prediction_range_min INTEGER,      -- 预测范围最小值
    prediction_range_max INTEGER,      -- 预测范围最大值
    classification_probs TEXT,         -- 分类概率分布（专属特征）
    pattern_analysis TEXT,             -- 模式分析结果（专属特征）
    
    -- 双重约束字段
    position_constraint_score REAL,    -- 位置约束分数
    sum_constraint_score REAL,         -- 和值约束分数
    dual_constraint_score REAL,        -- 双重约束综合分数

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 索引优化
    UNIQUE(issue, model_type)
);

-- 跨度模型性能表（标准表结构+专属指标）
CREATE TABLE IF NOT EXISTS span_model_performance (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    model_type TEXT NOT NULL,
    evaluation_period TEXT NOT NULL,
    accuracy REAL NOT NULL,             -- 标准字段：主要准确率指标

    -- 回归专属性能指标
    mae REAL NOT NULL,                  -- 平均绝对误差（专属）
    rmse REAL NOT NULL,                 -- 均方根误差（专属）
    accuracy_1 REAL NOT NULL,           -- ±1准确率（专属）
    accuracy_2 REAL NOT NULL,           -- ±2准确率（专属）
    r2_score REAL NOT NULL,             -- R²分数（专属）
    
    -- 分类专属性能指标
    classification_accuracy REAL,       -- 分类准确率（专属）
    top_3_accuracy REAL,                -- Top-3准确率（专属）
    f1_score REAL,                      -- F1分数（专属）
    
    -- 模式分析专属指标
    pattern_accuracy REAL,              -- 模式分析准确率（专属）
    ascending_pattern_acc REAL,         -- 升序模式准确率
    descending_pattern_acc REAL,        -- 降序模式准确率
    same_digit_pattern_acc REAL,        -- 相同数字模式准确率
    consecutive_pattern_acc REAL,       -- 连续数字模式准确率
    
    -- 双重约束性能指标
    dual_constraint_score REAL,         -- 双重约束分数（专属）
    position_consistency REAL,          -- 位置一致性分数
    sum_consistency REAL,               -- 和值一致性分数

    -- 标准性能字段
    avg_confidence REAL,                -- 平均置信度
    training_time REAL,                 -- 训练时间
    prediction_time REAL,               -- 预测时间
    model_size INTEGER,                 -- 模型大小

    evaluated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 跨度分布统计表（专属特征表）
CREATE TABLE IF NOT EXISTS span_distribution_stats (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    span_value INTEGER NOT NULL,        -- 跨度值（0-9）
    frequency INTEGER NOT NULL,         -- 出现频次
    probability REAL NOT NULL,          -- 概率
    avg_sum REAL,                       -- 该跨度的平均和值
    common_patterns TEXT,               -- 常见模式(JSON)
    seasonal_frequency TEXT,            -- 季节性频次(JSON)
    correlation_with_positions TEXT,    -- 与位置预测的相关性(JSON)
    correlation_with_sum TEXT,          -- 与和值预测的相关性(JSON)
    
    -- 模式统计
    ascending_count INTEGER DEFAULT 0,  -- 升序模式次数
    descending_count INTEGER DEFAULT 0, -- 降序模式次数
    same_digit_count INTEGER DEFAULT 0, -- 相同数字次数
    consecutive_count INTEGER DEFAULT 0,-- 连续数字次数
    
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 索引和约束
    UNIQUE(span_value),
    CHECK(span_value >= 0 AND span_value <= 9),
    CHECK(probability >= 0 AND probability <= 1)
);

-- 跨度约束规则表（专属特征表）
CREATE TABLE IF NOT EXISTS span_constraint_rules (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    rule_name TEXT NOT NULL,
    rule_description TEXT NOT NULL,
    rule_type TEXT NOT NULL,            -- 规则类型：position/sum/span/pattern/dual

    -- 约束条件
    min_span INTEGER,                   -- 最小跨度
    max_span INTEGER,                   -- 最大跨度
    position_constraint TEXT,           -- 位置约束条件(JSON)
    sum_constraint TEXT,                -- 和值约束条件(JSON)
    pattern_constraint TEXT,            -- 模式约束条件(JSON)
    
    -- 双重约束配置
    dual_constraint_config TEXT,        -- 双重约束配置(JSON)
    position_weight REAL DEFAULT 0.5,   -- 位置约束权重
    sum_weight REAL DEFAULT 0.5,        -- 和值约束权重
    
    -- 模式约束配置
    pattern_weights TEXT,               -- 模式权重配置(JSON)
    enable_pattern_constraint BOOLEAN DEFAULT FALSE,

    -- 规则配置
    weight REAL DEFAULT 1.0,            -- 规则权重
    priority INTEGER DEFAULT 1,         -- 规则优先级
    is_active BOOLEAN DEFAULT TRUE,     -- 是否启用

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 约束检查
    CHECK(weight >= 0 AND weight <= 1),
    CHECK(priority >= 1),
    CHECK(min_span >= 0 AND max_span <= 9),
    CHECK(min_span <= max_span),
    CHECK(position_weight >= 0 AND position_weight <= 1),
    CHECK(sum_weight >= 0 AND sum_weight <= 1),
    CHECK(position_weight + sum_weight <= 1.1)  -- 允许小误差
);

-- 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_span_predictions_issue ON span_predictions(issue);
CREATE INDEX IF NOT EXISTS idx_span_predictions_model_type ON span_predictions(model_type);
CREATE INDEX IF NOT EXISTS idx_span_predictions_created_at ON span_predictions(created_at);
CREATE INDEX IF NOT EXISTS idx_span_predictions_dual_score ON span_predictions(dual_constraint_score);

CREATE INDEX IF NOT EXISTS idx_span_performance_model_type ON span_model_performance(model_type);
CREATE INDEX IF NOT EXISTS idx_span_performance_period ON span_model_performance(evaluation_period);
CREATE INDEX IF NOT EXISTS idx_span_performance_dual_score ON span_model_performance(dual_constraint_score);

CREATE INDEX IF NOT EXISTS idx_span_distribution_value ON span_distribution_stats(span_value);
CREATE INDEX IF NOT EXISTS idx_span_distribution_probability ON span_distribution_stats(probability DESC);
CREATE INDEX IF NOT EXISTS idx_span_distribution_patterns ON span_distribution_stats(ascending_count, descending_count);

CREATE INDEX IF NOT EXISTS idx_span_rules_type ON span_constraint_rules(rule_type);
CREATE INDEX IF NOT EXISTS idx_span_rules_active ON span_constraint_rules(is_active);
CREATE INDEX IF NOT EXISTS idx_span_rules_priority ON span_constraint_rules(priority DESC);
CREATE INDEX IF NOT EXISTS idx_span_rules_dual ON span_constraint_rules(position_weight, sum_weight);
