# P5-个位预测器

## 📋 项目概述
**设计理念**: 🎯 **独立位置预测** - 个位作为完全独立的随机变量进行预测
**前置条件**：P2-特征工程系统完成
**核心目标**：专门预测个位数字0-9的概率分布
**预计时间**：1-2周
**技术基础**：基于P2的PredictorFeatureInterface和个位专用特征生成器

## 🎯 设计原则

### 独立性原则
- **完全独立**: 个位预测不依赖百位、十位的任何信息
- **专注优化**: 专门针对个位特征进行深度优化
- **简单可靠**: 避免复杂的关联性分析，确保预测稳定性
- **并行友好**: 支持与P3、P4并行开发和训练

### 随机性本质
- **符合彩票本质**: 每个位置理论上是独立的随机变量
- **避免噪音干扰**: 不引入可能的虚假关联性
- **专注本位**: 深度挖掘个位自身的统计规律

## 🔧 技术要求

### 预测目标
- **输入**：个位专用特征向量（基于P2系统）
- **输出**：0-9每个数字的概率分布 (shape: 10,)
- **约束**：概率和为1，每个概率在[0,1]区间

### 模型架构
- **XGBoost分类器**：主力模型，处理非线性关系
- **LightGBM分类器**：辅助模型，快速训练
- **LSTM分类器**：时序模型，捕获时间依赖
- **集成融合**：多模型加权融合

### 数据库设计（独立架构）
```sql
-- 个位预测结果表（独立设计）
CREATE TABLE units_predictions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    issue TEXT NOT NULL,
    model_type TEXT NOT NULL,           -- xgb/lgb/lstm/ensemble
    prob_0 REAL NOT NULL,
    prob_1 REAL NOT NULL,
    prob_2 REAL NOT NULL,
    prob_3 REAL NOT NULL,
    prob_4 REAL NOT NULL,
    prob_5 REAL NOT NULL,
    prob_6 REAL NOT NULL,
    prob_7 REAL NOT NULL,
    prob_8 REAL NOT NULL,
    prob_9 REAL NOT NULL,
    predicted_digit INTEGER,            -- 最高概率的数字
    confidence REAL,                    -- 预测置信度
    hundreds_correlation REAL,          -- 与百位的关联度
    tens_correlation REAL,              -- 与十位的关联度
    sequence_pattern TEXT,              -- 序列模式(ascending/descending/random)
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 个位模型性能表
CREATE TABLE units_model_performance (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    model_type TEXT NOT NULL,
    evaluation_period TEXT NOT NULL,
    accuracy REAL NOT NULL,
    top3_accuracy REAL NOT NULL,
    avg_confidence REAL NOT NULL,
    correlation_accuracy REAL,          -- 关联预测准确率
    sequence_accuracy REAL,             -- 序列模式准确率
    precision_per_digit TEXT,
    recall_per_digit TEXT,
    evaluated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 位置关联模式表
CREATE TABLE position_correlation_patterns (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    hundreds_digit INTEGER,
    tens_digit INTEGER,
    units_digit INTEGER,
    frequency INTEGER NOT NULL,
    probability REAL NOT NULL,
    pattern_type TEXT,                  -- triplet/hundreds_units/tens_units
    last_appearance TEXT,
    avg_interval REAL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 序列模式表
CREATE TABLE sequence_patterns (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    pattern_type TEXT NOT NULL,         -- ascending/descending/same/mixed
    pattern_description TEXT,           -- 具体模式描述
    frequency INTEGER NOT NULL,
    probability REAL NOT NULL,
    avg_sum_value REAL,                 -- 该模式的平均和值
    avg_span_value REAL,                -- 该模式的平均跨度
    last_appearance TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 核心功能实现

### 1. 个位预测器基类
```python
import numpy as np
import pandas as pd
from abc import ABC, abstractmethod
from typing import Dict, List, Tuple, Optional
import sqlite3
import json
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report
import xgboost as xgb
import lightgbm as lgb
from tensorflow.keras.models import Sequential, Model
from tensorflow.keras.layers import LSTM, Dense, Dropout, Input, concatenate
from tensorflow.keras.utils import to_categorical

class BaseUnitsPredictor(ABC):
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.model = None
        self.feature_names = None
        self.is_trained = False
        self.hundreds_units_correlation = None
        self.tens_units_correlation = None
        self.triplet_patterns = None
        self.sequence_patterns = None
        
    @abstractmethod
    def build_model(self):
        """构建模型"""
        pass
    
    @abstractmethod
    def train(self, X: np.ndarray, y: np.ndarray, 
              hundreds_data: Optional[np.ndarray] = None,
              tens_data: Optional[np.ndarray] = None):
        """训练模型"""
        pass
    
    @abstractmethod
    def predict_probability(self, X: np.ndarray, 
                          hundreds_prob: Optional[np.ndarray] = None,
                          tens_prob: Optional[np.ndarray] = None) -> np.ndarray:
        """预测概率分布"""
        pass
    
    def load_data(self) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
        """加载个位训练数据"""
        conn = sqlite3.connect(self.db_path)
        
        feature_query = """
            SELECT fd.feature_vector, ld.units, ld.hundreds, ld.tens
            FROM feature_data fd
            JOIN lottery_data ld ON fd.issue = ld.issue
            WHERE fd.feature_type = 'units'
            ORDER BY fd.issue
        """
        
        cursor = conn.cursor()
        cursor.execute(feature_query)
        rows = cursor.fetchall()
        
        if not rows:
            raise ValueError("没有找到个位特征数据")
        
        X = []
        y = []
        hundreds_data = []
        tens_data = []
        
        for row in rows:
            feature_vector = json.loads(row[0])
            units_target = row[1]
            hundreds_value = row[2]
            tens_value = row[3]
            
            X.append(feature_vector)
            y.append(units_target)
            hundreds_data.append(hundreds_value)
            tens_data.append(tens_value)
        
        # 获取特征名称
        cursor.execute("""
            SELECT feature_names FROM feature_data 
            WHERE feature_type = 'units' 
            LIMIT 1
        """)
        feature_names_row = cursor.fetchone()
        if feature_names_row:
            self.feature_names = json.loads(feature_names_row[0])
        
        conn.close()
        
        return np.array(X), np.array(y), np.array(hundreds_data), np.array(tens_data)
    
    def build_correlation_matrices(self):
        """构建多重关联矩阵"""
        conn = sqlite3.connect(self.db_path)
        
        # 1. 百位-个位关联矩阵
        query_hu = """
            SELECT hundreds, units, COUNT(*) as frequency
            FROM lottery_data
            GROUP BY hundreds, units
            ORDER BY hundreds, units
        """
        df_hu = pd.read_sql_query(query_hu, conn)
        
        self.hundreds_units_correlation = np.zeros((10, 10))
        for _, row in df_hu.iterrows():
            h, u, freq = row['hundreds'], row['units'], row['frequency']
            self.hundreds_units_correlation[h][u] = freq
        
        # 转换为概率矩阵
        row_sums = self.hundreds_units_correlation.sum(axis=1, keepdims=True)
        row_sums[row_sums == 0] = 1
        self.hundreds_units_correlation = self.hundreds_units_correlation / row_sums
        
        # 2. 十位-个位关联矩阵
        query_tu = """
            SELECT tens, units, COUNT(*) as frequency
            FROM lottery_data
            GROUP BY tens, units
            ORDER BY tens, units
        """
        df_tu = pd.read_sql_query(query_tu, conn)
        
        self.tens_units_correlation = np.zeros((10, 10))
        for _, row in df_tu.iterrows():
            t, u, freq = row['tens'], row['units'], row['frequency']
            self.tens_units_correlation[t][u] = freq
        
        row_sums = self.tens_units_correlation.sum(axis=1, keepdims=True)
        row_sums[row_sums == 0] = 1
        self.tens_units_correlation = self.tens_units_correlation / row_sums
        
        # 3. 三位数组合模式
        query_triplet = """
            SELECT hundreds, tens, units, COUNT(*) as frequency
            FROM lottery_data
            GROUP BY hundreds, tens, units
            ORDER BY frequency DESC
        """
        df_triplet = pd.read_sql_query(query_triplet, conn)
        self.triplet_patterns = df_triplet
        
        # 4. 序列模式分析
        self.analyze_sequence_patterns()
        
        # 保存到数据库
        self.save_correlation_patterns(df_hu, df_tu, df_triplet)
        
        conn.close()
    
    def analyze_sequence_patterns(self):
        """分析序列模式"""
        conn = sqlite3.connect(self.db_path)
        
        query = """
            SELECT hundreds, tens, units, sum_value, span
            FROM lottery_data
            ORDER BY issue
        """
        df = pd.read_sql_query(query, conn)
        
        patterns = {
            'ascending': 0,      # 升序：百<十<个
            'descending': 0,     # 降序：百>十>个
            'same_all': 0,       # 全相同：百=十=个
            'same_partial': 0,   # 部分相同：任意两位相同
            'mixed': 0           # 混合：其他情况
        }
        
        pattern_details = []
        
        for _, row in df.iterrows():
            h, t, u = row['hundreds'], row['tens'], row['units']
            
            if h == t == u:
                pattern_type = 'same_all'
            elif h == t or t == u or h == u:
                pattern_type = 'same_partial'
            elif h < t < u:
                pattern_type = 'ascending'
            elif h > t > u:
                pattern_type = 'descending'
            else:
                pattern_type = 'mixed'
            
            patterns[pattern_type] += 1
            pattern_details.append({
                'pattern_type': pattern_type,
                'sum_value': row['sum_value'],
                'span': row['span']
            })
        
        # 计算概率和统计信息
        total = sum(patterns.values())
        self.sequence_patterns = {}
        
        for pattern_type, count in patterns.items():
            pattern_data = [p for p in pattern_details if p['pattern_type'] == pattern_type]
            
            self.sequence_patterns[pattern_type] = {
                'frequency': count,
                'probability': count / total,
                'avg_sum': np.mean([p['sum_value'] for p in pattern_data]) if pattern_data else 0,
                'avg_span': np.mean([p['span'] for p in pattern_data]) if pattern_data else 0
            }
        
        conn.close()
    
    def save_correlation_patterns(self, df_hu: pd.DataFrame, df_tu: pd.DataFrame, df_triplet: pd.DataFrame):
        """保存关联模式到数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 清空旧数据
        cursor.execute("DELETE FROM position_correlation_patterns")
        cursor.execute("DELETE FROM sequence_patterns")
        
        # 保存百位-个位关联
        total_hu = df_hu['frequency'].sum()
        for _, row in df_hu.iterrows():
            cursor.execute("""
                INSERT INTO position_correlation_patterns 
                (hundreds_digit, tens_digit, units_digit, frequency, probability, pattern_type)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (row['hundreds'], None, row['units'], row['frequency'], 
                  row['frequency']/total_hu, 'hundreds_units'))
        
        # 保存十位-个位关联
        total_tu = df_tu['frequency'].sum()
        for _, row in df_tu.iterrows():
            cursor.execute("""
                INSERT INTO position_correlation_patterns 
                (hundreds_digit, tens_digit, units_digit, frequency, probability, pattern_type)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (None, row['tens'], row['units'], row['frequency'], 
                  row['frequency']/total_tu, 'tens_units'))
        
        # 保存三位数组合
        total_triplet = df_triplet['frequency'].sum()
        for _, row in df_triplet.iterrows():
            cursor.execute("""
                INSERT INTO position_correlation_patterns 
                (hundreds_digit, tens_digit, units_digit, frequency, probability, pattern_type)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (row['hundreds'], row['tens'], row['units'], row['frequency'], 
                  row['frequency']/total_triplet, 'triplet'))
        
        # 保存序列模式
        for pattern_type, data in self.sequence_patterns.items():
            cursor.execute("""
                INSERT INTO sequence_patterns 
                (pattern_type, frequency, probability, avg_sum_value, avg_span_value)
                VALUES (?, ?, ?, ?, ?)
            """, (pattern_type, data['frequency'], data['probability'], 
                  data['avg_sum'], data['avg_span']))
        
        conn.commit()
        conn.close()
    
    def get_enhanced_features(self, X: np.ndarray, 
                            hundreds_prob: Optional[np.ndarray] = None,
                            tens_prob: Optional[np.ndarray] = None) -> np.ndarray:
        """获取增强特征"""
        if (self.hundreds_units_correlation is None or 
            self.tens_units_correlation is None):
            self.build_correlation_matrices()
        
        enhanced_features = []
        
        for i, features in enumerate(X):
            base_features = features.tolist()
            
            # 添加百位-个位关联特征
            if hundreds_prob is not None and i < len(hundreds_prob):
                hu_expected = np.dot(hundreds_prob[i], self.hundreds_units_correlation)
                base_features.extend(hu_expected.tolist())
                
                most_likely_hundreds = np.argmax(hundreds_prob[i])
                hundreds_confidence = np.max(hundreds_prob[i])
                base_features.extend([most_likely_hundreds, hundreds_confidence])
            else:
                base_features.extend([0] * 12)
            
            # 添加十位-个位关联特征
            if tens_prob is not None and i < len(tens_prob):
                tu_expected = np.dot(tens_prob[i], self.tens_units_correlation)
                base_features.extend(tu_expected.tolist())
                
                most_likely_tens = np.argmax(tens_prob[i])
                tens_confidence = np.max(tens_prob[i])
                base_features.extend([most_likely_tens, tens_confidence])
            else:
                base_features.extend([0] * 12)
            
            # 添加序列模式特征
            if (hundreds_prob is not None and tens_prob is not None and 
                i < len(hundreds_prob) and i < len(tens_prob)):
                
                # 预测最可能的序列模式
                h_pred = np.argmax(hundreds_prob[i])
                t_pred = np.argmax(tens_prob[i])
                
                # 序列模式概率
                ascending_prob = self.sequence_patterns.get('ascending', {}).get('probability', 0)
                descending_prob = self.sequence_patterns.get('descending', {}).get('probability', 0)
                same_prob = self.sequence_patterns.get('same_partial', {}).get('probability', 0)
                
                base_features.extend([ascending_prob, descending_prob, same_prob])
                
                # 基于当前百位十位预测的序列倾向
                if h_pred < t_pred:
                    sequence_tendency = 1  # 倾向升序
                elif h_pred > t_pred:
                    sequence_tendency = -1  # 倾向降序
                else:
                    sequence_tendency = 0  # 倾向相同
                
                base_features.append(sequence_tendency)
            else:
                base_features.extend([0] * 4)
            
            enhanced_features.append(base_features)
        
        return np.array(enhanced_features)
```

### 2. 双重关联预测器
```python
class DualCorrelationUnitsPredictor(BaseUnitsPredictor):
    def __init__(self, db_path: str):
        super().__init__(db_path)
        self.base_predictor = xgb.XGBClassifier(
            objective='multi:softprob',
            num_class=10,
            max_depth=8,  # 增加深度处理复杂关联
            learning_rate=0.08,
            n_estimators=300,
            random_state=42
        )
        self.hundreds_weight = 0.3
        self.tens_weight = 0.4
        self.sequence_weight = 0.3
    
    def build_model(self):
        """构建双重关联模型"""
        self.model = self.base_predictor
        self.build_correlation_matrices()
        return self.model
    
    def train(self, X: np.ndarray, y: np.ndarray, 
              hundreds_data: Optional[np.ndarray] = None,
              tens_data: Optional[np.ndarray] = None):
        """训练双重关联模型"""
        if self.model is None:
            self.build_model()
        
        # 创建训练时的"完美"概率分布
        hundreds_prob = np.eye(10)[hundreds_data] if hundreds_data is not None else None
        tens_prob = np.eye(10)[tens_data] if tens_data is not None else None
        
        # 获取增强特征
        X_enhanced = self.get_enhanced_features(X, hundreds_prob, tens_prob)
        
        # 训练模型
        X_train, X_val, y_train, y_val = train_test_split(
            X_enhanced, y, test_size=0.2, random_state=42, stratify=y
        )
        
        self.model.fit(
            X_train, y_train,
            eval_set=[(X_val, y_val)],
            early_stopping_rounds=30,
            verbose=False
        )
        
        self.is_trained = True
        
        # 优化权重
        if hundreds_data is not None and tens_data is not None:
            val_hundreds_prob = np.eye(10)[hundreds_data[-len(y_val):]]
            val_tens_prob = np.eye(10)[tens_data[-len(y_val):]]
            self.optimize_correlation_weights(X_val, y_val, val_hundreds_prob, val_tens_prob)
        
        val_performance = self.evaluate_model(X_val, y_val)
        print(f"双重关联个位预测器验证集性能: {val_performance}")
        
        return val_performance
    
    def predict_probability(self, X: np.ndarray, 
                          hundreds_prob: Optional[np.ndarray] = None,
                          tens_prob: Optional[np.ndarray] = None) -> np.ndarray:
        """双重关联预测"""
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        # 获取基础预测
        X_enhanced = self.get_enhanced_features(X, hundreds_prob, tens_prob)
        base_prob = self.model.predict_proba(X_enhanced)
        
        if (hundreds_prob is None or tens_prob is None or 
            self.hundreds_units_correlation is None or 
            self.tens_units_correlation is None):
            return base_prob
        
        # 计算关联增强概率
        enhanced_prob = []
        
        for i in range(len(X)):
            # 基于百位概率的个位期望
            hu_correlation_prob = np.dot(hundreds_prob[i], self.hundreds_units_correlation)
            
            # 基于十位概率的个位期望
            tu_correlation_prob = np.dot(tens_prob[i], self.tens_units_correlation)
            
            # 序列模式增强
            sequence_prob = self.get_sequence_enhanced_prob(
                hundreds_prob[i], tens_prob[i]
            )
            
            # 多重融合
            final_prob = (
                (1 - self.hundreds_weight - self.tens_weight - self.sequence_weight) * base_prob[i] +
                self.hundreds_weight * hu_correlation_prob +
                self.tens_weight * tu_correlation_prob +
                self.sequence_weight * sequence_prob
            )
            
            # 归一化
            final_prob = final_prob / np.sum(final_prob)
            enhanced_prob.append(final_prob)
        
        return np.array(enhanced_prob)
    
    def get_sequence_enhanced_prob(self, hundreds_prob: np.ndarray, tens_prob: np.ndarray) -> np.ndarray:
        """基于序列模式的概率增强"""
        units_prob = np.full(10, 0.1)  # 初始均匀分布
        
        # 获取最可能的百位和十位
        h_pred = np.argmax(hundreds_prob)
        t_pred = np.argmax(tens_prob)
        
        # 根据序列模式调整个位概率
        if h_pred < t_pred:  # 当前趋势是升序
            # 增加大于十位的数字概率
            for u in range(t_pred + 1, 10):
                units_prob[u] *= 1.5
            # 降低小于十位的数字概率
            for u in range(0, t_pred):
                units_prob[u] *= 0.7
        elif h_pred > t_pred:  # 当前趋势是降序
            # 增加小于十位的数字概率
            for u in range(0, t_pred):
                units_prob[u] *= 1.5
            # 降低大于十位的数字概率
            for u in range(t_pred + 1, 10):
                units_prob[u] *= 0.7
        else:  # 百位等于十位
            # 增加相同数字的概率
            units_prob[t_pred] *= 2.0
        
        # 归一化
        return units_prob / np.sum(units_prob)
    
    def optimize_correlation_weights(self, X_val: np.ndarray, y_val: np.ndarray,
                                   hundreds_prob_val: np.ndarray, tens_prob_val: np.ndarray):
        """优化关联权重"""
        best_weights = (0.3, 0.4, 0.3)
        best_accuracy = 0
        
        # 网格搜索最优权重组合
        for h_w in np.arange(0.1, 0.6, 0.1):
            for t_w in np.arange(0.1, 0.6, 0.1):
                for s_w in np.arange(0.1, 0.5, 0.1):
                    if h_w + t_w + s_w <= 0.9:  # 确保基础权重至少0.1
                        self.hundreds_weight = h_w
                        self.tens_weight = t_w
                        self.sequence_weight = s_w
                        
                        y_prob = self.predict_probability(X_val, hundreds_prob_val, tens_prob_val)
                        y_pred = np.argmax(y_prob, axis=1)
                        accuracy = accuracy_score(y_val, y_pred)
                        
                        if accuracy > best_accuracy:
                            best_accuracy = accuracy
                            best_weights = (h_w, t_w, s_w)
        
        self.hundreds_weight, self.tens_weight, self.sequence_weight = best_weights
        print(f"最优权重 - 百位: {self.hundreds_weight:.1f}, "
              f"十位: {self.tens_weight:.1f}, 序列: {self.sequence_weight:.1f}, "
              f"准确率: {best_accuracy:.4f}")
        
        return best_weights
```

### 3. 集成个位预测器
```python
class EnsembleUnitsPredictor:
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.xgb_predictor = XGBUnitsPredictor(db_path)  # 基础XGB预测器
        self.lgb_predictor = LGBUnitsPredictor(db_path)  # 基础LGB预测器
        self.correlation_predictor = DualCorrelationUnitsPredictor(db_path)
        self.lstm_predictor = LSTMUnitsPredictor(db_path)  # LSTM预测器
        
        # 初始权重
        self.weights = {
            'xgb': 0.25,
            'lgb': 0.25,
            'correlation': 0.3,
            'lstm': 0.2
        }
        
        self.is_trained = False
    
    def train_all_models(self):
        """训练所有模型"""
        X, y, hundreds_data, tens_data = self.xgb_predictor.load_data()
        
        print("训练XGBoost个位预测器...")
        xgb_performance = self.xgb_predictor.train(X, y, hundreds_data, tens_data)
        
        print("训练LightGBM个位预测器...")
        lgb_performance = self.lgb_predictor.train(X, y, hundreds_data, tens_data)
        
        print("训练双重关联个位预测器...")
        correlation_performance = self.correlation_predictor.train(X, y, hundreds_data, tens_data)
        
        print("训练LSTM个位预测器...")
        lstm_performance = self.lstm_predictor.train(X, y, hundreds_data, tens_data)
        
        # 根据性能调整权重
        self.adjust_weights(xgb_performance, lgb_performance, 
                          correlation_performance, lstm_performance)
        
        self.is_trained = True
        
        return {
            'xgb': xgb_performance,
            'lgb': lgb_performance,
            'correlation': correlation_performance,
            'lstm': lstm_performance,
            'weights': self.weights
        }
    
    def adjust_weights(self, xgb_perf: Dict, lgb_perf: Dict, 
                      corr_perf: Dict, lstm_perf: Dict):
        """根据性能调整权重"""
        performances = {
            'xgb': xgb_perf.get('accuracy', 0),
            'lgb': lgb_perf.get('accuracy', 0),
            'correlation': corr_perf.get('accuracy', 0),
            'lstm': lstm_perf.get('accuracy', 0)
        }
        
        total_acc = sum(performances.values())
        
        if total_acc > 0:
            for model_name in self.weights:
                self.weights[model_name] = performances[model_name] / total_acc
        
        print(f"调整后的个位预测器权重: {self.weights}")
    
    def predict_probability(self, X: np.ndarray, 
                          hundreds_prob: Optional[np.ndarray] = None,
                          tens_prob: Optional[np.ndarray] = None) -> np.ndarray:
        """集成预测概率分布"""
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        # 获取各模型预测
        xgb_prob = self.xgb_predictor.predict_probability(X, hundreds_prob, tens_prob)
        lgb_prob = self.lgb_predictor.predict_probability(X, hundreds_prob, tens_prob)
        corr_prob = self.correlation_predictor.predict_probability(X, hundreds_prob, tens_prob)
        lstm_prob = self.lstm_predictor.predict_probability(X, hundreds_prob, tens_prob)
        
        # 加权融合
        ensemble_prob = (
            self.weights['xgb'] * xgb_prob +
            self.weights['lgb'] * lgb_prob +
            self.weights['correlation'] * corr_prob +
            self.weights['lstm'] * lstm_prob
        )
        
        # 确保概率和为1
        ensemble_prob = ensemble_prob / np.sum(ensemble_prob, axis=1, keepdims=True)
        
        return ensemble_prob
    
    def predict_next_period(self, issue: str, 
                          hundreds_prob: Optional[np.ndarray] = None,
                          tens_prob: Optional[np.ndarray] = None) -> Dict:
        """预测下一期个位"""
        # 获取最新特征
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT feature_vector FROM feature_data 
            WHERE feature_type = 'units' 
            ORDER BY issue DESC 
            LIMIT 1
        """)
        
        row = cursor.fetchone()
        if not row:
            raise ValueError("没有找到最新的个位特征数据")
        
        latest_features = np.array(json.loads(row[0])).reshape(1, -1)
        
        # 预测概率
        probabilities = self.predict_probability(latest_features, hundreds_prob, tens_prob)[0]
        
        # 分析序列模式
        sequence_pattern = self.analyze_sequence_pattern(hundreds_prob, tens_prob, probabilities)
        
        # 计算关联度
        correlation_scores = self.calculate_correlation_scores(
            hundreds_prob, tens_prob, probabilities
        )
        
        # 保存预测结果
        self.save_prediction(issue, probabilities, correlation_scores, sequence_pattern)
        
        conn.close()
        
        return {
            'issue': issue,
            'probabilities': probabilities.tolist(),
            'predicted_digit': int(np.argmax(probabilities)),
            'confidence': float(np.max(probabilities)),
            'hundreds_correlation': correlation_scores['hundreds'],
            'tens_correlation': correlation_scores['tens'],
            'sequence_pattern': sequence_pattern,
            'top3_digits': np.argsort(probabilities)[-3:].tolist()[::-1]
        }
    
    def analyze_sequence_pattern(self, hundreds_prob: Optional[np.ndarray], 
                               tens_prob: Optional[np.ndarray], 
                               units_prob: np.ndarray) -> str:
        """分析序列模式"""
        if hundreds_prob is None or tens_prob is None:
            return "unknown"
        
        h_pred = np.argmax(hundreds_prob[0])
        t_pred = np.argmax(tens_prob[0])
        u_pred = np.argmax(units_prob)
        
        if h_pred == t_pred == u_pred:
            return "same_all"
        elif h_pred == t_pred or t_pred == u_pred or h_pred == u_pred:
            return "same_partial"
        elif h_pred < t_pred < u_pred:
            return "ascending"
        elif h_pred > t_pred > u_pred:
            return "descending"
        else:
            return "mixed"
    
    def calculate_correlation_scores(self, hundreds_prob: Optional[np.ndarray],
                                   tens_prob: Optional[np.ndarray],
                                   units_prob: np.ndarray) -> Dict[str, float]:
        """计算关联度分数"""
        scores = {'hundreds': 0.0, 'tens': 0.0}
        
        if (hundreds_prob is not None and 
            self.correlation_predictor.hundreds_units_correlation is not None):
            expected_prob = np.dot(hundreds_prob[0], 
                                 self.correlation_predictor.hundreds_units_correlation)
            scores['hundreds'] = float(np.dot(units_prob, expected_prob))
        
        if (tens_prob is not None and 
            self.correlation_predictor.tens_units_correlation is not None):
            expected_prob = np.dot(tens_prob[0], 
                                 self.correlation_predictor.tens_units_correlation)
            scores['tens'] = float(np.dot(units_prob, expected_prob))
        
        return scores
    
    def save_prediction(self, issue: str, probabilities: np.ndarray, 
                       correlation_scores: Dict[str, float], sequence_pattern: str):
        """保存预测结果"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        predicted_digit = int(np.argmax(probabilities))
        confidence = float(np.max(probabilities))
        
        cursor.execute("""
            INSERT OR REPLACE INTO units_predictions 
            (issue, model_type, prob_0, prob_1, prob_2, prob_3, prob_4, 
             prob_5, prob_6, prob_7, prob_8, prob_9, predicted_digit, 
             confidence, hundreds_correlation, tens_correlation, sequence_pattern)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (issue, 'ensemble', *probabilities.tolist(), predicted_digit, 
              confidence, correlation_scores['hundreds'], correlation_scores['tens'], 
              sequence_pattern))
        
        conn.commit()
        conn.close()
```

## 成功标准

### 模型性能
- [ ] XGBoost准确率 > 35%
- [ ] LightGBM准确率 > 35%
- [ ] 双重关联准确率 > 40%
- [ ] LSTM准确率 > 32%
- [ ] 集成模型准确率 > 42%

### 关联性分析
- [ ] 百位-个位关联矩阵构建完成
- [ ] 十位-个位关联矩阵构建完成
- [ ] 序列模式识别准确率 > 60%
- [ ] 双重关联权重自动优化

### 预测质量
- [ ] Top3准确率 > 75%
- [ ] 平均置信度 > 0.16
- [ ] 序列模式预测准确

## 部署说明

```python
# 使用示例
predictor = EnsembleUnitsPredictor("data/lottery.db")

# 训练所有模型
performance = predictor.train_all_models()
print(f"个位预测器训练完成: {performance}")

# 预测下一期（需要百位和十位概率）
hundreds_prob = np.array([[0.1, 0.2, 0.15, 0.1, 0.05, 0.1, 0.1, 0.05, 0.1, 0.05]])
tens_prob = np.array([[0.05, 0.1, 0.2, 0.15, 0.1, 0.1, 0.1, 0.05, 0.1, 0.05]])
prediction = predictor.predict_next_period("2024001", hundreds_prob, tens_prob)
print(f"个位预测结果: {prediction}")
```

## 下一步
完成P5后，进入**P6-和值预测器**开发阶段。
