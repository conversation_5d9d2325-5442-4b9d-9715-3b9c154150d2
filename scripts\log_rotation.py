#!/usr/bin/env python3
"""
日志轮转管理脚本

实现日志文件的自动轮转、压缩和清理功能
确保日志文件不会无限增长，保持系统稳定

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import os
import sys
import gzip
import shutil
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any
import argparse

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

class LogRotationManager:
    """日志轮转管理器"""
    
    def __init__(self, logs_dir: str = "logs"):
        """
        初始化日志轮转管理器
        
        Args:
            logs_dir: 日志目录路径
        """
        self.project_root = Path(__file__).parent.parent
        self.logs_dir = self.project_root / logs_dir
        
        # 确保日志目录存在
        self.logs_dir.mkdir(exist_ok=True)
        
        # 轮转配置
        self.rotation_config = {
            'max_file_size_mb': 20,     # 单个日志文件最大大小(MB)
            'max_backup_count': 10,     # 最大备份文件数量
            'compress_backups': True,   # 是否压缩备份文件
            'retention_days': 30,       # 日志保留天数
        }
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    def get_log_files(self) -> List[Path]:
        """获取所有日志文件"""
        log_files = []
        
        # 查找所有.log文件
        for file_path in self.logs_dir.glob("*.log"):
            if file_path.is_file():
                log_files.append(file_path)
        
        return log_files
    
    def get_file_size_mb(self, file_path: Path) -> float:
        """获取文件大小(MB)"""
        try:
            size_bytes = file_path.stat().st_size
            return size_bytes / (1024 * 1024)
        except Exception:
            return 0.0
    
    def rotate_log_file(self, log_file: Path) -> bool:
        """轮转单个日志文件"""
        try:
            # 检查文件大小
            file_size_mb = self.get_file_size_mb(log_file)
            if file_size_mb < self.rotation_config['max_file_size_mb']:
                return False
            
            self.logger.info(f"开始轮转日志文件: {log_file.name} ({file_size_mb:.2f}MB)")
            
            # 生成备份文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"{log_file.stem}_{timestamp}.log"
            backup_path = self.logs_dir / backup_name
            
            # 移动当前日志文件到备份位置
            shutil.move(str(log_file), str(backup_path))
            
            # 创建新的空日志文件
            log_file.touch()
            
            # 压缩备份文件
            if self.rotation_config['compress_backups']:
                self._compress_file(backup_path)
            
            self.logger.info(f"日志文件轮转完成: {log_file.name}")
            return True
            
        except Exception as e:
            self.logger.error(f"轮转日志文件失败 {log_file.name}: {e}")
            return False
    
    def _compress_file(self, file_path: Path):
        """压缩文件"""
        try:
            compressed_path = file_path.with_suffix(file_path.suffix + '.gz')
            
            with open(file_path, 'rb') as f_in:
                with gzip.open(compressed_path, 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
            
            # 删除原文件
            file_path.unlink()
            
            self.logger.info(f"文件已压缩: {compressed_path.name}")
            
        except Exception as e:
            self.logger.error(f"压缩文件失败 {file_path.name}: {e}")
    
    def cleanup_old_backups(self):
        """清理旧的备份文件"""
        try:
            current_time = datetime.now()
            retention_delta = timedelta(days=self.rotation_config['retention_days'])
            
            # 查找所有备份文件
            backup_files = []
            for pattern in ["*.log.*", "*.log.gz"]:
                backup_files.extend(self.logs_dir.glob(pattern))
            
            # 按文件名分组
            file_groups = {}
            for backup_file in backup_files:
                # 提取基础文件名
                base_name = backup_file.name.split('_')[0]
                if base_name not in file_groups:
                    file_groups[base_name] = []
                file_groups[base_name].append(backup_file)
            
            # 清理每个文件组
            for base_name, files in file_groups.items():
                # 按修改时间排序
                files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
                
                # 保留最新的N个文件
                max_count = self.rotation_config['max_backup_count']
                files_to_keep = files[:max_count]
                files_to_remove = files[max_count:]
                
                # 删除超出数量限制的文件
                for file_path in files_to_remove:
                    try:
                        file_path.unlink()
                        self.logger.info(f"删除旧备份文件: {file_path.name}")
                    except Exception as e:
                        self.logger.error(f"删除文件失败 {file_path.name}: {e}")
                
                # 删除超出时间限制的文件
                for file_path in files_to_keep:
                    try:
                        file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                        if current_time - file_time > retention_delta:
                            file_path.unlink()
                            self.logger.info(f"删除过期备份文件: {file_path.name}")
                    except Exception as e:
                        self.logger.error(f"删除过期文件失败 {file_path.name}: {e}")
            
        except Exception as e:
            self.logger.error(f"清理备份文件失败: {e}")
    
    def rotate_all_logs(self):
        """轮转所有日志文件"""
        self.logger.info("开始日志轮转任务")
        
        log_files = self.get_log_files()
        rotated_count = 0
        
        for log_file in log_files:
            if self.rotate_log_file(log_file):
                rotated_count += 1
        
        # 清理旧备份
        self.cleanup_old_backups()
        
        self.logger.info(f"日志轮转任务完成，共轮转 {rotated_count} 个文件")
    
    def get_log_status(self) -> Dict[str, Any]:
        """获取日志状态信息"""
        log_files = self.get_log_files()
        
        status = {
            'total_files': len(log_files),
            'files': [],
            'total_size_mb': 0.0,
            'timestamp': datetime.now().isoformat()
        }
        
        for log_file in log_files:
            file_size_mb = self.get_file_size_mb(log_file)
            status['total_size_mb'] += file_size_mb
            
            file_info = {
                'name': log_file.name,
                'size_mb': round(file_size_mb, 2),
                'needs_rotation': file_size_mb >= self.rotation_config['max_file_size_mb'],
                'modified_time': datetime.fromtimestamp(log_file.stat().st_mtime).isoformat()
            }
            status['files'].append(file_info)
        
        status['total_size_mb'] = round(status['total_size_mb'], 2)
        return status
    
    def force_rotate_file(self, filename: str) -> bool:
        """强制轮转指定文件"""
        log_file = self.logs_dir / filename
        
        if not log_file.exists():
            self.logger.error(f"文件不存在: {filename}")
            return False
        
        # 临时修改配置，强制轮转
        original_max_size = self.rotation_config['max_file_size_mb']
        self.rotation_config['max_file_size_mb'] = 0
        
        try:
            result = self.rotate_log_file(log_file)
            return result
        finally:
            # 恢复原配置
            self.rotation_config['max_file_size_mb'] = original_max_size


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="日志轮转管理工具")
    parser.add_argument('--action', choices=['rotate', 'status', 'cleanup', 'force'], 
                       default='rotate', help='执行的操作')
    parser.add_argument('--file', type=str, help='指定要强制轮转的文件名')
    
    args = parser.parse_args()
    
    manager = LogRotationManager()
    
    if args.action == 'rotate':
        print("执行日志轮转...")
        manager.rotate_all_logs()
        print("日志轮转完成")
        
    elif args.action == 'status':
        status = manager.get_log_status()
        print("日志状态:")
        print(f"总文件数: {status['total_files']}")
        print(f"总大小: {status['total_size_mb']} MB")
        print("\n文件详情:")
        for file_info in status['files']:
            rotation_status = "需要轮转" if file_info['needs_rotation'] else "正常"
            print(f"  {file_info['name']}: {file_info['size_mb']} MB - {rotation_status}")
    
    elif args.action == 'cleanup':
        print("清理旧备份文件...")
        manager.cleanup_old_backups()
        print("清理完成")
    
    elif args.action == 'force':
        if not args.file:
            print("错误: 强制轮转需要指定文件名 (--file)")
            return
        
        print(f"强制轮转文件: {args.file}")
        if manager.force_rotate_file(args.file):
            print("轮转成功")
        else:
            print("轮转失败")


if __name__ == "__main__":
    main()
