# P8智能交集融合系统配置文件
# Author: Augment Code AI Assistant
# Date: 2025-01-14

# 融合引擎配置
fusion:
  # 默认融合方法
  default_method: "adaptive_fusion"
  
  # 权重配置
  probability_weight: 0.5      # 概率权重
  constraint_weight: 0.3       # 约束权重
  diversity_weight: 0.2        # 多样性权重
  
  # 融合参数
  min_probability: 1.0e-6      # 最小概率阈值
  max_recommendations: 20      # 最大推荐数量
  normalization_method: "softmax"  # 归一化方法
  temperature: 1.0             # softmax温度参数
  confidence_threshold: 0.1    # 置信度阈值

# 排序器配置
ranking:
  # 默认排序策略
  default_strategy: "adaptive"
  
  # 排序参数
  top_k: 20                    # Top-K数量
  min_score_threshold: 0.01    # 最小分数阈值
  diversity_penalty: 0.1       # 多样性惩罚
  recency_weight: 0.05         # 时效性权重
  
  # 策略权重
  probability_weight: 0.5      # 概率权重
  constraint_weight: 0.3       # 约束权重
  diversity_weight: 0.2        # 多样性权重

# 权重调整器配置
weights:
  # 学习参数
  learning_rate: 0.1           # 学习率
  decay_factor: 0.95           # 衰减因子
  
  # 权重约束
  min_weight: 0.1              # 最小权重
  max_weight: 2.0              # 最大权重
  
  # 评估参数
  evaluation_window: 30        # 评估窗口（天）
  min_samples: 10              # 最小样本数
  confidence_threshold: 0.6    # 置信度阈值
  performance_threshold: 0.3   # 性能阈值

# 约束优化器配置
constraints:
  # 容差参数
  sum_tolerance: 2.0           # 和值容差
  span_tolerance: 1.0          # 跨度容差
  
  # 权重参数
  consistency_weight: 0.3      # 一致性权重
  diversity_weight: 0.2        # 多样性权重
  
  # 约束参数
  min_probability: 1.0e-6      # 最小概率阈值
  max_recommendations: 20      # 最大推荐数量

# 数据库配置
database:
  # 数据库路径
  path: "data/lottery.db"
  
  # 连接参数
  timeout: 30                  # 连接超时（秒）
  check_same_thread: false     # 检查同线程
  
  # 性能参数
  cache_size: 1000             # 缓存大小
  journal_mode: "WAL"          # 日志模式

# 日志配置
logging:
  # 日志级别
  level: "INFO"
  
  # 日志格式
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
  # 日志文件
  file: "logs/fusion_system.log"
  
  # 日志轮转
  max_bytes: 10485760          # 10MB
  backup_count: 5              # 备份数量

# 性能监控配置
monitoring:
  # 监控间隔
  interval: 300                # 5分钟
  
  # 性能指标
  metrics:
    - "prediction_count"
    - "success_rate"
    - "avg_execution_time"
    - "hit_rate"
    - "confidence_accuracy"
  
  # 告警阈值
  alerts:
    low_success_rate: 0.8      # 成功率低于80%告警
    high_execution_time: 5.0   # 执行时间超过5秒告警
    low_hit_rate: 0.1          # 命中率低于10%告警

# 预测器配置
predictors:
  # 预测器列表
  enabled:
    - "hundreds"
    - "tens" 
    - "units"
    - "sum"
    - "span"
  
  # 预测器权重
  weights:
    hundreds: 1.0
    tens: 1.0
    units: 1.0
    sum: 0.8
    span: 0.6
  
  # 超时配置
  timeout: 10                  # 预测超时（秒）
  
  # 重试配置
  max_retries: 3               # 最大重试次数
  retry_delay: 1               # 重试延迟（秒）

# 缓存配置
cache:
  # 缓存启用
  enabled: true
  
  # 缓存大小
  max_size: 1000               # 最大缓存条目数
  
  # 缓存过期时间
  ttl: 3600                    # 1小时（秒）
  
  # 缓存类型
  type: "memory"               # memory/redis

# API配置
api:
  # 服务端口
  port: 8080
  
  # 服务主机
  host: "0.0.0.0"
  
  # 请求限制
  rate_limit: 100              # 每分钟请求数
  
  # 超时配置
  timeout: 30                  # 请求超时（秒）
  
  # 认证配置
  auth:
    enabled: false
    token_expiry: 86400        # 24小时（秒）

# 实验配置
experiments:
  # A/B测试
  ab_testing:
    enabled: false
    test_ratio: 0.1            # 测试流量比例
  
  # 新算法测试
  new_algorithms:
    enabled: false
    algorithms:
      - "experimental_fusion"
      - "neural_ranking"
  
  # 特征实验
  feature_flags:
    advanced_constraints: true
    dynamic_weights: true
    real_time_optimization: false

# 部署配置
deployment:
  # 环境
  environment: "development"   # development/staging/production
  
  # 版本
  version: "1.0.0"
  
  # 健康检查
  health_check:
    enabled: true
    interval: 60               # 健康检查间隔（秒）
    timeout: 5                 # 健康检查超时（秒）
  
  # 备份配置
  backup:
    enabled: true
    interval: 86400            # 24小时（秒）
    retention: 30              # 保留天数
