"""
数据采集器模块
基于3dyuce项目成功经验，实现稳定的数据采集功能
"""

import requests
import time
import random
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
from urllib.parse import urlparse
import re
from bs4 import BeautifulSoup

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class LotteryDataCollector:
    """福彩3D数据采集器"""
    
    def __init__(self, config=None):
        self.config = config or self._get_default_config()
        self.session = requests.Session()
        self.collected_data = []
        self.collection_stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'total_records': 0
        }
    
    def _get_default_config(self) -> Dict:
        """获取默认配置 - 优化版本"""
        return {
            # 17500.cn数据源（主要）- 相同数据，不同排序
            'primary_sources': {
                'complete_collection': "https://data.17500.cn/3d_asc.txt",   # 正序：适合完整采集
                'incremental_update': "https://data.17500.cn/3d_desc.txt",  # 倒序：适合增量更新
                'backup_17500': "https://data.17500.cn/3d_desc.txt"         # 备用：倒序数据
            },
            # 传统数据源（备用）
            'backup_sources': [
                "https://www.17500.cn/chart/3d-tjb.html",
                "https://www.cjcp.com.cn/kaijiang/3dmingxi_0.html"
            ],
            'request_timeout': 30,
            'max_retries': 3,
            'base_delay': 2,
            'max_delay': 10,
            'user_agents': [
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            ]
        }
    
    def get_random_headers(self) -> Dict[str, str]:
        """获取随机请求头"""
        return {
            'User-Agent': random.choice(self.config['user_agents']),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'max-age=0'
        }
    
    def safe_request(self, url: str, retries: int = None) -> Optional[requests.Response]:
        """安全的HTTP请求，包含重试机制"""
        if retries is None:
            retries = self.config['max_retries']
        
        for attempt in range(retries + 1):
            try:
                self.collection_stats['total_requests'] += 1
                
                headers = self.get_random_headers()
                response = self.session.get(
                    url,
                    headers=headers,
                    timeout=self.config['request_timeout']
                )
                
                if response.status_code == 200:
                    self.collection_stats['successful_requests'] += 1
                    logger.info(f"成功请求: {url} (尝试 {attempt + 1}/{retries + 1})")
                    return response
                else:
                    logger.warning(f"HTTP错误 {response.status_code}: {url}")
                    
            except requests.exceptions.Timeout:
                logger.warning(f"请求超时: {url} (尝试 {attempt + 1}/{retries + 1})")
            except requests.exceptions.ConnectionError:
                logger.warning(f"连接错误: {url} (尝试 {attempt + 1}/{retries + 1})")
            except Exception as e:
                logger.error(f"请求异常: {url} - {str(e)}")
            
            # 如果不是最后一次尝试，等待后重试
            if attempt < retries:
                delay = min(
                    self.config['base_delay'] * (2 ** attempt) + random.uniform(0, 1),
                    self.config['max_delay']
                )
                logger.info(f"等待 {delay:.1f} 秒后重试...")
                time.sleep(delay)
        
        self.collection_stats['failed_requests'] += 1
        logger.error(f"所有重试失败: {url}")
        return None
    
    def collect_from_html_source(self, url: str) -> List[Dict]:
        """从HTML数据源采集数据"""
        logger.info(f"开始从HTML源采集数据: {url}")

        response = self.safe_request(url)
        if not response:
            return []

        try:
            soup = BeautifulSoup(response.text, 'html.parser')
            data_list = []

            # 更全面的表格查找策略
            tables = []

            # 1. 查找特定class的表格
            tables.extend(soup.find_all('table', class_='chart_table'))
            tables.extend(soup.find_all('table', class_='kj_table'))
            tables.extend(soup.find_all('table', class_='data_table'))

            # 2. 查找包含福彩3D关键词的表格
            if not tables:
                all_tables = soup.find_all('table')
                for table in all_tables:
                    text = table.get_text()
                    if any(keyword in text for keyword in ['期号', '开奖', '福彩3D', '3D', '试机号']):
                        tables.append(table)

            # 3. 如果还没找到，查找所有表格
            if not tables:
                tables = soup.find_all('table')

            logger.info(f"找到 {len(tables)} 个候选表格")

            for table_idx, table in enumerate(tables):
                logger.info(f"处理表格 {table_idx + 1}")
                rows = table.find_all('tr')

                for row_idx, row in enumerate(rows):
                    cells = row.find_all(['td', 'th'])
                    if len(cells) >= 3:  # 至少包含期号、日期、开奖号码
                        try:
                            data = self._parse_html_row(cells, row_idx == 0)  # 传递是否为表头信息
                            if data:
                                data_list.append(data)
                                logger.debug(f"成功解析行 {row_idx}: {data.get('issue', 'N/A')}")
                        except Exception as e:
                            logger.debug(f"解析行 {row_idx} 失败: {str(e)}")
                            continue

            logger.info(f"从HTML源采集到 {len(data_list)} 条数据")
            return data_list

        except Exception as e:
            logger.error(f"HTML数据解析失败: {str(e)}")
            return []
    
    def _parse_html_row(self, cells, is_header: bool = False) -> Optional[Dict]:
        """解析HTML表格行数据"""
        try:
            # 跳过表头行
            if is_header:
                return None

            # 提取文本内容
            cell_texts = [cell.get_text().strip() for cell in cells]

            # 过滤空白和无效内容
            cell_texts = [text for text in cell_texts if text and text != '-']

            if len(cell_texts) < 3:
                return None

            # 查找期号 - 更灵活的匹配
            issue = None
            for text in cell_texts[:3]:  # 期号通常在前几列
                # 匹配7位数字的期号
                if re.match(r'^\d{7}$', text):
                    issue = text
                    break
                # 匹配可能包含年份的期号格式
                issue_match = re.search(r'(20\d{2}\d{3})', text)
                if issue_match:
                    issue = issue_match.group(1)
                    break

            if not issue:
                return None

            # 查找日期 - 支持多种日期格式
            date_text = ""
            for text in cell_texts:
                # 标准日期格式
                if re.match(r'\d{4}-\d{2}-\d{2}', text):
                    date_text = text
                    break
                # 其他日期格式
                date_match = re.search(r'(\d{4})[/-](\d{1,2})[/-](\d{1,2})', text)
                if date_match:
                    year, month, day = date_match.groups()
                    date_text = f"{year}-{month.zfill(2)}-{day.zfill(2)}"
                    break

            # 查找开奖号码 - 更灵活的匹配
            numbers = []
            for text in cell_texts:
                # 匹配三位连续数字
                number_match = re.search(r'(\d)[\s,]*(\d)[\s,]*(\d)', text)
                if number_match and len(text.replace(' ', '').replace(',', '')) <= 5:
                    numbers = [int(number_match.group(1)),
                              int(number_match.group(2)),
                              int(number_match.group(3))]
                    break

                # 匹配三位数字（如"123"）
                if re.match(r'^\d{3}$', text):
                    numbers = [int(text[0]), int(text[1]), int(text[2])]
                    break

            if not numbers or len(numbers) != 3:
                return None
            
            # 构建数据记录
            data = {
                'issue': issue,
                'draw_date': date_text,
                'hundreds': numbers[0],
                'tens': numbers[1],
                'units': numbers[2],
                'sum_value': sum(numbers),
                'span': max(numbers) - min(numbers),
                'number_type': self._determine_number_type(numbers)
            }
            
            # 尝试提取试机号码（如果存在）
            trial_numbers = self._extract_trial_numbers(cell_texts)
            if trial_numbers:
                data.update({
                    'trial_hundreds': trial_numbers[0],
                    'trial_tens': trial_numbers[1],
                    'trial_units': trial_numbers[2]
                })
            
            return data
            
        except Exception as e:
            logger.warning(f"解析HTML行失败: {str(e)}")
            return None
    
    def collect_from_text_source(self, url: str) -> List[Dict]:
        """从文本数据源采集数据"""
        logger.info(f"开始从文本源采集数据: {url}")
        
        response = self.safe_request(url)
        if not response:
            return []
        
        try:
            lines = response.text.strip().split('\n')
            data_list = []
            
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                
                try:
                    data = self._parse_text_line(line)
                    if data:
                        data_list.append(data)
                except Exception as e:
                    logger.warning(f"解析文本行失败: {line} - {str(e)}")
                    continue
            
            logger.info(f"从文本源采集到 {len(data_list)} 条数据")
            return data_list
            
        except Exception as e:
            logger.error(f"文本数据解析失败: {str(e)}")
            return []
    
    def _parse_text_line(self, line: str) -> Optional[Dict]:
        """解析文本行数据"""
        try:
            # 常见的文本格式：期号,日期,开奖号码,试机号码,销售额等
            parts = line.split(',')
            if len(parts) < 3:
                return None
            
            issue = parts[0].strip()
            if not re.match(r'\d{7}', issue):
                return None
            
            date_text = parts[1].strip()
            
            # 解析开奖号码
            number_text = parts[2].strip()
            number_match = re.search(r'(\d)(\d)(\d)', number_text)
            if not number_match:
                return None
            
            numbers = [int(number_match.group(1)), 
                      int(number_match.group(2)), 
                      int(number_match.group(3))]
            
            data = {
                'issue': issue,
                'draw_date': date_text,
                'hundreds': numbers[0],
                'tens': numbers[1],
                'units': numbers[2],
                'sum_value': sum(numbers),
                'span': max(numbers) - min(numbers),
                'number_type': self._determine_number_type(numbers)
            }
            
            # 尝试解析试机号码
            if len(parts) > 3:
                trial_text = parts[3].strip()
                trial_match = re.search(r'(\d)(\d)(\d)', trial_text)
                if trial_match:
                    data.update({
                        'trial_hundreds': int(trial_match.group(1)),
                        'trial_tens': int(trial_match.group(2)),
                        'trial_units': int(trial_match.group(3))
                    })
            
            # 尝试解析销售额
            if len(parts) > 4:
                sales_text = parts[4].strip()
                sales_match = re.search(r'([\d,]+)', sales_text)
                if sales_match:
                    sales_amount = float(sales_match.group(1).replace(',', ''))
                    data['sales_amount'] = sales_amount
            
            return data
            
        except Exception as e:
            logger.warning(f"解析文本行失败: {str(e)}")
            return None
    
    def _extract_trial_numbers(self, cell_texts: List[str]) -> Optional[List[int]]:
        """提取试机号码"""
        for text in cell_texts:
            # 查找试机号码模式
            if '试机' in text or 'trial' in text.lower():
                number_match = re.search(r'(\d)(\d)(\d)', text)
                if number_match:
                    return [int(number_match.group(1)), 
                           int(number_match.group(2)), 
                           int(number_match.group(3))]
        return None
    
    def _determine_number_type(self, numbers: List[int]) -> str:
        """判断号码类型"""
        unique_count = len(set(numbers))
        if unique_count == 1:
            return "豹子"
        elif unique_count == 2:
            return "对子"
        else:
            return "组六"
    
    def collect_data(self, source_url: str = None, limit: int = None) -> List[Dict]:
        """主要的数据采集方法"""
        start_time = time.time()
        logger.info("开始数据采集...")
        
        sources = [source_url] if source_url else (
            self.config['primary_sources'] + self.config['backup_sources']
        )
        
        all_data = []
        
        for url in sources:
            try:
                logger.info(f"尝试数据源: {url}")
                
                if url.endswith('.txt'):
                    data = self.collect_from_text_source(url)
                else:
                    data = self.collect_from_html_source(url)
                
                if data:
                    all_data.extend(data)
                    logger.info(f"从 {url} 采集到 {len(data)} 条数据")
                    
                    # 如果已经获得足够数据，可以停止
                    if limit and len(all_data) >= limit:
                        all_data = all_data[:limit]
                        break
                else:
                    logger.warning(f"从 {url} 未采集到数据")
                
                # 请求间隔
                time.sleep(random.uniform(1, 3))
                
            except Exception as e:
                logger.error(f"采集数据源 {url} 失败: {str(e)}")
                continue
        
        # 去重处理
        unique_data = self._deduplicate_data(all_data)
        
        collection_time = time.time() - start_time
        self.collection_stats['total_records'] = len(unique_data)
        
        logger.info(f"数据采集完成:")
        logger.info(f"  采集时间: {collection_time:.2f} 秒")
        logger.info(f"  总请求数: {self.collection_stats['total_requests']}")
        logger.info(f"  成功请求: {self.collection_stats['successful_requests']}")
        logger.info(f"  失败请求: {self.collection_stats['failed_requests']}")
        logger.info(f"  采集记录: {len(unique_data)} 条")
        
        return unique_data
    
    def _deduplicate_data(self, data_list: List[Dict]) -> List[Dict]:
        """数据去重"""
        seen_issues = set()
        unique_data = []
        
        for data in data_list:
            issue = data.get('issue')
            if issue and issue not in seen_issues:
                seen_issues.add(issue)
                unique_data.append(data)
        
        logger.info(f"去重前: {len(data_list)} 条，去重后: {len(unique_data)} 条")
        return unique_data
    
    def get_collection_stats(self) -> Dict:
        """获取采集统计信息"""
        return self.collection_stats.copy()


if __name__ == "__main__":
    # 测试数据采集器
    print("=== 福彩3D数据采集器测试 ===")

    collector = LotteryDataCollector()

    # 测试采集
    data = collector.collect_data(limit=5)

    print(f"\n采集结果:")
    for i, record in enumerate(data[:3], 1):
        print(f"{i}. 期号: {record.get('issue')}, "
              f"日期: {record.get('draw_date')}, "
              f"号码: {record.get('hundreds')}{record.get('tens')}{record.get('units')}")

    # 显示统计信息
    stats = collector.get_collection_stats()
    print(f"\n统计信息:")
    for key, value in stats.items():
        print(f"  {key}: {value}")


# 添加新的解析方法到LotteryDataCollector类
def add_17500_desc_support():
    """为LotteryDataCollector类添加17500 desc格式支持"""

    def parse_17500_desc_format(self, raw_data: str) -> List[Dict]:
        """
        解析17500.cn的3d_desc.txt格式数据
        格式：期号 日期 开奖号码 试机号码
        示例：2025204 2025-07-23 497 728
        """
        if not raw_data:
            return []

        parsed_records = []
        lines = raw_data.strip().split('\n')

        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if not line:
                continue

            try:
                # 使用正则表达式解析数据
                # 格式：期号 日期 开奖号码 试机号码
                pattern = r'(\d{7})\s+(\d{4}-\d{2}-\d{2})\s+(\d{3})\s+(\d{3})'
                match = re.match(pattern, line)

                if match:
                    period, date, numbers, trial_numbers = match.groups()

                    # 验证数据有效性
                    if self._validate_lottery_data(period, date, numbers, trial_numbers):
                        # 解析开奖号码
                        hundreds = int(numbers[0])
                        tens = int(numbers[1])
                        units = int(numbers[2])

                        # 解析试机号码
                        trial_hundreds = int(trial_numbers[0])
                        trial_tens = int(trial_numbers[1])
                        trial_units = int(trial_numbers[2])

                        # 计算和值、跨度等
                        sum_value = hundreds + tens + units
                        trial_sum_value = trial_hundreds + trial_tens + trial_units
                        span_value = max(hundreds, tens, units) - min(hundreds, tens, units)
                        trial_span_value = max(trial_hundreds, trial_tens, trial_units) - min(trial_hundreds, trial_tens, trial_units)

                        record = {
                            'period': period,
                            'date': date,
                            'numbers': numbers,
                            'trial_numbers': trial_numbers,
                            'draw_machine': 0,  # 默认值，实际数据源中没有
                            'trial_machine': 0,  # 默认值
                            'sales_amount': 0,  # 默认值
                            'direct_prize': 0,  # 默认值
                            'group3_prize': 0,  # 默认值
                            'group6_prize': 0,  # 默认值
                            'unknown_field1': 0,
                            'unknown_field2': 0,
                            'unknown_field3': 0,
                            'sum_value': sum_value,
                            'trial_sum_value': trial_sum_value,
                            'span_value': span_value,
                            'trial_span_value': trial_span_value
                        }

                        parsed_records.append(record)

            except Exception as e:
                logger.warning(f"解析第{line_num}行数据失败: {line[:50]}... 错误: {e}")
                continue

        logger.info(f"成功解析 {len(parsed_records)} 条记录")
        return parsed_records

    def _validate_lottery_data(self, period: str, date: str, numbers: str, trial_numbers: str) -> bool:
        """验证彩票数据的有效性"""
        try:
            # 验证期号格式（7位数字）
            if not re.match(r'^\d{7}$', period):
                return False

            # 验证期号范围（2002001-2030365）
            period_num = int(period)
            if period_num < 2002001 or period_num > 2030365:
                return False

            # 验证日期格式
            if not re.match(r'^\d{4}-\d{2}-\d{2}$', date):
                return False

            # 验证开奖号码格式（3位数字）
            if not re.match(r'^\d{3}$', numbers):
                return False

            # 验证试机号码格式（3位数字）
            if not re.match(r'^\d{3}$', trial_numbers):
                return False

            return True

        except Exception:
            return False

    def get_optimal_data_source(self, collection_type: str = 'incremental') -> str:
        """
        根据采集类型选择最优数据源

        Args:
            collection_type: 'complete' (完整采集) 或 'incremental' (增量更新)

        Returns:
            最优数据源URL
        """
        primary_sources = self.config.get('primary_sources', {})

        if collection_type == 'complete':
            # 完整采集：使用正序数据源，从历史到最新
            return primary_sources.get('complete_collection',
                                     "https://data.17500.cn/3d_asc.txt")
        else:
            # 增量更新：使用倒序数据源，最新数据在前
            return primary_sources.get('incremental_update',
                                     "https://data.17500.cn/3d_desc.txt")

    def get_backup_data_source(self) -> str:
        """获取备用17500数据源"""
        primary_sources = self.config.get('primary_sources', {})
        return primary_sources.get('backup_17500',
                                 "https://data.17500.cn/3d_desc.txt")

    def collect_with_optimal_source(self, collection_type: str = 'incremental',
                                   max_records: int = None) -> List[Dict]:
        """
        使用最优数据源进行采集

        Args:
            collection_type: 采集类型
            max_records: 最大记录数（None表示全部）

        Returns:
            采集到的记录列表
        """
        # 选择最优数据源
        optimal_url = self.get_optimal_data_source(collection_type)
        logger.info(f"🎯 使用最优数据源进行{collection_type}采集: {optimal_url}")

        try:
            # 尝试主要数据源
            response = self.safe_request(optimal_url)
            if response and response.status_code == 200:
                content = response.text
                records = self.parse_17500_format(content)

                # 如果指定了最大记录数，进行截取
                if max_records and len(records) > max_records:
                    if collection_type == 'incremental':
                        # 增量更新：取最新的记录
                        records = records[:max_records]
                        logger.info(f"📊 增量更新：获取最新 {max_records} 条记录")
                    else:
                        # 完整采集：取全部或指定数量
                        records = records[:max_records]
                        logger.info(f"📊 完整采集：获取 {max_records} 条记录")

                logger.info(f"✅ 成功采集 {len(records)} 条记录")
                return records

        except Exception as e:
            logger.warning(f"⚠️ 主要数据源失败: {e}")

        # 尝试备用数据源
        try:
            backup_url = self.get_backup_data_source()
            logger.info(f"🔄 尝试备用数据源: {backup_url}")

            response = self.safe_request(backup_url)
            if response and response.status_code == 200:
                content = response.text
                records = self.parse_17500_format(content)

                if max_records and len(records) > max_records:
                    records = records[:max_records]

                logger.info(f"✅ 备用数据源成功采集 {len(records)} 条记录")
                return records

        except Exception as e:
            logger.error(f"❌ 备用数据源也失败: {e}")

        logger.error("❌ 所有17500数据源都不可用")
        return []
