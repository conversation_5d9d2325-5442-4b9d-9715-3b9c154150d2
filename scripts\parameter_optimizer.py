#!/usr/bin/env python3
"""
参数调优脚本

根据试运行数据调整融合权重、排序策略等基础参数
优化P8智能交集融合系统的性能

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import os
import sys
import json
import yaml
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

class ParameterOptimizer:
    """参数优化器"""
    
    def __init__(self):
        """初始化参数优化器"""
        self.project_root = Path(__file__).parent.parent
        self.config_dir = self.project_root / "config"
        self.reports_dir = self.project_root / "reports"
        self.logs_dir = self.project_root / "logs"
        
        # 确保目录存在
        self.reports_dir.mkdir(exist_ok=True)
        self.logs_dir.mkdir(exist_ok=True)
        
        # 设置日志
        self._setup_logging()
        self.logger = logging.getLogger(__name__)
        
        # 配置文件路径
        self.fusion_config_path = self.config_dir / "fusion_config.yaml"
        self.backup_config_path = self.config_dir / "fusion_config_backup.yaml"
        
        # 优化结果
        self.optimization_results = {
            'optimization_time': datetime.now().isoformat(),
            'original_config': {},
            'optimized_config': {},
            'improvements': {},
            'recommendations': []
        }
    
    def _setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler(self.logs_dir / 'parameter_optimizer.log')
            ]
        )
    
    def load_current_config(self) -> Dict[str, Any]:
        """加载当前配置"""
        try:
            if self.fusion_config_path.exists():
                with open(self.fusion_config_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                self.logger.info("成功加载当前融合配置")
                return config
            else:
                self.logger.warning("融合配置文件不存在，使用默认配置")
                return self._get_default_config()
                
        except Exception as e:
            self.logger.error(f"加载配置失败: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'fusion': {
                'algorithms': {
                    'weighted_product': {'weight': 0.25, 'enabled': True},
                    'weighted_average': {'weight': 0.20, 'enabled': True},
                    'adaptive_fusion': {'weight': 0.20, 'enabled': True},
                    'bayesian_fusion': {'weight': 0.15, 'enabled': True},
                    'entropy_weighted': {'weight': 0.10, 'enabled': True},
                    'confidence_weighted': {'weight': 0.10, 'enabled': True}
                },
                'predictor_weights': {
                    'hundreds_predictor': 0.18,
                    'tens_predictor': 0.18,
                    'units_predictor': 0.18,
                    'sum_predictor': 0.23,
                    'span_predictor': 0.23
                },
                'ranking_strategies': {
                    'probability_score': {'weight': 0.30, 'enabled': True},
                    'confidence_score': {'weight': 0.25, 'enabled': True},
                    'constraint_score': {'weight': 0.20, 'enabled': True},
                    'diversity_score': {'weight': 0.15, 'enabled': True},
                    'historical_score': {'weight': 0.10, 'enabled': True}
                }
            },
            'constraints': {
                'sum_range': {'min': 0, 'max': 27, 'weight': 0.4},
                'span_range': {'min': 0, 'max': 9, 'weight': 0.3},
                'pattern_consistency': {'weight': 0.3}
            },
            'performance': {
                'target_accuracy': 0.25,
                'target_top10_rate': 0.65,
                'max_response_time': 2.0
            }
        }
    
    def analyze_performance_data(self) -> Dict[str, Any]:
        """分析性能数据"""
        analysis = {
            'data_sources': [],
            'performance_metrics': {},
            'bottlenecks': [],
            'recommendations': []
        }
        
        try:
            # 查找性能报告文件
            report_files = list(self.reports_dir.glob("*performance*.json"))
            report_files.extend(list(self.reports_dir.glob("*test*.json")))
            
            if not report_files:
                self.logger.warning("未找到性能数据文件")
                return analysis
            
            # 分析最新的报告文件
            latest_report = max(report_files, key=lambda x: x.stat().st_mtime)
            
            with open(latest_report, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            analysis['data_sources'].append(str(latest_report))
            
            # 提取性能指标
            if 'statistics' in data:
                stats = data['statistics']
                analysis['performance_metrics'] = {
                    'avg_cpu_usage': stats.get('cpu', {}).get('avg', 0),
                    'avg_memory_usage': stats.get('memory', {}).get('avg', 0),
                    'max_cpu_usage': stats.get('cpu', {}).get('max', 0),
                    'max_memory_usage': stats.get('memory', {}).get('max', 0)
                }
            
            # 分析瓶颈
            metrics = analysis['performance_metrics']
            if metrics.get('avg_cpu_usage', 0) > 70:
                analysis['bottlenecks'].append("CPU使用率较高")
                analysis['recommendations'].append("优化算法复杂度，减少CPU密集型操作")
            
            if metrics.get('avg_memory_usage', 0) > 80:
                analysis['bottlenecks'].append("内存使用率较高")
                analysis['recommendations'].append("优化内存使用，增加数据缓存策略")
            
            self.logger.info(f"分析了性能数据文件: {latest_report.name}")
            
        except Exception as e:
            self.logger.error(f"分析性能数据失败: {e}")
        
        return analysis
    
    def optimize_fusion_weights(self, current_config: Dict[str, Any], 
                               performance_data: Dict[str, Any]) -> Dict[str, Any]:
        """优化融合权重"""
        optimized_config = current_config.copy()
        
        try:
            # 基于性能数据调整融合算法权重
            fusion_algorithms = optimized_config['fusion']['algorithms']
            
            # 如果CPU使用率高，降低复杂算法的权重
            if performance_data.get('performance_metrics', {}).get('avg_cpu_usage', 0) > 70:
                # 降低贝叶斯融合和自适应融合的权重
                fusion_algorithms['bayesian_fusion']['weight'] *= 0.8
                fusion_algorithms['adaptive_fusion']['weight'] *= 0.9
                
                # 增加简单算法的权重
                fusion_algorithms['weighted_average']['weight'] *= 1.1
                fusion_algorithms['weighted_product']['weight'] *= 1.05
                
                self.logger.info("基于CPU使用率调整了融合算法权重")
            
            # 重新归一化权重
            total_weight = sum(alg['weight'] for alg in fusion_algorithms.values() if alg['enabled'])
            for alg in fusion_algorithms.values():
                if alg['enabled']:
                    alg['weight'] /= total_weight
            
            # 优化预测器权重
            predictor_weights = optimized_config['fusion']['predictor_weights']
            
            # 基于历史表现调整权重（这里使用启发式规则）
            # 在实际应用中，应该基于真实的准确率数据
            if 'sum_predictor' in predictor_weights and 'span_predictor' in predictor_weights:
                # 和值和跨度预测器通常更稳定，稍微增加权重
                predictor_weights['sum_predictor'] *= 1.05
                predictor_weights['span_predictor'] *= 1.05
                
                # 位置预测器权重稍微降低
                for key in ['hundreds_predictor', 'tens_predictor', 'units_predictor']:
                    if key in predictor_weights:
                        predictor_weights[key] *= 0.98
            
            # 重新归一化预测器权重
            total_weight = sum(predictor_weights.values())
            for key in predictor_weights:
                predictor_weights[key] /= total_weight
            
            self.logger.info("完成融合权重优化")
            
        except Exception as e:
            self.logger.error(f"优化融合权重失败: {e}")
        
        return optimized_config
    
    def optimize_ranking_strategies(self, current_config: Dict[str, Any]) -> Dict[str, Any]:
        """优化排序策略"""
        optimized_config = current_config.copy()
        
        try:
            ranking_strategies = optimized_config['fusion']['ranking_strategies']
            
            # 基于经验调整排序策略权重
            # 概率分数是最重要的，保持较高权重
            ranking_strategies['probability_score']['weight'] = 0.35
            
            # 约束分数对于福彩3D很重要
            ranking_strategies['constraint_score']['weight'] = 0.25
            
            # 置信度分数
            ranking_strategies['confidence_score']['weight'] = 0.20
            
            # 多样性和历史分数权重较低
            ranking_strategies['diversity_score']['weight'] = 0.12
            ranking_strategies['historical_score']['weight'] = 0.08
            
            self.logger.info("完成排序策略优化")
            
        except Exception as e:
            self.logger.error(f"优化排序策略失败: {e}")
        
        return optimized_config
    
    def optimize_constraints(self, current_config: Dict[str, Any]) -> Dict[str, Any]:
        """优化约束参数"""
        optimized_config = current_config.copy()
        
        try:
            constraints = optimized_config['constraints']
            
            # 基于福彩3D的特点调整约束权重
            # 和值约束最重要
            constraints['sum_range']['weight'] = 0.45
            
            # 跨度约束次之
            constraints['span_range']['weight'] = 0.35
            
            # 模式一致性
            constraints['pattern_consistency']['weight'] = 0.20
            
            self.logger.info("完成约束参数优化")
            
        except Exception as e:
            self.logger.error(f"优化约束参数失败: {e}")
        
        return optimized_config
    
    def calculate_improvements(self, original_config: Dict[str, Any], 
                             optimized_config: Dict[str, Any]) -> Dict[str, Any]:
        """计算改进情况"""
        improvements = {
            'fusion_algorithms': {},
            'predictor_weights': {},
            'ranking_strategies': {},
            'constraints': {},
            'summary': []
        }
        
        try:
            # 比较融合算法权重变化
            orig_fusion = original_config['fusion']['algorithms']
            opt_fusion = optimized_config['fusion']['algorithms']
            
            for alg_name in orig_fusion:
                if alg_name in opt_fusion:
                    orig_weight = orig_fusion[alg_name]['weight']
                    opt_weight = opt_fusion[alg_name]['weight']
                    change = (opt_weight - orig_weight) / orig_weight * 100
                    
                    improvements['fusion_algorithms'][alg_name] = {
                        'original': orig_weight,
                        'optimized': opt_weight,
                        'change_percent': change
                    }
                    
                    if abs(change) > 5:  # 变化超过5%
                        direction = "增加" if change > 0 else "减少"
                        improvements['summary'].append(
                            f"{alg_name} 权重{direction} {abs(change):.1f}%"
                        )
            
            # 比较预测器权重变化
            orig_pred = original_config['fusion']['predictor_weights']
            opt_pred = optimized_config['fusion']['predictor_weights']
            
            for pred_name in orig_pred:
                if pred_name in opt_pred:
                    orig_weight = orig_pred[pred_name]
                    opt_weight = opt_pred[pred_name]
                    change = (opt_weight - orig_weight) / orig_weight * 100
                    
                    improvements['predictor_weights'][pred_name] = {
                        'original': orig_weight,
                        'optimized': opt_weight,
                        'change_percent': change
                    }
            
            # 比较排序策略变化
            orig_rank = original_config['fusion']['ranking_strategies']
            opt_rank = optimized_config['fusion']['ranking_strategies']
            
            for strategy_name in orig_rank:
                if strategy_name in opt_rank:
                    orig_weight = orig_rank[strategy_name]['weight']
                    opt_weight = opt_rank[strategy_name]['weight']
                    change = (opt_weight - orig_weight) / orig_weight * 100
                    
                    improvements['ranking_strategies'][strategy_name] = {
                        'original': orig_weight,
                        'optimized': opt_weight,
                        'change_percent': change
                    }
                    
                    if abs(change) > 10:  # 变化超过10%
                        direction = "增加" if change > 0 else "减少"
                        improvements['summary'].append(
                            f"{strategy_name} 权重{direction} {abs(change):.1f}%"
                        )
            
        except Exception as e:
            self.logger.error(f"计算改进情况失败: {e}")
        
        return improvements
    
    def backup_current_config(self):
        """备份当前配置"""
        try:
            if self.fusion_config_path.exists():
                import shutil
                shutil.copy2(self.fusion_config_path, self.backup_config_path)
                self.logger.info(f"配置已备份到: {self.backup_config_path}")
            
        except Exception as e:
            self.logger.error(f"备份配置失败: {e}")
    
    def save_optimized_config(self, optimized_config: Dict[str, Any]):
        """保存优化后的配置"""
        try:
            with open(self.fusion_config_path, 'w', encoding='utf-8') as f:
                yaml.dump(optimized_config, f, default_flow_style=False, 
                         allow_unicode=True, indent=2)
            
            self.logger.info(f"优化配置已保存到: {self.fusion_config_path}")
            
        except Exception as e:
            self.logger.error(f"保存优化配置失败: {e}")
    
    def run_optimization(self) -> Dict[str, Any]:
        """运行参数优化"""
        self.logger.info("开始基础参数调优")
        
        # 1. 备份当前配置
        self.backup_current_config()
        
        # 2. 加载当前配置
        current_config = self.load_current_config()
        self.optimization_results['original_config'] = current_config.copy()
        
        # 3. 分析性能数据
        performance_data = self.analyze_performance_data()
        
        # 4. 优化融合权重
        optimized_config = self.optimize_fusion_weights(current_config, performance_data)
        
        # 5. 优化排序策略
        optimized_config = self.optimize_ranking_strategies(optimized_config)
        
        # 6. 优化约束参数
        optimized_config = self.optimize_constraints(optimized_config)
        
        # 7. 计算改进情况
        improvements = self.calculate_improvements(current_config, optimized_config)
        self.optimization_results['optimized_config'] = optimized_config
        self.optimization_results['improvements'] = improvements
        
        # 8. 生成建议
        recommendations = performance_data.get('recommendations', [])
        recommendations.extend([
            "定期监控系统性能指标",
            "根据实际预测准确率调整权重",
            "考虑增加更多历史数据进行训练"
        ])
        self.optimization_results['recommendations'] = recommendations
        
        # 9. 保存优化配置
        self.save_optimized_config(optimized_config)
        
        # 10. 保存优化报告
        self._save_optimization_report()
        
        return self.optimization_results
    
    def _save_optimization_report(self):
        """保存优化报告"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = self.reports_dir / f"parameter_optimization_{timestamp}.json"
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.optimization_results, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"优化报告已保存到: {filename}")
            
        except Exception as e:
            self.logger.error(f"保存优化报告失败: {e}")


def main():
    """主函数"""
    print("🚀 开始基础参数调优...")
    print("=" * 50)
    
    optimizer = ParameterOptimizer()
    results = optimizer.run_optimization()
    
    # 输出优化结果
    print("\n📊 参数优化结果:")
    
    improvements = results.get('improvements', {})
    if improvements.get('summary'):
        print("主要改进:")
        for improvement in improvements['summary']:
            print(f"  • {improvement}")
    else:
        print("  • 配置已优化，权重微调完成")
    
    recommendations = results.get('recommendations', [])
    if recommendations:
        print("\n💡 优化建议:")
        for i, rec in enumerate(recommendations[:5], 1):
            print(f"  {i}. {rec}")
    
    print("\n✅ 基础参数调优完成！")
    print("配置文件已更新，建议进行测试验证。")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
