#!/usr/bin/env python3
"""
个位预测器数据访问层

提供个位预测器相关的数据库操作功能，包括：
- 预测结果的保存和查询
- 模型性能数据的管理
- 历史数据的统计分析

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import sqlite3
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from pathlib import Path

class TensDataAccess:
    """十位预测器数据访问类"""
    
    def __init__(self, db_path: str):
        """
        初始化数据访问层
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = db_path
        self.logger = logging.getLogger("TensDataAccess")
        
        # 验证数据库文件存在
        if not Path(db_path).exists():
            raise FileNotFoundError(f"数据库文件不存在: {db_path}")
        
        # 验证必要的表是否存在
        self._verify_tables()
    
    def _verify_tables(self):
        """验证必要的表是否存在"""
        required_tables = ['tens_predictions', 'tens_model_performance']
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            existing_tables = [row[0] for row in cursor.fetchall()]
            
            for table in required_tables:
                if table not in existing_tables:
                    self.logger.warning(f"表 {table} 不存在，将尝试创建")
                    self._create_missing_table(cursor, table)
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"表验证失败: {e}")
            raise
    
    def _create_missing_table(self, cursor: sqlite3.Cursor, table_name: str):
        """创建缺失的表"""
        if table_name == 'tens_predictions':
            cursor.execute("""
            CREATE TABLE tens_predictions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                issue TEXT NOT NULL,
                model_type TEXT NOT NULL,
                prob_0 REAL NOT NULL,
                prob_1 REAL NOT NULL,
                prob_2 REAL NOT NULL,
                prob_3 REAL NOT NULL,
                prob_4 REAL NOT NULL,
                prob_5 REAL NOT NULL,
                prob_6 REAL NOT NULL,
                prob_7 REAL NOT NULL,
                prob_8 REAL NOT NULL,
                prob_9 REAL NOT NULL,
                predicted_digit INTEGER,
                confidence REAL,
                feature_count INTEGER,
                training_samples INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(issue, model_type)
            )
            """)
        elif table_name == 'tens_model_performance':
            cursor.execute("""
            CREATE TABLE tens_model_performance (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                model_type TEXT NOT NULL,
                evaluation_period TEXT NOT NULL,
                accuracy REAL NOT NULL,
                top3_accuracy REAL NOT NULL,
                avg_confidence REAL NOT NULL,
                precision_per_digit TEXT,
                recall_per_digit TEXT,
                f1_score_per_digit TEXT,
                confusion_matrix TEXT,
                feature_importance TEXT,
                training_time REAL,
                prediction_time REAL,
                model_size INTEGER,
                evaluated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """)
    
    def save_prediction_result(self, prediction_result: Dict[str, Any]) -> bool:
        """
        保存预测结果
        
        Args:
            prediction_result: 预测结果字典
            
        Returns:
            是否保存成功
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 提取概率分布
            probabilities = prediction_result['probabilities']
            
            cursor.execute("""
            INSERT OR REPLACE INTO tens_predictions (
                issue, model_type, prob_0, prob_1, prob_2, prob_3, prob_4,
                prob_5, prob_6, prob_7, prob_8, prob_9, predicted_digit,
                confidence, feature_count, training_samples
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                prediction_result['issue'],
                prediction_result.get('model_type', 'unknown'),
                probabilities[0], probabilities[1], probabilities[2],
                probabilities[3], probabilities[4], probabilities[5],
                probabilities[6], probabilities[7], probabilities[8],
                probabilities[9],
                prediction_result['predicted_digit'],
                prediction_result['confidence'],
                prediction_result.get('feature_count', 0),
                prediction_result.get('training_samples', 0)
            ))
            
            conn.commit()
            conn.close()
            
            self.logger.info(f"预测结果保存成功: {prediction_result['issue']}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存预测结果失败: {e}")
            return False
    
    def get_prediction_history(self, model_type: Optional[str] = None, 
                             limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        获取预测历史
        
        Args:
            model_type: 模型类型过滤
            limit: 限制返回数量
            
        Returns:
            预测历史列表
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            query = """
            SELECT issue, model_type, prob_0, prob_1, prob_2, prob_3, prob_4,
                   prob_5, prob_6, prob_7, prob_8, prob_9, predicted_digit,
                   confidence, feature_count, training_samples, created_at
            FROM tens_predictions
            """
            
            params = []
            if model_type:
                query += " WHERE model_type = ?"
                params.append(model_type)
            
            query += " ORDER BY created_at DESC"
            
            if limit:
                query += " LIMIT ?"
                params.append(limit)
            
            cursor.execute(query, params)
            rows = cursor.fetchall()
            
            results = []
            for row in rows:
                probabilities = [row[2+i] for i in range(10)]  # prob_0 到 prob_9
                
                results.append({
                    'issue': row[0],
                    'model_type': row[1],
                    'probabilities': probabilities,
                    'predicted_digit': row[12],
                    'confidence': row[13],
                    'feature_count': row[14],
                    'training_samples': row[15],
                    'created_at': row[16]
                })
            
            conn.close()
            return results
            
        except Exception as e:
            self.logger.error(f"获取预测历史失败: {e}")
            return []
    
    def save_performance_metrics(self, performance_data: Dict[str, Any]) -> bool:
        """
        保存模型性能指标
        
        Args:
            performance_data: 性能数据字典
            
        Returns:
            是否保存成功
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
            INSERT INTO tens_model_performance (
                model_type, evaluation_period, accuracy, top3_accuracy,
                avg_confidence, precision_per_digit, recall_per_digit,
                f1_score_per_digit, confusion_matrix, feature_importance,
                training_time, prediction_time, model_size
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                performance_data['model_type'],
                performance_data['evaluation_period'],
                performance_data['accuracy'],
                performance_data['top3_accuracy'],
                performance_data['avg_confidence'],
                json.dumps(performance_data.get('precision_per_digit', {})),
                json.dumps(performance_data.get('recall_per_digit', {})),
                json.dumps(performance_data.get('f1_score_per_digit', {})),
                json.dumps(performance_data.get('confusion_matrix', [])),
                json.dumps(performance_data.get('feature_importance', {})),
                performance_data.get('training_time', 0.0),
                performance_data.get('prediction_time', 0.0),
                performance_data.get('model_size', 0)
            ))
            
            conn.commit()
            conn.close()
            
            self.logger.info(f"性能指标保存成功: {performance_data['model_type']}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存性能指标失败: {e}")
            return False
    
    def get_performance_history(self, model_type: Optional[str] = None,
                              days: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        获取性能历史
        
        Args:
            model_type: 模型类型过滤
            days: 最近天数过滤
            
        Returns:
            性能历史列表
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            query = """
            SELECT model_type, evaluation_period, accuracy, top3_accuracy,
                   avg_confidence, precision_per_digit, recall_per_digit,
                   f1_score_per_digit, confusion_matrix, feature_importance,
                   training_time, prediction_time, model_size, evaluated_at
            FROM tens_model_performance
            """
            
            params = []
            conditions = []
            
            if model_type:
                conditions.append("model_type = ?")
                params.append(model_type)
            
            if days:
                cutoff_date = datetime.now() - timedelta(days=days)
                conditions.append("evaluated_at >= ?")
                params.append(cutoff_date.isoformat())
            
            if conditions:
                query += " WHERE " + " AND ".join(conditions)
            
            query += " ORDER BY evaluated_at DESC"
            
            cursor.execute(query, params)
            rows = cursor.fetchall()
            
            results = []
            for row in rows:
                results.append({
                    'model_type': row[0],
                    'evaluation_period': row[1],
                    'accuracy': row[2],
                    'top3_accuracy': row[3],
                    'avg_confidence': row[4],
                    'precision_per_digit': json.loads(row[5]) if row[5] else {},
                    'recall_per_digit': json.loads(row[6]) if row[6] else {},
                    'f1_score_per_digit': json.loads(row[7]) if row[7] else {},
                    'confusion_matrix': json.loads(row[8]) if row[8] else [],
                    'feature_importance': json.loads(row[9]) if row[9] else {},
                    'training_time': row[10],
                    'prediction_time': row[11],
                    'model_size': row[12],
                    'evaluated_at': row[13]
                })
            
            conn.close()
            return results
            
        except Exception as e:
            self.logger.error(f"获取性能历史失败: {e}")
            return []
    
    def get_accuracy_statistics(self, model_type: Optional[str] = None,
                               recent_periods: int = 100) -> Dict[str, float]:
        """
        获取准确率统计
        
        Args:
            model_type: 模型类型
            recent_periods: 最近期数
            
        Returns:
            统计结果字典
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取实际开奖结果进行对比
            query = """
            SELECT tp.predicted_digit, ld.tens, tp.confidence
            FROM tens_predictions tp
            JOIN lottery_data ld ON tp.issue = ld.issue
            """
            
            params = []
            if model_type:
                query += " WHERE tp.model_type = ?"
                params.append(model_type)
            
            query += " ORDER BY tp.created_at DESC"
            
            if recent_periods:
                query += " LIMIT ?"
                params.append(recent_periods)
            
            cursor.execute(query, params)
            rows = cursor.fetchall()
            
            if not rows:
                return {'accuracy': 0.0, 'avg_confidence': 0.0, 'total_predictions': 0}
            
            correct_predictions = sum(1 for row in rows if row[0] == row[1])
            total_predictions = len(rows)
            avg_confidence = sum(row[2] for row in rows) / total_predictions
            
            accuracy = correct_predictions / total_predictions
            
            conn.close()
            
            return {
                'accuracy': accuracy,
                'avg_confidence': avg_confidence,
                'total_predictions': total_predictions,
                'correct_predictions': correct_predictions
            }
            
        except Exception as e:
            self.logger.error(f"获取准确率统计失败: {e}")
            return {'accuracy': 0.0, 'avg_confidence': 0.0, 'total_predictions': 0}
    
    def cleanup_old_data(self, days_to_keep: int = 365) -> int:
        """
        清理旧数据
        
        Args:
            days_to_keep: 保留天数
            
        Returns:
            删除的记录数
        """
        try:
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 清理旧的预测结果
            cursor.execute("""
            DELETE FROM tens_predictions 
            WHERE created_at < ?
            """, (cutoff_date.isoformat(),))
            
            deleted_predictions = cursor.rowcount
            
            # 清理旧的性能数据
            cursor.execute("""
            DELETE FROM tens_model_performance 
            WHERE evaluated_at < ?
            """, (cutoff_date.isoformat(),))
            
            deleted_performance = cursor.rowcount
            
            conn.commit()
            conn.close()
            
            total_deleted = deleted_predictions + deleted_performance
            self.logger.info(f"清理完成: 删除 {total_deleted} 条记录")
            
            return total_deleted
            
        except Exception as e:
            self.logger.error(f"数据清理失败: {e}")
            return 0
