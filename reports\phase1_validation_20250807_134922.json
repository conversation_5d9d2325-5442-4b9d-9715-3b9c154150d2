{"phase": "Phase 1 - System Validation", "start_time": "2025-08-07T13:48:56.082521", "tasks": [{"task": "1.1 集成测试", "success": false, "output": "正在初始化P8智能交集融合系统...\n错误: 系统初始化失败: 'gbk' codec can't encode character '\\u2713' in position 0: illegal multibyte sequence\n", "error": "INFO:src.data.feature_importance:SHAP库导入成功\n2025-08-07 13:49:00.889338: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.\n2025-08-07 13:49:02.812108: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.\nINFO:src.fusion.fusion_predictor:加载配置文件: config/fusion_config.yaml\nINFO:src.predictors.unified_predictor_interface:统一预测器接口管理器初始化完成\nINFO:src.fusion.probability_fusion_engine:概率融合引擎初始化完成\nINFO:src.fusion.constraint_optimizer:约束优化器初始化完成\nINFO:src.fusion.intelligent_ranker:智能排序器初始化完成\nINFO:src.fusion.dynamic_weight_adjuster:动态权重调整器初始化完成\nINFO:src.fusion.fusion_predictor:所有融合组件初始化完成\nINFO:src.fusion.fusion_predictor:P8智能交集融合系统初始化完成\nINFO:src.fusion.report_generator:评估报告生成器初始化完成\n", "execution_time": 1754545747.7346537}, {"task": "1.2 性能基准测试", "success": false, "output": "正在初始化P8智能交集融合系统...\n错误: 系统初始化失败: 'gbk' codec can't encode character '\\u2713' in position 0: illegal multibyte sequence\n", "error": "INFO:src.data.feature_importance:SHAP库导入成功\n2025-08-07 13:49:10.478389: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.\n2025-08-07 13:49:11.698551: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.\nINFO:src.fusion.fusion_predictor:加载配置文件: config/fusion_config.yaml\nINFO:src.predictors.unified_predictor_interface:统一预测器接口管理器初始化完成\nINFO:src.fusion.probability_fusion_engine:概率融合引擎初始化完成\nINFO:src.fusion.constraint_optimizer:约束优化器初始化完成\nINFO:src.fusion.intelligent_ranker:智能排序器初始化完成\nINFO:src.fusion.dynamic_weight_adjuster:动态权重调整器初始化完成\nINFO:src.fusion.fusion_predictor:所有融合组件初始化完成\nINFO:src.fusion.fusion_predictor:P8智能交集融合系统初始化完成\nINFO:src.fusion.report_generator:评估报告生成器初始化完成\n", "execution_time": 1754545755.238097}, {"task": "1.3 数据库验证", "success": false, "output": "正在初始化P8智能交集融合系统...\n错误: 系统初始化失败: 'gbk' codec can't encode character '\\u2713' in position 0: illegal multibyte sequence\n", "error": "INFO:src.data.feature_importance:SHAP库导入成功\n2025-08-07 13:49:17.919624: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.\n2025-08-07 13:49:19.183618: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.\nINFO:src.fusion.fusion_predictor:加载配置文件: config/fusion_config.yaml\nINFO:src.predictors.unified_predictor_interface:统一预测器接口管理器初始化完成\nINFO:src.fusion.probability_fusion_engine:概率融合引擎初始化完成\nINFO:src.fusion.constraint_optimizer:约束优化器初始化完成\nINFO:src.fusion.intelligent_ranker:智能排序器初始化完成\nINFO:src.fusion.dynamic_weight_adjuster:动态权重调整器初始化完成\nINFO:src.fusion.fusion_predictor:所有融合组件初始化完成\nINFO:src.fusion.fusion_predictor:P8智能交集融合系统初始化完成\nINFO:src.fusion.report_generator:评估报告生成器初始化完成\n", "execution_time": 1754545762.7563093}], "end_time": "2025-08-07T13:49:22.756309", "success": false}