#!/usr/bin/env python3
"""
LightGBM十位预测模型

基于LightGBM实现的十位数字预测模型，专注于独立位置预测。

特点：
- 高效的梯度提升算法
- 更快的训练速度
- 支持多分类概率输出
- 内置特征重要性分析
- 支持早停和交叉验证

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import sys
from pathlib import Path
import numpy as np
import logging
import pickle
import time
from typing import Dict, Tuple, Optional, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    import lightgbm as lgb
    from sklearn.model_selection import train_test_split
    from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
    from sklearn.preprocessing import LabelEncoder
except ImportError as e:
    print(f"警告: 缺少必要的机器学习库: {e}")
    print("请安装: pip install lightgbm scikit-learn")

# 导入基类
try:
    from src.predictors.base_independent_predictor import BaseIndependentPredictor
    from config.config_loader import get_config
except ImportError as e:
    print(f"警告: 无法导入基类或配置: {e}")

class LGBTensModel(BaseIndependentPredictor):
    """LightGBM十位预测模型"""
    
    def __init__(self, db_path: str):
        """
        初始化LightGBM十位预测模型
        
        Args:
            db_path: 数据库路径
        """
        super().__init__("tens", db_path)
        
        # 加载LightGBM配置
        self._load_lgb_config()
        
        # 初始化模型相关属性
        self.lgb_model = None
        self.label_encoder = LabelEncoder()
        self.feature_importance_ = None
        self.training_metrics = {}
        
        self.logger.info("LightGBM十位预测模型初始化完成")
    
    def _load_lgb_config(self):
        """加载LightGBM配置"""
        try:
            config_loader = get_config()
            self.lgb_config = config_loader.get_model_config('lightgbm')
        except Exception as e:
            self.logger.warning(f"配置加载失败，使用默认LightGBM配置: {e}")
            self.lgb_config = {
                'objective': 'multiclass',
                'num_class': 10,
                'max_depth': 6,
                'learning_rate': 0.05,
                'n_estimators': 300,
                'subsample': 0.8,
                'colsample_bytree': 0.8,
                'reg_alpha': 0.1,
                'reg_lambda': 1.0,
                'random_state': 42,
                'n_jobs': -1,
                'early_stopping_rounds': 20,
                'metric': 'multi_logloss',
                'verbose': -1
            }
    
    def build_model(self) -> lgb.LGBMClassifier:
        """
        构建LightGBM模型
        
        Returns:
            LightGBM分类器
        """
        try:
            # 创建LightGBM分类器
            self.lgb_model = lgb.LGBMClassifier(
                objective=self.lgb_config['objective'],
                num_class=self.lgb_config['num_class'],
                n_estimators=self.lgb_config['n_estimators'],
                max_depth=self.lgb_config['max_depth'],
                learning_rate=self.lgb_config['learning_rate'],
                subsample=self.lgb_config['subsample'],
                colsample_bytree=self.lgb_config['colsample_bytree'],
                reg_alpha=self.lgb_config['reg_alpha'],
                reg_lambda=self.lgb_config['reg_lambda'],
                random_state=self.lgb_config['random_state'],
                n_jobs=self.lgb_config['n_jobs'],
                verbose=self.lgb_config['verbose']
            )
            
            self.logger.info("LightGBM模型构建完成")
            return self.lgb_model
            
        except Exception as e:
            self.logger.error(f"LightGBM模型构建失败: {e}")
            raise
    
    def train(self, X: np.ndarray, y: np.ndarray) -> Dict[str, Any]:
        """
        训练LightGBM模型
        
        Args:
            X: 特征矩阵
            y: 目标向量
            
        Returns:
            训练结果字典
        """
        try:
            start_time = time.time()
            self.logger.info(f"开始训练LightGBM模型，数据形状: X{X.shape}, y{y.shape}")
            
            # 构建模型
            if self.lgb_model is None:
                self.build_model()
            
            # 数据预处理
            X_processed, y_processed = self._preprocess_data(X, y)
            
            # 分割训练和验证集
            X_train, X_val, y_train, y_val = train_test_split(
                X_processed, y_processed, 
                test_size=self.data_config.get('validation_split', 0.2),
                random_state=42,
                stratify=y_processed
            )
            
            # 训练模型
            eval_set = [(X_val, y_val)]
            
            self.lgb_model.fit(
                X_train, y_train,
                eval_set=eval_set,
                eval_metric=self.lgb_config.get('metric', 'multi_logloss'),
                callbacks=[
                    lgb.early_stopping(self.lgb_config.get('early_stopping_rounds', 20)),
                    lgb.log_evaluation(0)  # 禁用训练日志
                ]
            )
            
            # 计算训练指标
            train_pred = self.lgb_model.predict(X_train)
            val_pred = self.lgb_model.predict(X_val)
            
            train_accuracy = accuracy_score(y_train, train_pred)
            val_accuracy = accuracy_score(y_val, val_pred)
            
            # 获取特征重要性
            self.feature_importance_ = self.lgb_model.feature_importances_
            
            training_time = time.time() - start_time
            
            # 保存训练指标
            self.training_metrics = {
                'train_accuracy': train_accuracy,
                'val_accuracy': val_accuracy,
                'training_time': training_time,
                'n_estimators_used': self.lgb_model.n_estimators,
                'best_iteration': getattr(self.lgb_model, 'best_iteration_', None),
                'feature_importance': self.feature_importance_.tolist() if self.feature_importance_ is not None else []
            }
            
            self.is_trained = True
            self.training_history['lgb'] = self.training_metrics
            
            self.logger.info(f"LightGBM训练完成: 训练准确率={train_accuracy:.4f}, 验证准确率={val_accuracy:.4f}, 耗时={training_time:.2f}s")
            
            return self.training_metrics
            
        except Exception as e:
            self.logger.error(f"LightGBM训练失败: {e}")
            raise
    
    def predict_probability(self, X: np.ndarray) -> np.ndarray:
        """
        预测概率分布
        
        Args:
            X: 特征矩阵
            
        Returns:
            概率分布数组，shape: (n_samples, 10)
        """
        if not self.is_trained or self.lgb_model is None:
            raise ValueError("模型尚未训练")
        
        try:
            # 数据预处理
            X_processed = self._preprocess_features(X)
            
            # 预测概率
            probabilities = self.lgb_model.predict_proba(X_processed)
            
            return probabilities
            
        except Exception as e:
            self.logger.error(f"LightGBM预测失败: {e}")
            raise
    
    def _preprocess_data(self, X: np.ndarray, y: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        数据预处理
        
        Args:
            X: 特征矩阵
            y: 目标向量
            
        Returns:
            预处理后的特征矩阵和目标向量
        """
        # 处理缺失值
        X_processed = np.nan_to_num(X, nan=0.0, posinf=0.0, neginf=0.0)
        
        # 标签编码（确保标签是0-9的整数）
        y_processed = self.label_encoder.fit_transform(y)
        
        return X_processed, y_processed
    
    def _preprocess_features(self, X: np.ndarray) -> np.ndarray:
        """
        特征预处理（仅特征）
        
        Args:
            X: 特征矩阵
            
        Returns:
            预处理后的特征矩阵
        """
        return np.nan_to_num(X, nan=0.0, posinf=0.0, neginf=0.0)
    
    def get_feature_importance(self, top_k: Optional[int] = None) -> Dict[str, float]:
        """
        获取特征重要性
        
        Args:
            top_k: 返回前k个重要特征，None返回全部
            
        Returns:
            特征重要性字典
        """
        if self.feature_importance_ is None:
            return {}
        
        if not self.feature_names:
            feature_names = [f"feature_{i}" for i in range(len(self.feature_importance_))]
        else:
            feature_names = self.feature_names
        
        importance_dict = dict(zip(feature_names, self.feature_importance_))
        
        # 按重要性排序
        sorted_importance = sorted(importance_dict.items(), key=lambda x: x[1], reverse=True)
        
        if top_k:
            sorted_importance = sorted_importance[:top_k]
        
        return dict(sorted_importance)
    
    def save_model(self, filepath: Optional[str] = None) -> str:
        """
        保存LightGBM模型
        
        Args:
            filepath: 保存路径
            
        Returns:
            实际保存路径
        """
        if not self.is_trained:
            raise ValueError("模型尚未训练，无法保存")
        
        if filepath is None:
            model_dir = Path(self.training_config.get('model_save_path', 'models/tens/'))
            model_dir.mkdir(parents=True, exist_ok=True)
            filepath = model_dir / "lgb_tens_model.pkl"
        
        try:
            # 保存模型和相关信息
            model_data = {
                'model': self.lgb_model,
                'label_encoder': self.label_encoder,
                'feature_names': self.feature_names,
                'feature_importance': self.feature_importance_,
                'training_metrics': self.training_metrics,
                'config': self.lgb_config
            }
            
            with open(filepath, 'wb') as f:
                pickle.dump(model_data, f)
            
            self.logger.info(f"LightGBM模型保存成功: {filepath}")
            return str(filepath)
            
        except Exception as e:
            self.logger.error(f"LightGBM模型保存失败: {e}")
            raise
    
    def load_model(self, filepath: str) -> bool:
        """
        加载LightGBM模型
        
        Args:
            filepath: 模型文件路径
            
        Returns:
            是否加载成功
        """
        try:
            with open(filepath, 'rb') as f:
                model_data = pickle.load(f)
            
            self.lgb_model = model_data['model']
            self.label_encoder = model_data['label_encoder']
            self.feature_names = model_data['feature_names']
            self.feature_importance_ = model_data['feature_importance']
            self.training_metrics = model_data['training_metrics']
            
            self.is_trained = True
            
            self.logger.info(f"LightGBM模型加载成功: {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"LightGBM模型加载失败: {e}")
            return False
