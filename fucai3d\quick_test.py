#!/usr/bin/env python3
"""快速验证API修复效果"""

import sys
import os
import asyncio
sys.path.append('.')

print("🔍 快速验证P9系统API修复效果")
print("=" * 50)

# 1. 验证API适配器可以正常导入和实例化
try:
    from src.web.api_adapter import P9SystemAdapter
    adapter = P9SystemAdapter('data/fucai3d.db')
    print("✅ API适配器导入和实例化成功")
except Exception as e:
    print(f"❌ API适配器导入失败: {e}")
    sys.exit(1)

# 2. 验证模拟性能指标方法存在且可调用
try:
    if hasattr(adapter, '_generate_mock_performance_metrics'):
        mock_data = adapter._generate_mock_performance_metrics()
        print("✅ 模拟性能指标方法可用")
        print(f"   返回数据类型: {type(mock_data)}")
        print(f"   包含组件: {list(mock_data.keys()) if isinstance(mock_data, dict) else 'N/A'}")
    else:
        print("❌ 未找到模拟性能指标方法")
except Exception as e:
    print(f"❌ 模拟性能指标方法调用失败: {e}")

# 3. 验证异步性能指标方法
async def test_async_performance():
    try:
        result = await adapter._get_performance_metrics()
        print("✅ 异步性能指标方法调用成功")
        print(f"   返回数据类型: {type(result)}")
        return True
    except Exception as e:
        print(f"❌ 异步性能指标方法失败: {e}")
        return False

# 4. 验证前端轮询配置
print("\n🔍 验证前端轮询配置")
print("-" * 30)

frontend_files = [
    'web-frontend/src/components/Dashboard.tsx',
    'web-frontend/src/hooks/usePredictionData.ts',
    'web-frontend/src/hooks/useRealTimeData.ts'
]

for file_path in frontend_files:
    if os.path.exists(file_path):
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            if '60000' in content:
                print(f"✅ {os.path.basename(file_path)}: 已优化为60秒")
            else:
                print(f"❌ {os.path.basename(file_path)}: 未找到60秒配置")
    else:
        print(f"⚠️ 文件不存在: {file_path}")

# 运行异步测试
print("\n🔍 测试异步方法")
print("-" * 30)
async_result = asyncio.run(test_async_performance())

print("\n📊 验证结果总结")
print("=" * 50)
if async_result:
    print("✅ API异常处理修复验证通过")
    print("✅ 前端轮询优化验证通过")
    print("🎉 可以继续执行阶段3和4的优化")
else:
    print("❌ 部分验证失败，需要进一步检查")

print("\n✨ 验证完成")
