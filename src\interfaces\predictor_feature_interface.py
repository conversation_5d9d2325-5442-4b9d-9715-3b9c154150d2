"""
P2高级特征工程系统 - 预测模型特征接口

创建PredictorFeatureInterface，实现ML就绪特征数据接口，
为机器学习模型提供标准化的特征数据访问和处理功能。

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any, Union
from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime, timedelta
import logging
import os
import sys

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
sys.path.insert(0, project_root)

try:
    from src.data.advanced_feature_engineer import AdvancedFeatureEngineer
    from src.data.feature_service import FeatureService
    from src.data.cache_optimizer import CacheOptimizer, CacheConfig
    from src.data.feature_importance import FeatureImportanceAnalyzer
except ImportError as e:
    print(f"导入模块失败: {e}")

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class FeatureConfig:
    """特征配置"""
    feature_types: List[str] = None  # 特征类型列表
    window_size: int = 30  # 时间窗口大小
    lag_features: List[int] = None  # 滞后特征
    target_encoding: bool = True  # 是否进行目标编码
    feature_selection: bool = True  # 是否进行特征选择
    normalization: str = "standard"  # 标准化方法
    handle_missing: str = "forward_fill"  # 缺失值处理
    
    def __post_init__(self):
        if self.feature_types is None:
            self.feature_types = ['hundreds', 'tens', 'units', 'sum', 'span']
        if self.lag_features is None:
            self.lag_features = [1, 2, 3, 5, 7]


@dataclass
class MLDataset:
    """ML数据集"""
    X: pd.DataFrame  # 特征矩阵
    y: pd.Series  # 目标变量
    feature_names: List[str]  # 特征名称
    issue_mapping: Dict[int, str]  # 索引到期号的映射
    metadata: Dict[str, Any]  # 元数据
    
    def train_test_split(self, test_size: float = 0.2, random_state: int = 42) -> Tuple['MLDataset', 'MLDataset']:
        """分割训练测试集"""
        from sklearn.model_selection import train_test_split
        
        X_train, X_test, y_train, y_test = train_test_split(
            self.X, self.y, test_size=test_size, random_state=random_state
        )
        
        # 创建训练集
        train_indices = X_train.index
        train_mapping = {i: self.issue_mapping[idx] for i, idx in enumerate(train_indices)}
        train_dataset = MLDataset(
            X=X_train.reset_index(drop=True),
            y=y_train.reset_index(drop=True),
            feature_names=self.feature_names,
            issue_mapping=train_mapping,
            metadata={**self.metadata, 'split': 'train'}
        )
        
        # 创建测试集
        test_indices = X_test.index
        test_mapping = {i: self.issue_mapping[idx] for i, idx in enumerate(test_indices)}
        test_dataset = MLDataset(
            X=X_test.reset_index(drop=True),
            y=y_test.reset_index(drop=True),
            feature_names=self.feature_names,
            issue_mapping=test_mapping,
            metadata={**self.metadata, 'split': 'test'}
        )
        
        return train_dataset, test_dataset


class BasePredictorInterface(ABC):
    """预测器接口基类"""
    
    @abstractmethod
    def prepare_features(self, issues: List[str], config: FeatureConfig) -> MLDataset:
        """准备特征数据"""
        pass
    
    @abstractmethod
    def get_target_variable(self, issues: List[str]) -> pd.Series:
        """获取目标变量"""
        pass
    
    @abstractmethod
    def validate_data(self, dataset: MLDataset) -> bool:
        """验证数据质量"""
        pass


class PredictorFeatureInterface(BasePredictorInterface):
    """
    预测模型特征接口
    
    为机器学习模型提供标准化的特征数据访问和处理功能：
    1. 特征数据准备和预处理
    2. 目标变量生成
    3. 数据质量验证
    4. ML就绪数据集创建
    5. 特征工程pipeline
    """
    
    def __init__(self, db_path: str, predictor_type: str = "hundreds"):
        """
        初始化预测器特征接口
        
        Args:
            db_path: 数据库路径
            predictor_type: 预测器类型 (hundreds, tens, units, sum, span)
        """
        self.db_path = db_path
        self.predictor_type = predictor_type
        
        # 初始化组件
        self.feature_engineer = AdvancedFeatureEngineer(db_path, cache_enabled=True)
        self.feature_service = FeatureService(db_path)
        
        # 缓存优化器
        cache_config = CacheConfig(
            memory_size=500,
            db_cache_enabled=True,
            db_cache_path=f"cache/{predictor_type}_predictor_cache.db"
        )
        self.cache_optimizer = CacheOptimizer(cache_config)
        
        # 特征重要性分析器
        self.importance_analyzer = FeatureImportanceAnalyzer()
        
        # 预测器特定配置
        self.predictor_configs = {
            'hundreds': {'target_range': (0, 9), 'feature_focus': ['hundreds', 'common']},
            'tens': {'target_range': (0, 9), 'feature_focus': ['tens', 'common']},
            'units': {'target_range': (0, 9), 'feature_focus': ['units', 'common']},
            'sum': {'target_range': (0, 27), 'feature_focus': ['sum', 'common']},
            'span': {'target_range': (0, 9), 'feature_focus': ['span', 'common']}
        }
        
        logger.info(f"PredictorFeatureInterface初始化完成: {predictor_type}")
    
    def prepare_features(self, issues: List[str], config: FeatureConfig) -> MLDataset:
        """
        准备特征数据
        
        Args:
            issues: 期号列表
            config: 特征配置
            
        Returns:
            MLDataset: ML就绪数据集
        """
        logger.info(f"准备{self.predictor_type}预测器特征数据，期号数量: {len(issues)}")
        
        # 获取预测器特定配置
        predictor_config = self.predictor_configs.get(self.predictor_type, {})
        feature_focus = predictor_config.get('feature_focus', config.feature_types)
        
        # 收集特征数据
        feature_data = []
        valid_issues = []
        
        for issue in issues:
            try:
                # 获取多类型特征
                issue_features = {}
                for feature_type in feature_focus:
                    features = self.feature_engineer.get_features_with_cache(issue, feature_type)
                    if features:
                        # 添加特征类型前缀避免冲突
                        prefixed_features = {f"{feature_type}_{k}": v for k, v in features.items()}
                        issue_features.update(prefixed_features)
                
                if issue_features:
                    # 添加滞后特征
                    if config.lag_features:
                        lag_features = self._create_lag_features(issue, config.lag_features, feature_focus)
                        issue_features.update(lag_features)
                    
                    # 添加时间窗口特征
                    if config.window_size > 1:
                        window_features = self._create_window_features(issue, config.window_size, feature_focus)
                        issue_features.update(window_features)
                    
                    feature_data.append(issue_features)
                    valid_issues.append(issue)
                    
            except Exception as e:
                logger.warning(f"获取期号{issue}特征失败: {e}")
                continue
        
        if not feature_data:
            raise ValueError("没有有效的特征数据")
        
        # 转换为DataFrame
        X = pd.DataFrame(feature_data)
        
        # 处理缺失值
        X = self._handle_missing_values(X, config.handle_missing)
        
        # 特征选择
        if config.feature_selection and len(X) > 20:
            X = self._select_features(X, valid_issues, config)
        
        # 标准化
        if config.normalization:
            X = self._normalize_features(X, config.normalization)
        
        # 获取目标变量
        y = self.get_target_variable(valid_issues)
        
        # 确保X和y长度一致
        min_length = min(len(X), len(y))
        X = X.iloc[:min_length]
        y = y.iloc[:min_length]
        valid_issues = valid_issues[:min_length]
        
        # 创建期号映射
        issue_mapping = {i: issue for i, issue in enumerate(valid_issues)}
        
        # 创建数据集
        dataset = MLDataset(
            X=X,
            y=y,
            feature_names=list(X.columns),
            issue_mapping=issue_mapping,
            metadata={
                'predictor_type': self.predictor_type,
                'feature_config': config.__dict__,
                'total_features': len(X.columns),
                'total_samples': len(X),
                'creation_time': datetime.now().isoformat()
            }
        )
        
        logger.info(f"特征数据准备完成: {len(X)}样本, {len(X.columns)}特征")
        return dataset
    
    def get_target_variable(self, issues: List[str]) -> pd.Series:
        """
        获取目标变量
        
        Args:
            issues: 期号列表
            
        Returns:
            pd.Series: 目标变量
        """
        target_data = []
        
        for issue in issues:
            try:
                # 直接从数据库获取开奖号码
                import sqlite3
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()

                cursor.execute("""
                    SELECT hundreds, tens, units, sum_value, span
                    FROM lottery_data WHERE issue = ?
                """, (issue,))

                row = cursor.fetchone()
                conn.close()

                if row:
                    h, t, u, sum_val, span_val = row
                    if self.predictor_type == 'hundreds':
                        target = h
                    elif self.predictor_type == 'tens':
                        target = t
                    elif self.predictor_type == 'units':
                        target = u
                    elif self.predictor_type == 'sum':
                        target = sum_val if sum_val is not None else (h + t + u)
                    elif self.predictor_type == 'span':
                        target = span_val if span_val is not None else (max(h, t, u) - min(h, t, u))
                    else:
                        target = 0

                    target_data.append(target)
                else:
                    target_data.append(None)

            except Exception as e:
                logger.warning(f"获取期号{issue}目标变量失败: {e}")
                target_data.append(None)
        
        # 转换为Series并处理缺失值
        y = pd.Series(target_data)
        y = y.fillna(method='ffill').fillna(0)
        
        return y

    def validate_data(self, dataset: MLDataset) -> bool:
        """
        验证数据质量

        Args:
            dataset: ML数据集

        Returns:
            bool: 数据是否有效
        """
        try:
            # 检查基本结构
            if dataset.X.empty or dataset.y.empty:
                logger.error("数据集为空")
                return False

            if len(dataset.X) != len(dataset.y):
                logger.error(f"特征和目标变量长度不匹配: {len(dataset.X)} vs {len(dataset.y)}")
                return False

            # 检查缺失值
            missing_ratio = dataset.X.isnull().sum().sum() / (len(dataset.X) * len(dataset.X.columns))
            if missing_ratio > 0.1:  # 超过10%缺失值
                logger.warning(f"缺失值比例较高: {missing_ratio:.2%}")

            # 检查目标变量范围
            predictor_config = self.predictor_configs.get(self.predictor_type, {})
            target_range = predictor_config.get('target_range', (0, 100))

            if dataset.y.min() < target_range[0] or dataset.y.max() > target_range[1]:
                logger.warning(f"目标变量超出预期范围: [{dataset.y.min()}, {dataset.y.max()}] vs {target_range}")

            # 检查特征方差
            low_variance_features = []
            for col in dataset.X.columns:
                if dataset.X[col].var() < 1e-6:
                    low_variance_features.append(col)

            if low_variance_features:
                logger.warning(f"发现{len(low_variance_features)}个低方差特征")

            # 检查数据量
            if len(dataset.X) < 50:
                logger.warning(f"数据量较少: {len(dataset.X)}样本")

            logger.info("数据质量验证通过")
            return True

        except Exception as e:
            logger.error(f"数据验证失败: {e}")
            return False

    def _create_lag_features(self, current_issue: str, lag_periods: List[int], feature_types: List[str]) -> Dict[str, Any]:
        """创建滞后特征"""
        lag_features = {}

        try:
            # 获取当前期号的数值
            current_num = int(current_issue[4:])
            current_year = int(current_issue[:4])

            for lag in lag_periods:
                lag_num = current_num - lag

                # 处理跨年情况
                if lag_num <= 0:
                    lag_year = current_year - 1
                    lag_num = 365 + lag_num  # 假设一年365期
                else:
                    lag_year = current_year

                lag_issue = f"{lag_year}{lag_num:03d}"

                # 获取滞后期的特征
                for feature_type in feature_types:
                    try:
                        lag_data = self.feature_engineer.get_features_with_cache(lag_issue, feature_type)
                        if lag_data:
                            for key, value in lag_data.items():
                                lag_key = f"lag_{lag}_{feature_type}_{key}"
                                lag_features[lag_key] = value
                    except:
                        continue

        except Exception as e:
            logger.warning(f"创建滞后特征失败: {e}")

        return lag_features

    def _create_window_features(self, current_issue: str, window_size: int, feature_types: List[str]) -> Dict[str, Any]:
        """创建时间窗口特征"""
        window_features = {}

        try:
            # 获取窗口期号列表
            window_issues = self._get_window_issues(current_issue, window_size)

            # 收集窗口数据
            window_data = {ft: [] for ft in feature_types}

            for issue in window_issues:
                for feature_type in feature_types:
                    try:
                        features = self.feature_engineer.get_features_with_cache(issue, feature_type)
                        if features:
                            # 只取数值特征
                            numeric_features = {k: v for k, v in features.items() if isinstance(v, (int, float))}
                            window_data[feature_type].append(numeric_features)
                    except:
                        continue

            # 计算窗口统计特征
            for feature_type, data_list in window_data.items():
                if data_list:
                    df = pd.DataFrame(data_list)

                    # 计算统计量
                    for col in df.columns:
                        if df[col].dtype in ['int64', 'float64']:
                            window_features[f"window_{feature_type}_{col}_mean"] = df[col].mean()
                            window_features[f"window_{feature_type}_{col}_std"] = df[col].std()
                            window_features[f"window_{feature_type}_{col}_min"] = df[col].min()
                            window_features[f"window_{feature_type}_{col}_max"] = df[col].max()

        except Exception as e:
            logger.warning(f"创建窗口特征失败: {e}")

        return window_features

    def _get_window_issues(self, current_issue: str, window_size: int) -> List[str]:
        """获取时间窗口期号列表"""
        issues = []

        try:
            current_num = int(current_issue[4:])
            current_year = int(current_issue[:4])

            for i in range(1, window_size + 1):
                issue_num = current_num - i
                issue_year = current_year

                # 处理跨年
                if issue_num <= 0:
                    issue_year -= 1
                    issue_num = 365 + issue_num

                issue = f"{issue_year}{issue_num:03d}"
                issues.append(issue)

        except Exception as e:
            logger.warning(f"获取窗口期号失败: {e}")

        return issues

    def _handle_missing_values(self, X: pd.DataFrame, method: str) -> pd.DataFrame:
        """处理缺失值"""
        if method == "forward_fill":
            return X.fillna(method='ffill').fillna(0)
        elif method == "backward_fill":
            return X.fillna(method='bfill').fillna(0)
        elif method == "mean":
            return X.fillna(X.mean()).fillna(0)
        elif method == "median":
            return X.fillna(X.median()).fillna(0)
        elif method == "zero":
            return X.fillna(0)
        else:
            return X.fillna(0)

    def _select_features(self, X: pd.DataFrame, issues: List[str], config: FeatureConfig) -> pd.DataFrame:
        """特征选择"""
        try:
            # 获取目标变量用于特征选择
            y = self.get_target_variable(issues[:len(X)])

            # 使用特征重要性分析进行选择
            result = self.importance_analyzer.analyze_feature_importance(X, y)

            # 选择Top特征
            top_k = min(50, len(X.columns))  # 最多50个特征
            selected_features = self.importance_analyzer.select_top_features(result, top_k)

            # 确保选中的特征在X中存在
            available_features = [f for f in selected_features if f in X.columns]

            if available_features:
                logger.info(f"特征选择: {len(X.columns)} -> {len(available_features)}")
                return X[available_features]
            else:
                logger.warning("特征选择失败，使用原始特征")
                return X

        except Exception as e:
            logger.warning(f"特征选择失败: {e}")
            return X

    def _normalize_features(self, X: pd.DataFrame, method: str) -> pd.DataFrame:
        """特征标准化"""
        try:
            if method == "standard":
                from sklearn.preprocessing import StandardScaler
                scaler = StandardScaler()
                X_scaled = scaler.fit_transform(X)
                return pd.DataFrame(X_scaled, columns=X.columns, index=X.index)

            elif method == "minmax":
                from sklearn.preprocessing import MinMaxScaler
                scaler = MinMaxScaler()
                X_scaled = scaler.fit_transform(X)
                return pd.DataFrame(X_scaled, columns=X.columns, index=X.index)

            elif method == "robust":
                from sklearn.preprocessing import RobustScaler
                scaler = RobustScaler()
                X_scaled = scaler.fit_transform(X)
                return pd.DataFrame(X_scaled, columns=X.columns, index=X.index)

            else:
                return X

        except Exception as e:
            logger.warning(f"特征标准化失败: {e}")
            return X

    def create_ml_pipeline(self, issues: List[str], config: FeatureConfig = None) -> MLDataset:
        """
        创建完整的ML pipeline

        Args:
            issues: 期号列表
            config: 特征配置

        Returns:
            MLDataset: 处理完成的ML数据集
        """
        if config is None:
            config = FeatureConfig()

        # 准备特征
        dataset = self.prepare_features(issues, config)

        # 验证数据
        if not self.validate_data(dataset):
            raise ValueError("数据验证失败")

        logger.info(f"ML pipeline创建完成: {self.predictor_type}")
        return dataset

    def get_feature_importance(self, dataset: MLDataset) -> Dict[str, Any]:
        """
        获取特征重要性

        Args:
            dataset: ML数据集

        Returns:
            Dict: 特征重要性分析结果
        """
        try:
            result = self.importance_analyzer.analyze_feature_importance(dataset.X, dataset.y)
            report = self.importance_analyzer.generate_report(result)

            return {
                'predictor_type': self.predictor_type,
                'top_features': report['top_features']['top_10'],
                'feature_statistics': report['feature_statistics'],
                'model_performance': report['model_performance'],
                'recommendations': report['recommendations']
            }

        except Exception as e:
            logger.error(f"特征重要性分析失败: {e}")
            return {}

    def export_dataset(self, dataset: MLDataset, format: str = "csv", output_dir: str = "output") -> str:
        """
        导出数据集

        Args:
            dataset: ML数据集
            format: 导出格式 (csv, json, pickle)
            output_dir: 输出目录

        Returns:
            str: 导出文件路径
        """
        import os
        os.makedirs(output_dir, exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_filename = f"{self.predictor_type}_dataset_{timestamp}"

        if format == "csv":
            # 导出特征
            features_path = os.path.join(output_dir, f"{base_filename}_features.csv")
            dataset.X.to_csv(features_path, index=False)

            # 导出目标变量
            target_path = os.path.join(output_dir, f"{base_filename}_target.csv")
            dataset.y.to_csv(target_path, index=False)

            # 导出元数据
            metadata_path = os.path.join(output_dir, f"{base_filename}_metadata.json")
            import json
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(dataset.metadata, f, indent=2, ensure_ascii=False)

            logger.info(f"数据集已导出到: {output_dir}")
            return features_path

        elif format == "pickle":
            import pickle
            dataset_path = os.path.join(output_dir, f"{base_filename}.pkl")
            with open(dataset_path, 'wb') as f:
                pickle.dump(dataset, f)
            return dataset_path

        else:
            raise ValueError(f"不支持的导出格式: {format}")

    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return {
            'predictor_type': self.predictor_type,
            'feature_engineer_cache': self.feature_engineer.get_cache_stats(),
            'cache_optimizer_stats': self.cache_optimizer.get_cache_stats()
        }

    def clear_cache(self):
        """清理缓存"""
        self.feature_engineer.clear_cache()
        self.cache_optimizer.clear_cache()
        logger.info(f"{self.predictor_type}预测器缓存已清理")


# 工厂函数
def create_predictor_interface(predictor_type: str, db_path: str = "data/lottery.db") -> PredictorFeatureInterface:
    """
    创建预测器特征接口

    Args:
        predictor_type: 预测器类型
        db_path: 数据库路径

    Returns:
        PredictorFeatureInterface: 预测器接口实例
    """
    valid_types = ['hundreds', 'tens', 'units', 'sum', 'span']
    if predictor_type not in valid_types:
        raise ValueError(f"无效的预测器类型: {predictor_type}，支持: {valid_types}")

    return PredictorFeatureInterface(db_path, predictor_type)


def create_all_predictor_interfaces(db_path: str = "data/lottery.db") -> Dict[str, PredictorFeatureInterface]:
    """
    创建所有预测器接口

    Args:
        db_path: 数据库路径

    Returns:
        Dict: 预测器接口字典
    """
    predictor_types = ['hundreds', 'tens', 'units', 'sum', 'span']
    interfaces = {}

    for predictor_type in predictor_types:
        try:
            interfaces[predictor_type] = create_predictor_interface(predictor_type, db_path)
            logger.info(f"{predictor_type}预测器接口创建成功")
        except Exception as e:
            logger.error(f"{predictor_type}预测器接口创建失败: {e}")

    return interfaces


# 使用示例
def example_usage():
    """使用示例"""
    print("🚀 PredictorFeatureInterface使用示例")
    print("=" * 50)

    # 创建百位预测器接口
    print("1. 创建百位预测器接口")
    interface = create_predictor_interface("hundreds", "data/lottery.db")
    print(f"✅ 接口创建成功: {interface.predictor_type}")

    # 准备特征配置
    print("\n2. 配置特征参数")
    config = FeatureConfig(
        feature_types=['hundreds', 'common'],
        window_size=10,
        lag_features=[1, 2, 3],
        feature_selection=True,
        normalization="standard"
    )
    print(f"✅ 配置完成: {len(config.feature_types)}种特征类型")

    # 获取真实的历史期号数据
    print("\n3. 准备真实历史数据")
    try:
        from data.feature_service import FeatureService
        feature_service = FeatureService("data/lottery.db")

        # 获取真实的历史期号
        test_issues = feature_service.get_latest_issues(50)  # 获取最近50期真实数据
        if len(test_issues) < 20:
            print("❌ 历史数据不足，需要至少20期数据")
            return
        print(f"✅ 真实历史期号: {len(test_issues)}个 (从{test_issues[-1]}到{test_issues[0]})")
    except Exception as e:
        print(f"❌ 无法获取真实历史数据: {e}")
        return

    try:
        # 创建ML数据集
        print("\n4. 创建ML数据集")
        dataset = interface.create_ml_pipeline(test_issues, config)
        print(f"✅ 数据集创建成功: {len(dataset.X)}样本, {len(dataset.X.columns)}特征")

        # 分割训练测试集
        print("\n5. 分割训练测试集")
        train_dataset, test_dataset = dataset.train_test_split(test_size=0.2)
        print(f"✅ 训练集: {len(train_dataset.X)}样本")
        print(f"✅ 测试集: {len(test_dataset.X)}样本")

        # 特征重要性分析
        print("\n6. 特征重要性分析")
        importance_result = interface.get_feature_importance(train_dataset)
        if importance_result:
            top_features = importance_result.get('top_features', [])
            print(f"✅ Top 5特征: {[f[0] for f in top_features[:5]]}")

        # 导出数据集
        print("\n7. 导出数据集")
        export_path = interface.export_dataset(dataset, format="csv", output_dir="test_output")
        print(f"✅ 数据集已导出: {export_path}")

        print("\n🎉 示例运行完成！")

    except Exception as e:
        print(f"\n❌ 示例运行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    example_usage()
