#!/usr/bin/env python3
"""
测试P9系统缓存策略优化效果
验证阶段3的缓存实现
"""

import sys
import os
import asyncio
import time
from datetime import datetime

# 添加项目路径
sys.path.append('.')

def test_ttl_cache_module():
    """测试TTL缓存模块"""
    print("=" * 60)
    print("🔍 测试阶段3：TTL缓存模块")
    print("=" * 60)
    
    try:
        from src.utils.ttl_cache import TTLCache, ttl_cache, get_api_cache, get_performance_cache
        print("✅ TTL缓存模块导入成功")
        
        # 测试基本缓存功能
        cache = TTLCache(default_ttl=5)  # 5秒TTL
        
        # 设置缓存
        cache.set("test_key", "test_value", ttl=3)
        print("✅ 缓存设置成功")
        
        # 获取缓存
        value = cache.get("test_key")
        if value == "test_value":
            print("✅ 缓存获取成功")
        else:
            print("❌ 缓存获取失败")
            return False
        
        # 测试缓存统计
        stats = cache.get_stats()
        print(f"✅ 缓存统计: {stats['total_items']} 项")
        
        # 测试TTL过期
        print("⏳ 等待缓存过期...")
        time.sleep(4)
        expired_value = cache.get("test_key")
        if expired_value is None:
            print("✅ TTL过期机制正常")
        else:
            print("❌ TTL过期机制异常")
            return False
        
        return True
        
    except ImportError as e:
        print(f"❌ TTL缓存模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ TTL缓存测试失败: {e}")
        return False

def test_api_adapter_cache():
    """测试API适配器缓存功能"""
    print("\n" + "=" * 60)
    print("🔍 测试API适配器缓存集成")
    print("=" * 60)
    
    try:
        from src.web.api_adapter import P9SystemAdapter
        
        # 创建适配器实例
        adapter = P9SystemAdapter('data/fucai3d.db')
        print("✅ API适配器实例创建成功")
        
        # 测试缓存统计方法
        cache_stats = adapter.get_cache_stats()
        print("✅ 缓存统计方法调用成功")
        print(f"   缓存可用: {cache_stats.get('cache_available', False)}")
        
        if cache_stats.get('cache_available'):
            print(f"   API缓存: {cache_stats['cache_stats']['api_cache']['total_items']} 项")
            print(f"   性能缓存: {cache_stats['cache_stats']['performance_cache']['total_items']} 项")
            print(f"   状态缓存: {cache_stats['cache_stats']['system_status_cache']['total_items']} 项")
        
        return True
        
    except Exception as e:
        print(f"❌ API适配器缓存测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_cached_api_methods():
    """测试带缓存的API方法"""
    print("\n" + "=" * 60)
    print("🔍 测试缓存装饰器效果")
    print("=" * 60)
    
    try:
        from src.web.api_adapter import P9SystemAdapter
        
        adapter = P9SystemAdapter('data/fucai3d.db')
        
        # 测试性能指标缓存
        print("📊 测试性能指标缓存...")
        start_time = time.time()
        metrics1 = await adapter._get_performance_metrics()
        first_call_time = time.time() - start_time
        
        start_time = time.time()
        metrics2 = await adapter._get_performance_metrics()
        second_call_time = time.time() - start_time
        
        print(f"   第一次调用耗时: {first_call_time:.3f}秒")
        print(f"   第二次调用耗时: {second_call_time:.3f}秒")
        
        if second_call_time < first_call_time * 0.5:  # 缓存应该显著提升速度
            print("✅ 性能指标缓存生效")
        else:
            print("⚠️ 性能指标缓存效果不明显")
        
        # 测试系统状态缓存
        print("🔧 测试系统状态缓存...")
        start_time = time.time()
        status1 = await adapter._get_system_status()
        first_call_time = time.time() - start_time
        
        start_time = time.time()
        status2 = await adapter._get_system_status()
        second_call_time = time.time() - start_time
        
        print(f"   第一次调用耗时: {first_call_time:.3f}秒")
        print(f"   第二次调用耗时: {second_call_time:.3f}秒")
        
        if second_call_time < first_call_time * 0.5:
            print("✅ 系统状态缓存生效")
        else:
            print("⚠️ 系统状态缓存效果不明显")
        
        # 测试仪表板数据缓存
        print("📈 测试仪表板数据缓存...")
        start_time = time.time()
        dashboard1 = await adapter.get_dashboard_data()
        first_call_time = time.time() - start_time
        
        start_time = time.time()
        dashboard2 = await adapter.get_dashboard_data()
        second_call_time = time.time() - start_time
        
        print(f"   第一次调用耗时: {first_call_time:.3f}秒")
        print(f"   第二次调用耗时: {second_call_time:.3f}秒")
        
        if second_call_time < first_call_time * 0.5:
            print("✅ 仪表板数据缓存生效")
        else:
            print("⚠️ 仪表板数据缓存效果不明显")
        
        return True
        
    except Exception as e:
        print(f"❌ 缓存API方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cache_management():
    """测试缓存管理功能"""
    print("\n" + "=" * 60)
    print("🔍 测试缓存管理功能")
    print("=" * 60)
    
    try:
        from src.web.api_adapter import P9SystemAdapter
        
        adapter = P9SystemAdapter('data/fucai3d.db')
        
        # 获取缓存统计
        stats_before = adapter.get_cache_stats()
        print("✅ 获取缓存统计成功")
        
        # 清空缓存
        clear_result = adapter.clear_all_caches()
        print(f"✅ 清空缓存: {clear_result.get('success', False)}")
        
        # 再次获取统计
        stats_after = adapter.get_cache_stats()
        print("✅ 清空后缓存统计获取成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 缓存管理测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 P9系统缓存策略优化测试")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试TTL缓存模块
    ttl_test_passed = test_ttl_cache_module()
    
    # 测试API适配器缓存集成
    adapter_test_passed = test_api_adapter_cache()
    
    # 测试缓存装饰器效果
    cache_effect_test_passed = asyncio.run(test_cached_api_methods())
    
    # 测试缓存管理功能
    management_test_passed = test_cache_management()
    
    # 总结测试结果
    print("\n" + "=" * 60)
    print("📊 阶段3缓存优化测试结果")
    print("=" * 60)
    print(f"TTL缓存模块: {'✅ 通过' if ttl_test_passed else '❌ 失败'}")
    print(f"API适配器集成: {'✅ 通过' if adapter_test_passed else '❌ 失败'}")
    print(f"缓存装饰器效果: {'✅ 通过' if cache_effect_test_passed else '❌ 失败'}")
    print(f"缓存管理功能: {'✅ 通过' if management_test_passed else '❌ 失败'}")
    
    all_passed = all([ttl_test_passed, adapter_test_passed, cache_effect_test_passed, management_test_passed])
    
    if all_passed:
        print("\n🎉 阶段3缓存策略优化验证通过！可以继续执行阶段4")
        return True
    else:
        print("\n⚠️ 部分缓存优化验证失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
