#!/usr/bin/env python3
"""
P9系统API异常处理修复验证脚本
验证阶段1和阶段2的修复效果
"""

import sys
import os
import asyncio
import time
from datetime import datetime

# 添加项目路径
sys.path.append('.')

def test_api_adapter():
    """测试API适配器修复效果"""
    print("=" * 60)
    print("🔍 测试阶段1：API适配器异常处理修复")
    print("=" * 60)
    
    try:
        from src.web.api_adapter import P9SystemAdapter
        
        # 创建适配器实例
        adapter = P9SystemAdapter('data/fucai3d.db')
        print("✅ API适配器实例创建成功")
        
        # 测试异步方法
        async def test_async_methods():
            try:
                # 测试主要的仪表板数据获取方法
                dashboard_data = await adapter.get_dashboard_data()
                print("✅ get_dashboard_data() 调用成功")
                print(f"   - 预测数据: {len(dashboard_data.get('predictions', []))}")
                print(f"   - 系统状态: {dashboard_data.get('system_status', {}).get('optimization_running', 'unknown')}")
                print(f"   - 更新时间: {dashboard_data.get('update_time', 'unknown')}")

                # 测试性能指标获取（这是之前出错的方法）
                if hasattr(adapter, '_generate_mock_performance_metrics'):
                    metrics = adapter._generate_mock_performance_metrics()
                    print("✅ _generate_mock_performance_metrics() 调用成功")
                    print(f"   - 指标组数: {len(metrics)}")
                    print(f"   - 组件: {list(metrics.keys())}")
                else:
                    print("⚠️ 未找到 _generate_mock_performance_metrics 方法")

                # 测试性能指标异步方法（之前出错的核心方法）
                perf_metrics = await adapter._get_performance_metrics()
                print("✅ _get_performance_metrics() 调用成功（关键修复点）")
                print(f"   - 返回数据类型: {type(perf_metrics)}")
                print(f"   - 数据键数量: {len(perf_metrics) if isinstance(perf_metrics, dict) else 0}")

                return True

            except Exception as e:
                print(f"❌ 异步方法测试失败: {e}")
                import traceback
                traceback.print_exc()
                return False
        
        # 运行异步测试
        result = asyncio.run(test_async_methods())
        
        if result:
            print("✅ 阶段1修复验证：通过")
        else:
            print("❌ 阶段1修复验证：失败")
            
        return result
        
    except ImportError as e:
        print(f"❌ 导入API适配器失败: {e}")
        return False
    except Exception as e:
        print(f"❌ API适配器测试失败: {e}")
        return False

def test_frontend_polling_config():
    """测试前端轮询配置修复效果"""
    print("\n" + "=" * 60)
    print("🔍 测试阶段2：前端轮询间隔优化")
    print("=" * 60)
    
    files_to_check = [
        {
            'file': 'web-frontend/src/components/Dashboard.tsx',
            'pattern': '60000',
            'description': 'Dashboard组件轮询间隔'
        },
        {
            'file': 'web-frontend/src/hooks/usePredictionData.ts', 
            'pattern': '60000',
            'description': 'usePredictionData Hook轮询间隔'
        },
        {
            'file': 'web-frontend/src/hooks/useRealTimeData.ts',
            'pattern': 'fallbackInterval = 60000',
            'description': 'useRealTimeData Hook fallback间隔'
        }
    ]
    
    all_passed = True
    
    for check in files_to_check:
        try:
            file_path = check['file']
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                if check['pattern'] in content:
                    print(f"✅ {check['description']}: 已优化为60秒")
                else:
                    print(f"❌ {check['description']}: 未找到60秒配置")
                    all_passed = False
            else:
                print(f"⚠️ 文件不存在: {file_path}")
                all_passed = False
                
        except Exception as e:
            print(f"❌ 检查文件失败 {check['file']}: {e}")
            all_passed = False
    
    if all_passed:
        print("✅ 阶段2修复验证：通过")
    else:
        print("❌ 阶段2修复验证：失败")
        
    return all_passed

def main():
    """主测试函数"""
    print("🚀 P9系统API异常处理修复验证")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试API适配器修复
    api_test_passed = test_api_adapter()
    
    # 测试前端轮询配置
    polling_test_passed = test_frontend_polling_config()
    
    # 总结测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    print(f"阶段1 - API适配器修复: {'✅ 通过' if api_test_passed else '❌ 失败'}")
    print(f"阶段2 - 前端轮询优化: {'✅ 通过' if polling_test_passed else '❌ 失败'}")
    
    if api_test_passed and polling_test_passed:
        print("\n🎉 所有修复验证通过！可以继续执行阶段3和4")
        return True
    else:
        print("\n⚠️ 部分修复验证失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
