#!/usr/bin/env python3
"""检查数据库中的真实历史数据"""

import sqlite3
import os
from pathlib import Path

def check_database():
    """检查数据库文件和数据"""
    print("🔍 检查福彩3D数据库")
    print("=" * 50)
    
    # 检查数据库文件
    db_files = ['data/lottery.db', 'data/fucai3d.db', 'data/alerts.db']
    
    for db_file in db_files:
        if os.path.exists(db_file):
            print(f"✅ 发现数据库文件: {db_file}")
            
            try:
                conn = sqlite3.connect(db_file)
                cursor = conn.cursor()
                
                # 获取所有表
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = cursor.fetchall()
                print(f"   包含 {len(tables)} 个表:")
                
                for table in tables:
                    table_name = table[0]
                    try:
                        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                        count = cursor.fetchone()[0]
                        print(f"     - {table_name}: {count} 条记录")
                        
                        # 如果是历史数据表，显示样本
                        if any(keyword in table_name.lower() for keyword in ['lottery', 'result', 'historical', 'fucai']):
                            if count > 0:
                                cursor.execute(f"SELECT * FROM {table_name} LIMIT 3")
                                samples = cursor.fetchall()
                                print(f"       样本数据: {samples}")
                    except Exception as e:
                        print(f"     - {table_name}: 查询失败 ({e})")
                
                conn.close()
                
            except Exception as e:
                print(f"   ❌ 数据库连接失败: {e}")
        else:
            print(f"❌ 数据库文件不存在: {db_file}")
    
    print("\n" + "=" * 50)

if __name__ == "__main__":
    check_database()
