"""
和值专用特征生成器
为和值预测器提供30+维专用特征，包括：
- 分布特征：和值分布区间、密度分析
- 范围特征：最大最小值、变化范围
- 尾数特征：和值尾数分析、奇偶性
- 变化特征：和值变化趋势、波动性
- 统计特征：均值、方差、偏度、峰度
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional
from collections import Counter


def generate_sum_features(df: pd.DataFrame, window_sizes: List[int] = [5, 10, 20, 50]) -> pd.DataFrame:
    """
    生成和值专用特征
    
    Args:
        df: 包含历史数据的DataFrame，必须包含'sum_value'列或能计算和值的列
        window_sizes: 滑动窗口大小列表
        
    Returns:
        pd.DataFrame: 包含和值专用特征的DataFrame
    """
    result_df = df.copy()
    
    # 确保有和值列
    if 'sum_value' not in df.columns:
        if all(col in df.columns for col in ['hundreds', 'tens', 'units']):
            result_df['sum_value'] = df['hundreds'] + df['tens'] + df['units']
        else:
            raise ValueError("DataFrame必须包含'sum_value'列或'hundreds', 'tens', 'units'列")
    
    sum_series = result_df['sum_value']
    
    # 1. 分布区间特征
    result_df.update(_generate_sum_distribution_features(sum_series, window_sizes))
    
    # 2. 范围分析特征
    result_df.update(_generate_sum_range_features(sum_series, window_sizes))
    
    # 3. 尾数分析特征
    result_df.update(_generate_sum_tail_features(sum_series, window_sizes))
    
    # 4. 变化趋势特征
    result_df.update(_generate_sum_change_features(sum_series, window_sizes))
    
    # 5. 统计分布特征
    result_df.update(_generate_sum_statistical_features(sum_series, window_sizes))
    
    # 6. 周期性特征
    result_df.update(_generate_sum_periodic_features(sum_series, window_sizes))
    
    return result_df


def _generate_sum_distribution_features(series: pd.Series, window_sizes: List[int]) -> pd.DataFrame:
    """生成和值分布特征"""
    features = pd.DataFrame(index=series.index)
    
    # 和值分布区间定义
    # 福彩3D和值范围：0-27
    def get_sum_zone(value):
        if value <= 9:
            return 'low'
        elif value <= 18:
            return 'middle'
        else:
            return 'high'
    
    def get_detailed_zone(value):
        if value <= 6:
            return 'very_low'
        elif value <= 12:
            return 'low'
        elif value <= 15:
            return 'middle'
        elif value <= 21:
            return 'high'
        else:
            return 'very_high'
    
    # 基础分布特征
    features['sum_zone'] = series.apply(get_sum_zone)
    features['sum_detailed_zone'] = series.apply(get_detailed_zone)
    features['sum_is_low'] = (series <= 9).astype(int)
    features['sum_is_middle'] = ((series > 9) & (series <= 18)).astype(int)
    features['sum_is_high'] = (series > 18).astype(int)
    
    for window in window_sizes:
        # 分布区间统计
        features[f'sum_low_ratio_{window}'] = features['sum_is_low'].rolling(window=window).mean()
        features[f'sum_middle_ratio_{window}'] = features['sum_is_middle'].rolling(window=window).mean()
        features[f'sum_high_ratio_{window}'] = features['sum_is_high'].rolling(window=window).mean()
        
        # 分布密度
        features[f'sum_distribution_density_{window}'] = _calculate_distribution_density(series, window)
        
        # 分布均匀性
        features[f'sum_distribution_uniformity_{window}'] = _calculate_distribution_uniformity(series, window)
        
        # 分布偏移
        features[f'sum_distribution_shift_{window}'] = _calculate_distribution_shift(series, window)
    
    return features


def _generate_sum_range_features(series: pd.Series, window_sizes: List[int]) -> pd.DataFrame:
    """生成和值范围特征"""
    features = pd.DataFrame(index=series.index)
    
    for window in window_sizes:
        # 基础范围特征
        features[f'sum_range_{window}'] = series.rolling(window=window).max() - series.rolling(window=window).min()
        features[f'sum_max_{window}'] = series.rolling(window=window).max()
        features[f'sum_min_{window}'] = series.rolling(window=window).min()
        
        # 范围变化
        features[f'sum_range_change_{window}'] = features[f'sum_range_{window}'].pct_change()
        
        # 范围稳定性
        features[f'sum_range_stability_{window}'] = 1 / (1 + features[f'sum_range_{window}'].rolling(window=window//2).std())
        
        # 极值频率
        features[f'sum_max_freq_{window}'] = (series == features[f'sum_max_{window}']).rolling(window=window).sum()
        features[f'sum_min_freq_{window}'] = (series == features[f'sum_min_{window}']).rolling(window=window).sum()
        
        # 范围位置
        range_val = features[f'sum_range_{window}']
        min_val = features[f'sum_min_{window}']
        features[f'sum_range_position_{window}'] = (series - min_val) / (range_val + 1e-8)
    
    return features


def _generate_sum_tail_features(series: pd.Series, window_sizes: List[int]) -> pd.DataFrame:
    """生成和值尾数特征"""
    features = pd.DataFrame(index=series.index)
    
    # 尾数基础特征
    features['sum_tail'] = series % 10
    features['sum_tail_is_odd'] = (features['sum_tail'] % 2).astype(int)
    features['sum_tail_is_even'] = ((features['sum_tail'] + 1) % 2).astype(int)
    
    # 尾数分类
    features['sum_tail_is_small'] = (features['sum_tail'] < 5).astype(int)
    features['sum_tail_is_big'] = (features['sum_tail'] >= 5).astype(int)
    
    for window in window_sizes:
        # 尾数统计
        features[f'sum_tail_odd_ratio_{window}'] = features['sum_tail_is_odd'].rolling(window=window).mean()
        features[f'sum_tail_even_ratio_{window}'] = features['sum_tail_is_even'].rolling(window=window).mean()
        features[f'sum_tail_small_ratio_{window}'] = features['sum_tail_is_small'].rolling(window=window).mean()
        features[f'sum_tail_big_ratio_{window}'] = features['sum_tail_is_big'].rolling(window=window).mean()
        
        # 尾数频次
        features[f'sum_tail_freq_{window}'] = features['sum_tail'].rolling(window=window).apply(
            lambda x: Counter(x)[x.iloc[-1]] if len(x) > 0 else 0
        )
        
        # 尾数变化
        features[f'sum_tail_change_{window}'] = (features['sum_tail'] != features['sum_tail'].shift(1)).rolling(window=window).sum()
        
        # 尾数模式
        features[f'sum_tail_pattern_{window}'] = _calculate_tail_pattern(features['sum_tail'], window)
    
    return features


def _generate_sum_change_features(series: pd.Series, window_sizes: List[int]) -> pd.DataFrame:
    """生成和值变化特征"""
    features = pd.DataFrame(index=series.index)
    
    # 基础变化特征
    features['sum_change'] = series.diff()
    features['sum_change_abs'] = np.abs(features['sum_change'])
    features['sum_change_direction'] = np.sign(features['sum_change'])
    
    # 变化幅度分类
    features['sum_change_small'] = (features['sum_change_abs'] <= 2).astype(int)
    features['sum_change_medium'] = ((features['sum_change_abs'] > 2) & (features['sum_change_abs'] <= 5)).astype(int)
    features['sum_change_large'] = (features['sum_change_abs'] > 5).astype(int)
    
    for window in window_sizes:
        # 变化统计
        features[f'sum_change_mean_{window}'] = features['sum_change'].rolling(window=window).mean()
        features[f'sum_change_std_{window}'] = features['sum_change'].rolling(window=window).std()
        features[f'sum_change_abs_mean_{window}'] = features['sum_change_abs'].rolling(window=window).mean()
        
        # 变化方向统计
        features[f'sum_increasing_ratio_{window}'] = (features['sum_change'] > 0).rolling(window=window).mean()
        features[f'sum_decreasing_ratio_{window}'] = (features['sum_change'] < 0).rolling(window=window).mean()
        features[f'sum_stable_ratio_{window}'] = (features['sum_change'] == 0).rolling(window=window).mean()
        
        # 变化幅度统计
        features[f'sum_small_change_ratio_{window}'] = features['sum_change_small'].rolling(window=window).mean()
        features[f'sum_medium_change_ratio_{window}'] = features['sum_change_medium'].rolling(window=window).mean()
        features[f'sum_large_change_ratio_{window}'] = features['sum_change_large'].rolling(window=window).mean()
        
        # 变化趋势
        features[f'sum_change_trend_{window}'] = features['sum_change'].rolling(window=window).apply(
            lambda x: np.polyfit(range(len(x)), x, 1)[0] if len(x) > 1 else 0
        )
        
        # 变化周期性
        features[f'sum_change_periodicity_{window}'] = _calculate_change_periodicity(features['sum_change'], window)
    
    return features


def _generate_sum_statistical_features(series: pd.Series, window_sizes: List[int]) -> pd.DataFrame:
    """生成和值统计特征"""
    features = pd.DataFrame(index=series.index)
    
    for window in window_sizes:
        # 基础统计量
        features[f'sum_mean_{window}'] = series.rolling(window=window).mean()
        features[f'sum_std_{window}'] = series.rolling(window=window).std()
        features[f'sum_var_{window}'] = series.rolling(window=window).var()
        
        # 分位数
        features[f'sum_q25_{window}'] = series.rolling(window=window).quantile(0.25)
        features[f'sum_median_{window}'] = series.rolling(window=window).median()
        features[f'sum_q75_{window}'] = series.rolling(window=window).quantile(0.75)
        features[f'sum_iqr_{window}'] = features[f'sum_q75_{window}'] - features[f'sum_q25_{window}']
        
        # 分布形状
        features[f'sum_skew_{window}'] = series.rolling(window=window).skew()
        features[f'sum_kurt_{window}'] = series.rolling(window=window).kurt()
        
        # 与统计量的关系
        features[f'sum_above_mean_{window}'] = (series > features[f'sum_mean_{window}']).astype(int)
        features[f'sum_below_mean_{window}'] = (series < features[f'sum_mean_{window}']).astype(int)
        features[f'sum_distance_from_mean_{window}'] = np.abs(series - features[f'sum_mean_{window}'])
        
        # 变异系数
        features[f'sum_cv_{window}'] = features[f'sum_std_{window}'] / (features[f'sum_mean_{window}'] + 1e-8)
    
    return features


def _generate_sum_periodic_features(series: pd.Series, window_sizes: List[int]) -> pd.DataFrame:
    """生成和值周期性特征"""
    features = pd.DataFrame(index=series.index)
    
    # 周期性分析
    for period in [7, 14, 30]:
        if len(series) > period:
            # 周期内位置
            features[f'sum_period_pos_{period}'] = series.index % period
            
            # 周期性相关性
            shifted_series = series.shift(period)
            features[f'sum_period_corr_{period}'] = series.rolling(window=period*2).corr(shifted_series)
            
            # 周期性差异
            features[f'sum_period_diff_{period}'] = series - shifted_series
            
            # 周期性模式
            features[f'sum_period_pattern_{period}'] = _calculate_periodic_pattern(series, period)
    
    return features


# 辅助函数
def _calculate_distribution_density(series: pd.Series, window: int) -> pd.Series:
    """计算分布密度"""
    densities = []
    for i in range(len(series)):
        if i < window:
            densities.append(0)
        else:
            window_data = series.iloc[i-window+1:i+1]
            unique_count = len(window_data.unique())
            density = unique_count / window
            densities.append(density)
    return pd.Series(densities, index=series.index)


def _calculate_distribution_uniformity(series: pd.Series, window: int) -> pd.Series:
    """计算分布均匀性"""
    uniformities = []
    for i in range(len(series)):
        if i < window:
            uniformities.append(0)
        else:
            window_data = series.iloc[i-window+1:i+1]
            value_counts = window_data.value_counts()
            expected_freq = window / len(value_counts)
            chi_square = np.sum((value_counts - expected_freq) ** 2 / expected_freq)
            uniformity = 1 / (1 + chi_square)
            uniformities.append(uniformity)
    return pd.Series(uniformities, index=series.index)


def _calculate_distribution_shift(series: pd.Series, window: int) -> pd.Series:
    """计算分布偏移"""
    shifts = []
    for i in range(len(series)):
        if i < window * 2:
            shifts.append(0)
        else:
            current_window = series.iloc[i-window+1:i+1]
            previous_window = series.iloc[i-window*2+1:i-window+1]
            
            current_mean = current_window.mean()
            previous_mean = previous_window.mean()
            shift = current_mean - previous_mean
            shifts.append(shift)
    return pd.Series(shifts, index=series.index)


def _calculate_tail_pattern(tail_series: pd.Series, window: int) -> pd.Series:
    """计算尾数模式"""
    patterns = []
    for i in range(len(tail_series)):
        if i < window:
            patterns.append(0)
        else:
            window_data = tail_series.iloc[i-window+1:i+1]
            # 计算尾数的重复模式
            value_counts = window_data.value_counts()
            max_freq = value_counts.max()
            pattern_score = max_freq / window
            patterns.append(pattern_score)
    return pd.Series(patterns, index=tail_series.index)


def _calculate_change_periodicity(change_series: pd.Series, window: int) -> pd.Series:
    """计算变化周期性"""
    periodicities = []
    for i in range(len(change_series)):
        if i < window:
            periodicities.append(0)
        else:
            window_data = change_series.iloc[i-window+1:i+1]
            # 简化的周期性计算：变化方向的规律性
            directions = np.sign(window_data)
            direction_changes = (directions != directions.shift(1)).sum()
            periodicity = 1 - (direction_changes / window)
            periodicities.append(periodicity)
    return pd.Series(periodicities, index=change_series.index)


def _calculate_periodic_pattern(series: pd.Series, period: int) -> pd.Series:
    """计算周期性模式"""
    patterns = []
    for i in range(len(series)):
        if i < period * 2:
            patterns.append(0)
        else:
            # 计算与前一个周期的相似性
            current_cycle = series.iloc[i-period+1:i+1]
            previous_cycle = series.iloc[i-period*2+1:i-period+1]
            
            if len(current_cycle) == len(previous_cycle):
                correlation = current_cycle.corr(previous_cycle)
                pattern_score = correlation if not pd.isna(correlation) else 0
            else:
                pattern_score = 0
            
            patterns.append(pattern_score)
    return pd.Series(patterns, index=series.index)
