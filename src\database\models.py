"""
数据库模型定义
基于3dyuce项目成功经验，扩展为15字段完整数据模型
支持动态特征计算，无需修改数据库结构
"""

import sqlite3
from datetime import datetime
from typing import Optional, List, Dict, Any
from dataclasses import dataclass, asdict
import json


@dataclass
class LotteryData:
    """福彩3D开奖数据模型 - 15字段完整版本"""
    
    # 基础字段
    id: Optional[int] = None
    issue: str = ""                    # 期号
    draw_date: str = ""               # 开奖日期
    
    # 开奖号码
    hundreds: int = 0                 # 百位
    tens: int = 0                     # 十位
    units: int = 0                    # 个位
    
    # 试机号码（新增字段）
    trial_hundreds: Optional[int] = None    # 试机百位
    trial_tens: Optional[int] = None        # 试机十位
    trial_units: Optional[int] = None       # 试机个位
    
    # 设备和销售信息（新增字段）
    machine_number: Optional[str] = None    # 机器号
    sales_amount: Optional[float] = None    # 销售额
    prize_info: Optional[str] = None        # 奖金信息
    
    # 计算字段
    sum_value: Optional[int] = None         # 和值
    span: Optional[int] = None              # 跨度
    number_type: Optional[str] = None       # 号码类型
    
    def __post_init__(self):
        """自动计算衍生字段"""
        if self.hundreds is not None and self.tens is not None and self.units is not None:
            # 计算和值
            self.sum_value = self.hundreds + self.tens + self.units
            
            # 计算跨度
            numbers = [self.hundreds, self.tens, self.units]
            self.span = max(numbers) - min(numbers)
            
            # 判断号码类型
            self.number_type = self._determine_number_type()
    
    def _determine_number_type(self) -> str:
        """判断号码类型"""
        numbers = [self.hundreds, self.tens, self.units]
        unique_count = len(set(numbers))
        
        if unique_count == 1:
            return "豹子"
        elif unique_count == 2:
            return "对子"
        else:
            return "组六"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'LotteryData':
        """从字典创建实例"""
        return cls(**data)

    def get_features(self, previous_data: Optional['LotteryData'] = None) -> Dict[str, Any]:
        """
        获取所有特征

        Args:
            previous_data: 上期数据，用于计算走势特征

        Returns:
            Dict[str, Any]: 包含所有特征的字典
        """
        # 延迟导入避免循环依赖
        from ..data.feature_calculator import calculate_features

        # 准备上期数据
        previous_hundreds = previous_data.hundreds if previous_data else None
        previous_tens = previous_data.tens if previous_data else None
        previous_units = previous_data.units if previous_data else None

        # 计算所有特征
        features = calculate_features(
            self.hundreds, self.tens, self.units,
            previous_hundreds, previous_tens, previous_units
        )

        # 添加基础字段
        features.update({
            'issue': self.issue,
            'draw_date': self.draw_date,
            'hundreds': self.hundreds,
            'tens': self.tens,
            'units': self.units
        })

        return features

    def get_feature_by_name(self, feature_name: str, previous_data: Optional['LotteryData'] = None) -> Any:
        """
        获取指定名称的特征

        Args:
            feature_name: 特征名称
            previous_data: 上期数据，用于计算走势特征

        Returns:
            Any: 特征值
        """
        features = self.get_features(previous_data)
        return features.get(feature_name)

    def get_basic_info(self) -> Dict[str, Any]:
        """获取基础信息（不包含复杂特征计算）"""
        return {
            'issue': self.issue,
            'draw_date': self.draw_date,
            'hundreds': self.hundreds,
            'tens': self.tens,
            'units': self.units,
            'sum_value': self.sum_value,
            'span': self.span,
            'number_type': self.number_type
        }


@dataclass
class CollectionLog:
    """数据采集日志模型"""
    
    id: Optional[int] = None
    collection_time: str = ""         # 采集时间
    source_url: str = ""              # 数据源URL
    records_collected: int = 0        # 采集记录数
    success: bool = True              # 是否成功
    error_message: Optional[str] = None    # 错误信息
    response_time: float = 0.0        # 响应时间
    
    def __post_init__(self):
        if not self.collection_time:
            self.collection_time = datetime.now().isoformat()


@dataclass
class DataValidation:
    """数据验证记录模型"""
    
    id: Optional[int] = None
    validation_time: str = ""         # 验证时间
    issue: str = ""                   # 期号
    validation_type: str = ""         # 验证类型（format/logic）
    is_valid: bool = True             # 是否有效
    error_details: Optional[str] = None    # 错误详情
    
    def __post_init__(self):
        if not self.validation_time:
            self.validation_time = datetime.now().isoformat()


class DatabaseSchema:
    """数据库表结构定义"""
    
    # 主数据表 - 15字段完整模型
    LOTTERY_DATA_TABLE = """
    CREATE TABLE IF NOT EXISTS lottery_data (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        issue TEXT UNIQUE NOT NULL,
        draw_date TEXT NOT NULL,
        hundreds INTEGER NOT NULL CHECK (hundreds >= 0 AND hundreds <= 9),
        tens INTEGER NOT NULL CHECK (tens >= 0 AND tens <= 9),
        units INTEGER NOT NULL CHECK (units >= 0 AND units <= 9),
        trial_hundreds INTEGER CHECK (trial_hundreds IS NULL OR (trial_hundreds >= 0 AND trial_hundreds <= 9)),
        trial_tens INTEGER CHECK (trial_tens IS NULL OR (trial_tens >= 0 AND trial_tens <= 9)),
        trial_units INTEGER CHECK (trial_units IS NULL OR (trial_units >= 0 AND trial_units <= 9)),
        machine_number TEXT,
        sales_amount REAL CHECK (sales_amount IS NULL OR sales_amount >= 0),
        prize_info TEXT,
        sum_value INTEGER CHECK (sum_value >= 0 AND sum_value <= 27),
        span INTEGER CHECK (span >= 0 AND span <= 9),
        number_type TEXT CHECK (number_type IN ('豹子', '对子', '组六')),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    """
    
    # 采集日志表
    COLLECTION_LOGS_TABLE = """
    CREATE TABLE IF NOT EXISTS collection_logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        collection_time TEXT NOT NULL,
        source_url TEXT NOT NULL,
        records_collected INTEGER DEFAULT 0,
        success BOOLEAN DEFAULT 1,
        error_message TEXT,
        response_time REAL DEFAULT 0.0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    """
    
    # 数据验证表
    DATA_VALIDATION_TABLE = """
    CREATE TABLE IF NOT EXISTS data_validation (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        validation_time TEXT NOT NULL,
        issue TEXT NOT NULL,
        validation_type TEXT NOT NULL,
        is_valid BOOLEAN DEFAULT 1,
        error_details TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (issue) REFERENCES lottery_data (issue)
    )
    """
    
    # 索引定义
    INDEXES = [
        "CREATE INDEX IF NOT EXISTS idx_lottery_data_issue ON lottery_data (issue)",
        "CREATE INDEX IF NOT EXISTS idx_lottery_data_date ON lottery_data (draw_date)",
        "CREATE INDEX IF NOT EXISTS idx_lottery_data_numbers ON lottery_data (hundreds, tens, units)",
        "CREATE INDEX IF NOT EXISTS idx_collection_logs_time ON collection_logs (collection_time)",
        "CREATE INDEX IF NOT EXISTS idx_validation_issue ON data_validation (issue)",
        "CREATE INDEX IF NOT EXISTS idx_validation_type ON data_validation (validation_type)"
    ]
    
    # 触发器 - 自动更新时间戳
    TRIGGERS = [
        """
        CREATE TRIGGER IF NOT EXISTS update_lottery_data_timestamp 
        AFTER UPDATE ON lottery_data
        BEGIN
            UPDATE lottery_data SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
        END
        """
    ]


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, db_path: str = "data/lottery.db"):
        self.db_path = db_path
        self.schema = DatabaseSchema()
    
    def get_connection(self) -> sqlite3.Connection:
        """获取数据库连接"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # 启用字典式访问
        return conn
    
    def initialize_database(self) -> bool:
        """初始化数据库"""
        try:
            with self.get_connection() as conn:
                # 创建表
                conn.execute(self.schema.LOTTERY_DATA_TABLE)
                conn.execute(self.schema.COLLECTION_LOGS_TABLE)
                conn.execute(self.schema.DATA_VALIDATION_TABLE)
                
                # 创建索引
                for index_sql in self.schema.INDEXES:
                    conn.execute(index_sql)
                
                # 创建触发器
                for trigger_sql in self.schema.TRIGGERS:
                    conn.execute(trigger_sql)
                
                conn.commit()
                return True
        except Exception as e:
            print(f"数据库初始化失败: {e}")
            return False
    
    def insert_lottery_data(self, data: LotteryData) -> bool:
        """插入开奖数据"""
        try:
            with self.get_connection() as conn:
                sql = """
                INSERT OR REPLACE INTO lottery_data 
                (issue, draw_date, hundreds, tens, units, trial_hundreds, trial_tens, trial_units,
                 machine_number, sales_amount, prize_info, sum_value, span, number_type)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                conn.execute(sql, (
                    data.issue, data.draw_date, data.hundreds, data.tens, data.units,
                    data.trial_hundreds, data.trial_tens, data.trial_units,
                    data.machine_number, data.sales_amount, data.prize_info,
                    data.sum_value, data.span, data.number_type
                ))
                conn.commit()
                return True
        except Exception as e:
            print(f"插入数据失败: {e}")
            return False
    
    def insert_collection_log(self, log: CollectionLog) -> bool:
        """插入采集日志"""
        try:
            with self.get_connection() as conn:
                sql = """
                INSERT INTO collection_logs 
                (collection_time, source_url, records_collected, success, error_message, response_time)
                VALUES (?, ?, ?, ?, ?, ?)
                """
                conn.execute(sql, (
                    log.collection_time, log.source_url, log.records_collected,
                    log.success, log.error_message, log.response_time
                ))
                conn.commit()
                return True
        except Exception as e:
            print(f"插入日志失败: {e}")
            return False
    
    def get_latest_issue(self) -> Optional[str]:
        """获取最新期号"""
        try:
            with self.get_connection() as conn:
                cursor = conn.execute(
                    "SELECT issue FROM lottery_data ORDER BY draw_date DESC, issue DESC LIMIT 1"
                )
                result = cursor.fetchone()
                return result['issue'] if result else None
        except Exception as e:
            print(f"获取最新期号失败: {e}")
            return None
    
    def get_data_count(self) -> int:
        """获取数据总数"""
        try:
            with self.get_connection() as conn:
                cursor = conn.execute("SELECT COUNT(*) as count FROM lottery_data")
                result = cursor.fetchone()
                return result['count'] if result else 0
        except Exception as e:
            print(f"获取数据总数失败: {e}")
            return 0
    
    def get_data_by_date_range(self, start_date: str, end_date: str) -> List[Dict]:
        """按日期范围获取数据"""
        try:
            with self.get_connection() as conn:
                cursor = conn.execute(
                    "SELECT * FROM lottery_data WHERE draw_date BETWEEN ? AND ? ORDER BY draw_date",
                    (start_date, end_date)
                )
                return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            print(f"按日期范围获取数据失败: {e}")
            return []


# 全局数据库管理器实例
db_manager = DatabaseManager()


if __name__ == "__main__":
    # 测试数据库模型
    print("=== 数据库模型测试 ===")
    
    # 测试数据模型
    test_data = LotteryData(
        issue="2024001",
        draw_date="2024-01-01",
        hundreds=1,
        tens=2,
        units=3,
        trial_hundreds=4,
        trial_tens=5,
        trial_units=6,
        machine_number="M001",
        sales_amount=1000000.0,
        prize_info="一等奖1注"
    )
    
    print(f"测试数据: {test_data}")
    print(f"和值: {test_data.sum_value}")
    print(f"跨度: {test_data.span}")
    print(f"号码类型: {test_data.number_type}")
    
    # 测试数据库初始化
    import os
    os.makedirs("data", exist_ok=True)
    
    if db_manager.initialize_database():
        print("✅ 数据库初始化成功")
        
        # 测试插入数据
        if db_manager.insert_lottery_data(test_data):
            print("✅ 数据插入成功")
            print(f"数据总数: {db_manager.get_data_count()}")
        else:
            print("❌ 数据插入失败")
    else:
        print("❌ 数据库初始化失败")
